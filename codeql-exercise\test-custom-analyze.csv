"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/app.js","16","17","16","40"
"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Sensitive value exposed in function call: 'The custom vulnerability pattern ""I am hacker's paradise"" appears in:'","/test-runner.js","103","13","103","84"
"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","6","17","6","40"
"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","7","21","7","44"
"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","10","21","10","44"
"Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values like ""I am hacker's paradise""","error","Hardcoded sensitive value in variable declaration: 'I AM HACKER'S PARADISE'","/test-vulnerabilities.js","11","20","11","43"
