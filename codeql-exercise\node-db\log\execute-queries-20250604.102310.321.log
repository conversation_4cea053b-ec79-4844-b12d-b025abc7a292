[2025-06-04 10:23:10] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --output=E:\advance_javascript\codeQL\8\codeql-exercise\filtered-results.bqrs -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:23:10] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:23:11] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 10:23:11] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\filtered-vulnerability.ql"
                      ]
[2025-06-04 10:23:11] Refusing fancy output: The terminal is not an xterm: 
[2025-06-04 10:23:11] Creating executor with 1 threads.
[2025-06-04 10:23:11] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:23:11] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:23:11] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 10:23:11] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise"
                      ]
[2025-06-04 10:23:11] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 10:23:11] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 10:23:11] [SPAMMY] resolve extensions-by-pack> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 1] security-queries: 1.0.0
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 10:23:12] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 10:23:12] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-04 10:23:12] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-04 10:23:13] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-04 10:23:13] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-04 10:23:14] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-04 10:23:14] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-04 10:23:14] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 10:23:14] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 10:23:15] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql --format=json
[2025-06-04 10:23:15] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:23:15] [DETAILS] resolve library-path> Found enclosing pack 'security-queries' at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 10:23:16] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 10:23:16] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml.
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 1] security-queries: 1.0.0
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 10:23:16] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 10:23:16] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise resolved OK.
[2025-06-04 10:23:16] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-04 10:23:16] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "security-queries\\queries\\filtered-vulnerability.ql",
                        "qlPackName" : "security-queries"
                      }
[2025-06-04 10:23:16] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:23:16] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:23:18] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql hashes to cca91c3800da364bfea0041d80d5175f.
[2025-06-04 10:23:18] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:23:18] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 10:23:19] ExternalModuleBindingPass ...
[2025-06-04 10:23:21] ExternalModuleBindingPass time: 00:02.417
[2025-06-04 10:23:21] CollectInstantiationsPass ...
[2025-06-04 10:23:23] CollectInstantiationsPass time: 00:01.297
[2025-06-04 10:23:23] Ql checks ...
[2025-06-04 10:23:35] Ql checks time: 00:12.337
[2025-06-04 10:23:52] Compilation pipeline
[2025-06-04 10:23:53] Type Inference ...
[2025-06-04 10:23:54] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-04 10:23:54] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:54] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 10:23:54] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,232,360
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,695,2112
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 10:23:55] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1014,46301
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,14,14
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,26,26
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,12,15
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,22,32
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,22,22
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,16,18
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,7,10
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 10:23:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 10:24:00] Type Inference time: 00:06.799
[2025-06-04 10:24:06] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:24:07] Processing compilation pipeline
[2025-06-04 10:24:07] Started compiling a query
[2025-06-04 10:24:08] Initialising compiler...
[2025-06-04 10:24:08] 	 ... compiler initialised
[2025-06-04 10:24:08] About to start query optimisation
[2025-06-04 10:24:10] Compilation cache hit - skipping compilation.
[2025-06-04 10:24:10] Compilation cache hit - skipping compilation.
[2025-06-04 10:24:10] Compilation cache hit - skipping compilation.
[2025-06-04 10:24:11] Compilation cache miss for 4f15a477ec475a5232faa8a2d2a61454.
[2025-06-04 10:24:17] Stored compiled program for 4f15a477ec475a5232faa8a2d2a61454.
[2025-06-04 10:24:18] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-04 10:24:18] CSV_COMPILATION: 705,1602,993,5509
[2025-06-04 10:24:18] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:24:18] [PROGRESS] execute queries> [1/1 comp 1m2s] Compiled E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql.
[2025-06-04 10:24:18] [PROGRESS] execute queries> Starting evaluation of security-queries\queries\filtered-vulnerability.ql.
[2025-06-04 10:24:18] Starting evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:24:18] (0s) Start query execution
[2025-06-04 10:24:18] (0s) Beginning execution of E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql
[2025-06-04 10:24:18] (0s)  >>> Full cache hit cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 385 rows and digest 1999b8doaea4airlo4e9tr0a623.
[2025-06-04 10:24:18] (0s)  >>> Full cache hit cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 192887 rows and digest bf1ab4ujhkf0don2emb08cu4701.
[2025-06-04 10:24:18] (0s)  >>> Full cache hit cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 237718 rows and digest e2e365km2cv53omf8vjemqdbtr1.
[2025-06-04 10:24:18] (0s)  >>> Full cache hit cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 with 76021 rows and digest 978f95qfnc1amiq09gqe53hk4k3.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j with 55376 rows and digest 6f6f02aupa0djiruh6s5gbgfrt6.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre with 108660 rows and digest 15f8bc8s4t79u5lv8seojntm508.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s with 75908 rows and digest f21e59taour23ovre1f9u53nu74.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 with 3906 rows and digest 3444e3v02bl020ava9qii82e3c3.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me with 61500 rows and digest 1d4de0tjs28ikf4osuddegn1ppe.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 with 147 rows and digest 9221c9o1tle1tvjggqsraki2ve9.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air with empty relation.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 with empty relation.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 with 34962 rows and digest fea75dtn6e02l4jtpj0fir9mba6.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 with 447 rows and digest 43325c7h18ff672hcvcd7qm0tkd.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::ConstantString#e55e40cc/1@43d594jb with 147 rows and digest 607b8ehoc6an67h29jl5uhfdfl7.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 with empty relation.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 with empty relation.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn with empty relation.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv with empty relation.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 with 5 rows and digest b418cdppe5vhu6uicbdbvhi2d71.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go with 289 rows and digest 681059jinboal2khcnn5qgtb7p2.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t with empty relation.
[2025-06-04 10:24:19] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t with empty relation.
[2025-06-04 10:24:19] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 10:24:20] (1s)  >>> Created relation exprs/5@a1831agj with 61500 rows and digest f83d74t85c4ldl9dpg3028d06p5.
[2025-06-04 10:24:20] (1s)  >>> Created relation properties/5@1908b117 with 132 rows and digest bab3982mukhq450ff251k976gd7.
[2025-06-04 10:24:20] (1s)  >>> Created relation typeexprs/5@5ef91d9v with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 10:24:20] (1s) Inferred that typeexprs_0#antijoin_rhs/1@e1d4c0cm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 10:24:20] (1s) Inferred that typeexprs_10#join_rhs/2@af1f3114 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 10:24:20] (1s)  >>> Created relation toplevels/2@39c77di1 with 113 rows and digest 931b7e65jp09m7cr3bf3crcrp6c.
[2025-06-04 10:24:20] (1s)  >>> Created relation stmts/5@bcd8ccid with 14276 rows and digest ef40790u9odu79q3hd2u9u8ntr4.
[2025-06-04 10:24:20] (1s) Starting to evaluate predicate exprs_0#antijoin_rhs/1@d222edj4
[2025-06-04 10:24:20] (1s)  >>> Created relation exprs_0#antijoin_rhs/1@d222edj4 with 61500 rows and digest 6dc383aq5t2h0sumsk6vbc39u36.
[2025-06-04 10:24:20] (1s) Starting to evaluate predicate AST::AstNode#17d81be7#b/1@700579mu
[2025-06-04 10:24:20] (1s)  >>> Created relation AST::AstNode#17d81be7#b/1@700579mu with 76021 rows and digest 727693m0i5t04rnbu76ne5fk5fb.
[2025-06-04 10:24:20] (1s) Starting to evaluate predicate AST::AstNode.getFile/0#dispred#dec40f60#bf/2@646564h0
[2025-06-04 10:24:20] (2s)  >>> Created relation AST::AstNode.getFile/0#dispred#dec40f60#bf/2@646564h0 with 76021 rows and digest a3a28fh0jiauo547b4rih31l0pc.
[2025-06-04 10:24:20] (2s)  >>> Created relation files/2@ec93749g with 115 rows and digest 1da27am6grhuehngssp5m5htnd9.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b_021#join_rhs/3@9a865792
[2025-06-04 10:24:20] (2s)  >>> Created relation Files::Container.splitAbsolutePath/2#dispred#43b82b7b_021#join_rhs/3@9a865792 with 385 rows and digest 9f766d974e4t7cto5mqqp3nf5j4.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate Files::Container.getBaseName/0#dispred#2cd5e2b5/2@f8191cuc
[2025-06-04 10:24:20] (2s)  >>> Created relation Files::Container.getBaseName/0#dispred#2cd5e2b5/2@f8191cuc with 115 rows and digest 62587as200n6ah16fkb6k52hsud.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5#shared/2@561870pt
[2025-06-04 10:24:20] (2s)  >>> Created relation _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5#shared/2@561870pt with 76021 rows and digest f2a8a7h39f62n3f4ai05svun374.
[2025-06-04 10:24:20] (2s)  >>> Created relation literals/3@f05366h0 with 35271 rows and digest abee41aghqnk1iifnu6cd40i64c.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate exprs_10#join_rhs/2@27dbbd1m
[2025-06-04 10:24:20] (2s)  >>> Created relation exprs_10#join_rhs/2@27dbbd1m with 61500 rows and digest 6da27335elt5b4ik9drf3iig65c.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate Expr::Literal#62045a1b/1@81201fg9
[2025-06-04 10:24:20] (2s)  >>> Created relation Expr::Literal#62045a1b/1@81201fg9 with 296 rows and digest 8c8b4e1u6vkn0co1nnlbqs4p75d.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate Expr::Literal.getValue/0#dispred#c75e715e/2@19797dqp
[2025-06-04 10:24:20] (2s)  >>> Created relation Expr::Literal.getValue/0#dispred#c75e715e/2@19797dqp with 296 rows and digest 6a513al0b7tpveio3enc8kfubp5.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate filteredvulnerability::isSensitiveString/1#1903220d/1@177a37nh
[2025-06-04 10:24:20] (2s)  >>> Created relation filteredvulnerability::isSensitiveString/1#1903220d/1@177a37nh with 8 rows and digest 64ee72i6ut8ma8cqafhvhj8al48.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5__AST::As__#antijoin_rhs/2@447a5e54
[2025-06-04 10:24:20] (2s)  >>> Created relation _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5__AST::As__#antijoin_rhs/2@447a5e54 with 1 rows and digest e73aa2apcv7s60oeqn51hssggoa.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5#shared___#shared/2@33b75f3s
[2025-06-04 10:24:20] (2s)  >>> Created relation __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5#shared___#shared/2@33b75f3s with 7 rows and digest d8ab03r1kb07rsc3uthd3vfck90.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _Expr::Literal.getValue/0#dispred#c75e715e#antijoin_rhs/1@b0eb85p6
[2025-06-04 10:24:20] (2s)  >>> Created relation _Expr::Literal.getValue/0#dispred#c75e715e#antijoin_rhs/1@b0eb85p6 with 2 rows and digest 502784oivvqbvlunr2l0o7tfq36.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::A__#antijoin_rhs#1/2@3143c1np
[2025-06-04 10:24:20] (2s)  >>> Created relation _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::A__#antijoin_rhs#1/2@3143c1np with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::__#shared/2@f7565f92
[2025-06-04 10:24:20] (2s)  >>> Created relation __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::__#shared/2@f7565f92 with 5 rows and digest 200d2cvujord92un2vsalac5aj2.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5_filtered__#antijoin_rhs/1@888febo7
[2025-06-04 10:24:20] (2s)  >>> Created relation _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5_filtered__#antijoin_rhs/1@888febo7 with 1 rows and digest c06202dkr8dp7mibihgjfuf25p9.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5_filtere__#shared/1@b04bfcik
[2025-06-04 10:24:20] (2s)  >>> Created relation __AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5_filtere__#shared/1@b04bfcik with 7 rows and digest 6420fb9330d827p00811tlep8ia.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::A__#antijoin_rhs/1@c90ac2j5
[2025-06-04 10:24:20] (2s)  >>> Created relation _AST::AstNode.getFile/0#dispred#dec40f60#bf_Files::Container.getBaseName/0#dispred#2cd5e2b5___AST::A__#antijoin_rhs/1@c90ac2j5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate m#Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/1@9ca49d30
[2025-06-04 10:24:20] (2s)  >>> Created relation m#Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/1@9ca49d30 with 5 rows and digest ecffe7hofuf0pvbcl6amnlgrob9.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate Expr::InvokeExpr#b783e907#b/1@fb80e375
[2025-06-04 10:24:20] (2s)  >>> Created relation Expr::InvokeExpr#b783e907#b/1@fb80e375 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 10:24:20] (2s) Inferred that Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/2@c50be099 is empty, due to Expr::InvokeExpr#b783e907#b/1@fb80e375.
[2025-06-04 10:24:20] (2s) Inferred that Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs/2@c246639b is empty, due to Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/2@c50be099.
[2025-06-04 10:24:20] (2s) Inferred that _Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs___Expr::Assignment.getRhs/0#dispre__#antijoin_rhs/2@ac6b6a4o is empty, due to Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs/2@c246639b.
[2025-06-04 10:24:20] (2s) Starting to evaluate predicate exprs_230#join_rhs/3@dfee24vq
[2025-06-04 10:24:21] (2s)  >>> Created relation exprs_230#join_rhs/3@dfee24vq with 61500 rows and digest 121fe0ao19alrk2qhpb5jmfd474.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Expr::Assignment#b84ed089/1@16fa64dv
[2025-06-04 10:24:21] (2s)  >>> Created relation Expr::Assignment#b84ed089/1@16fa64dv with 4035 rows and digest 0ba380natrj0vi7dd9nomsk7go4.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e
[2025-06-04 10:24:21] (2s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e with 4035 rows and digest 0483269apge48a8mhobr9ok3vm8.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@0b6c7fvm
[2025-06-04 10:24:21] (2s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@0b6c7fvm with 4035 rows and digest 62d161o0m9dsatncptk9c31lqpc.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#06e2b126/2@454fa2a6
[2025-06-04 10:24:21] (2s)  >>> Created relation Variables::VariableDeclarator.getInit/0#06e2b126/2@454fa2a6 with 163 rows and digest 9c49705gs6p4iv040lqjjkr3aj8.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate @varref#b/1@ac5ae271
[2025-06-04 10:24:21] (2s)  >>> Created relation @varref#b/1@ac5ae271 with 16300 rows and digest ed34d8jls3stle01soc4up906c5.
[2025-06-04 10:24:21] (2s)  >>> Created relation comments/5@047a73l1 with 10629 rows and digest d1135dre6j64r0gd0f27qpi4gl4.
[2025-06-04 10:24:21] (2s)  >>> Created relation jsdoc/3@a172dbu7 with 9175 rows and digest d621f15eoa1c48gqem08u7ed368.
[2025-06-04 10:24:21] (2s)  >>> Created relation jsdoc_tags/5@1637af13 with 18122 rows and digest a40d3frdjcanr2e8ks2nstfe07a.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate jsdoc_tags_12#join_rhs/2@f0316em6
[2025-06-04 10:24:21] (2s)  >>> Created relation jsdoc_tags_12#join_rhs/2@f0316em6 with 15744 rows and digest 2fc8e4osfom1p0fp5a2qkap1vqa.
[2025-06-04 10:24:21] (2s)  >>> Created relation is_externs/1@68886e91 with 110 rows and digest 63269arci3euoe8kacmbrjubg67.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate AST::TopLevel.isExterns/0#dispred#6ecfdfe6/1@52be5e5o
[2025-06-04 10:24:21] (2s)  >>> Created relation AST::TopLevel.isExterns/0#dispred#6ecfdfe6/1@52be5e5o with 110 rows and digest 63269arci3euoe8kacmbrjubg67.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s
[2025-06-04 10:24:21] (2s)  >>> Created relation CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate stmts_10#join_rhs/2@71c68aq8
[2025-06-04 10:24:21] (2s)  >>> Created relation stmts_10#join_rhs/2@71c68aq8 with 14276 rows and digest b9a0380171luskbthbsaf8nihg7.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Stmt::DeclStmt#28398b2c/1@fb8d64fs
[2025-06-04 10:24:21] (2s)  >>> Created relation Stmt::DeclStmt#28398b2c/1@fb8d64fs with 290 rows and digest 7145315gffie3cfcegkst59912e.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate exprs_12034#join_rhs/5@d7abdd0t
[2025-06-04 10:24:21] (2s)  >>> Created relation exprs_12034#join_rhs/5@d7abdd0t with 61500 rows and digest 460adak2j5nagtfpsn5tvg98stb.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Variables::VariableDeclarator.getDeclStmt/0#dispred#44c06fc8/2@d523fd37
[2025-06-04 10:24:21] (2s)  >>> Created relation Variables::VariableDeclarator.getDeclStmt/0#dispred#44c06fc8/2@d523fd37 with 290 rows and digest 868a90svl67hj1ug0npi7hutktd.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Externs::hasTypedefAnnotation/1#626d2238#b/1@809e2c45
[2025-06-04 10:24:21] (2s)  >>> Created relation Externs::hasTypedefAnnotation/1#626d2238#b/1@809e2c45 with 82 rows and digest 286847r55fumfhqkqbjond81dr8.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate _Externs::hasTypedefAnnotation/1#626d2238#b_Variables::VariableDeclarator.getDeclStmt/0#dispred#44c0__#antijoin_rhs/1@9a5a49v2
[2025-06-04 10:24:21] (2s)  >>> Created relation _Externs::hasTypedefAnnotation/1#626d2238#b_Variables::VariableDeclarator.getDeclStmt/0#dispred#44c0__#antijoin_rhs/1@9a5a49v2 with 82 rows and digest c094bf1mnfnq1783al1t7uernd9.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Externs::ExternalGlobalVarDecl#c56ad568/1@32f410bc
[2025-06-04 10:24:21] (2s)  >>> Created relation Externs::ExternalGlobalVarDecl#c56ad568/1@32f410bc with 154 rows and digest 894951r3o215e6asn54jl8oj4v2.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#dispred#92991ad2/2@61cbe2uh
[2025-06-04 10:24:21] (2s)  >>> Created relation Variables::VariableDeclarator.getInit/0#dispred#92991ad2/2@61cbe2uh with 163 rows and digest 9c49705gs6p4iv040lqjjkr3aj8.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs/1@cec296c0
[2025-06-04 10:24:21] (2s)  >>> Created relation Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs/1@cec296c0 with 163 rows and digest 695802p2d7t9j89qrg3174cq4g9.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs___AST::AstNode.getFile/0#dispre__#shared/2@0c6152t4
[2025-06-04 10:24:21] (2s)  >>> Created relation _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs___AST::AstNode.getFile/0#dispre__#shared/2@0c6152t4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 10:24:21] (2s) Inferred that _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#dis__#antijoin_rhs/2@aee4b54d is empty, due to _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs___AST::AstNode.getFile/0#dispre__#shared/2@0c6152t4.
[2025-06-04 10:24:21] (2s) Inferred that __Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#di__#shared/2@313ca0dj is empty, due to _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#join_rhs___AST::AstNode.getFile/0#dispre__#shared/2@0c6152t4.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate #select/2@fc4fbd1p
[2025-06-04 10:24:21] (2s)  >>> Created relation #select/2@fc4fbd1p with 5 rows and digest 7feb4br0i08lckr5fl9omqvva56.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate _#select_exprs_exprs_0#antijoin_rhs#shared/3@da7b90l8
[2025-06-04 10:24:21] (2s)  >>> Created relation _#select_exprs_exprs_0#antijoin_rhs#shared/3@da7b90l8 with 5 rows and digest c10edenlv5u09v4j548b4prtk3c.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a/6@1a6e1eqp
[2025-06-04 10:24:21] (2s)  >>> Created relation Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a/6@1a6e1eqp with 192887 rows and digest 0189e2oo353qnqtjra2vslj88ka.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@1de7d7ot
[2025-06-04 10:24:21] (2s)  >>> Created relation _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@1de7d7ot with 5 rows and digest 394df3adorrqdnqatraepumubt7.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a__Locations::getLocatableLocation/1#b180d12__#antijoin_rhs/3@377ff585
[2025-06-04 10:24:21] (2s)  >>> Created relation _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a__Locations::getLocatableLocation/1#b180d12__#antijoin_rhs/3@377ff585 with 5 rows and digest c10edenlv5u09v4j548b4prtk3c.
[2025-06-04 10:24:21] (2s) Starting to evaluate predicate #select#query/8@bf5b23cv
[2025-06-04 10:24:21] (2s)  >>> Created relation #select#query/8@bf5b23cv with 5 rows and digest 3b7845jn30hkg4clr1dc6p6rs9f.
[2025-06-04 10:24:21] (2s) Query done
[2025-06-04 10:24:21] (2s) Sequence stamp origin is -6041933254487809345
[2025-06-04 10:24:21] (2s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-04 10:24:21] (2s) Unpausing evaluation
[2025-06-04 10:24:21] Evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\filtered-vulnerability.ql produced BQRS results.
[2025-06-04 10:24:21] [PROGRESS] execute queries> [1/1 eval 2.8s] Evaluation done; writing results to E:\advance_javascript\codeQL\8\codeql-exercise\filtered-results.bqrs.
[2025-06-04 10:24:21] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-04 10:24:21] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-04 10:24:21] The disk cache is freshly trimmed; leave it be.
[2025-06-04 10:24:21] Unpausing evaluation
[2025-06-04 10:24:21] Exiting with code 0
