[2025-06-04 12:52:22] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\results -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript queries/command-injection.ql
[2025-06-04 12:52:22] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- queries/command-injection.ql
[2025-06-04 12:52:22] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:22] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\command-injection.ql"
                      ]
[2025-06-04 12:52:22] Refusing fancy output: Cannot detect terminal size on this platform
[2025-06-04 12:52:22] Creating executor with 1 threads.
[2025-06-04 12:52:23] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations queries/command-injection.ql
[2025-06-04 12:52:23] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- queries/command-injection.ql
[2025-06-04 12:52:23] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:23] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                      ]
[2025-06-04 12:52:23] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise\queries
[2025-06-04 12:52:23] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 12:52:23] [SPAMMY] resolve extensions-by-pack> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 12:52:23] [SPAMMY] resolve extensions-by-pack> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 12:52:23] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 12:52:23] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 12:52:23] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql --format=json
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Found no pack; trying after symlink resolution with E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Found enclosing pack 'queries' at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] [SPAMMY] resolve library-path> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 12:52:23] [SPAMMY] resolve library-path> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 12:52:23] [SPAMMY] resolve library-path> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 12:52:23] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise\queries resolved OK.
[2025-06-04 12:52:23] [DETAILS] resolve library-path> Found no dbscheme through dependencies.
[2025-06-04 12:52:23] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                        ],
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "queries\\command-injection.ql",
                        "possibleAdvice" : "There should probably be a qlpack.yml file declaring dependencies in E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries or an enclosing directory.",
                        "qlPackName" : "queries"
                      }
[2025-06-04 12:52:23] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql hashes to ad169e1a05c72e6fe92b655543f5a745.
[2025-06-04 12:52:23] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 12:52:23] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 12:52:24] ExternalModuleBindingPass ...
[2025-06-04 12:52:24] ExternalModuleBindingPass time: 00:00.002
[2025-06-04 12:52:24] CollectInstantiationsPass ...
[2025-06-04 12:52:24] CollectInstantiationsPass time: 00:00.005
[2025-06-04 12:52:24] Ql checks ...
[2025-06-04 12:52:24] Ql checks time: 00:00.055
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module javascript (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:13,8-18)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:18,25-33)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type CallExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:19,10-18)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type CallExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:29,10-18)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:31,12-22)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:46,29-37)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:48,10-20)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:49,21-31)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:53,10-20)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:54,21-31)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:58,10-20)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:59,21-31)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:67,33-41)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:67,54-62)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type AddExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:68,10-17)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:71,12-25)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type TemplateLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:77,10-25)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type TemplateElement (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:80,12-27)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:88,6-14)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:88,29-37)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:100,12-20)
[2025-06-04 12:52:24] [ERROR] execute queries> ERROR: could not resolve type AddExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql:111,12-19)
[2025-06-04 12:52:24] Sequence stamp origin is -6041894807349754578
[2025-06-04 12:52:24] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-04 12:52:24] The disk cache is freshly trimmed; leave it be.
[2025-06-04 12:52:24] Unpausing evaluation
[2025-06-04 12:52:24] Exiting with code 2
