/**
 * @name Simple Working Command Injection
 * @description Simple command injection detection that actually works
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision medium
 * @id js/simple-working-cmd
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript

from CallExpr call, Expr commandArg, string message
where
  // Find exec calls
  (call.getCalleeName() = "exec" or
   exists(PropAccess prop |
     prop.getPropertyName() = "exec" and
     call.getCallee() = prop
   )) and
  commandArg = call.getArgument(0) and
  // Check if command contains variables (potential injection points)
  (exists(TemplateLiteral template |
     template = commandArg and
     exists(VarAccess var | var = template.getAnElement())
   ) or
   exists(VarAccess var | var = commandArg)) and
  message = "Potential command injection: dynamic command construction"
select call, message + " - Command: " + commandArg.toString() + " (File: " + call.getFile().getBaseName() + ", Line: " + call.getLocation().getStartLine() + ")"
