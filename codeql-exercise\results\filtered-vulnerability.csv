"Filtered Vulnerability Detection","Detects vulnerabilities but excludes test-runner.js","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise' (File: app.js, Line: 16)","/app.js","16","17","16","40"
"Filtered Vulnerability Detection","Detects vulnerabilities but excludes test-runner.js","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise' (File: test-vulnerabilities.js, Line: 6)","/test-vulnerabilities.js","6","17","6","40"
"Filtered Vulnerability Detection","Detects vulnerabilities but excludes test-runner.js","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise' (File: test-vulnerabilities.js, Line: 7)","/test-vulnerabilities.js","7","21","7","44"
"Filtered Vulnerability Detection","Detects vulnerabilities but excludes test-runner.js","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise' (File: test-vulnerabilities.js, Line: 10)","/test-vulnerabilities.js","10","21","10","44"
"Filtered Vulnerability Detection","Detects vulnerabilities but excludes test-runner.js","error","Hardcoded sensitive value in variable declaration: 'I AM HACKER'S PARADISE' (File: test-vulnerabilities.js, Line: 11)","/test-vulnerabilities.js","11","20","11","43"
