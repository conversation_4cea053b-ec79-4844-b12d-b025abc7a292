#!/bin/bash

# Validation script to confirm the exercise works correctly
# This script runs both queries and compares results to validate false positive handling

echo "=== CodeQL Exercise Validation ==="
echo "This script validates that the refined query correctly handles false positives"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Ensure database exists
if [ ! -d "codeql-db" ]; then
    echo -e "${RED}CodeQL database not found. Run setup-codeql.sh first.${NC}"
    exit 1
fi

echo -e "${BLUE}Step 1: Running initial custom vulnerability query...${NC}"
codeql query run queries/custom-vulnerability.ql --database=codeql-db --output=initial-results.bqrs

echo -e "${BLUE}Step 2: Running refined custom vulnerability query...${NC}"
codeql query run queries/refined-custom-vulnerability.ql --database=codeql-db --output=refined-results.bqrs

echo -e "${BLUE}Step 3: Comparing results...${NC}"

# Count results from both queries
initial_count=$(codeql bqrs decode --format=csv initial-results.bqrs 2>/dev/null | wc -l)
refined_count=$(codeql bqrs decode --format=csv refined-results.bqrs 2>/dev/null | wc -l)

# Subtract 1 for header row if results exist
if [ $initial_count -gt 0 ]; then
    initial_count=$((initial_count - 1))
fi
if [ $refined_count -gt 0 ]; then
    refined_count=$((refined_count - 1))
fi

echo "Initial query results: $initial_count"
echo "Refined query results: $refined_count"

echo -e "${YELLOW}=== Initial Query Results ===${NC}"
codeql bqrs decode --format=table initial-results.bqrs 2>/dev/null || echo "No results"

echo -e "${YELLOW}=== Refined Query Results ===${NC}"
codeql bqrs decode --format=table refined-results.bqrs 2>/dev/null || echo "No results"

echo -e "${BLUE}=== Validation Analysis ===${NC}"

# Expected behavior: refined query should have fewer or equal results (due to false positive filtering)
if [ $refined_count -le $initial_count ]; then
    echo -e "${GREEN}✓ PASS: Refined query has $refined_count results vs initial query's $initial_count results${NC}"
    echo -e "${GREEN}✓ This suggests false positive filtering is working${NC}"
else
    echo -e "${RED}✗ UNEXPECTED: Refined query has more results than initial query${NC}"
fi

# Check if we still detect the main vulnerability
echo -e "${BLUE}Step 4: Checking for main vulnerability detection...${NC}"
main_vuln_detected=$(codeql bqrs decode --format=csv refined-results.bqrs 2>/dev/null | grep -i "hacker.*paradise" | wc -l)

if [ $main_vuln_detected -gt 0 ]; then
    echo -e "${GREEN}✓ PASS: Main vulnerability (VAL_VAR) still detected by refined query${NC}"
else
    echo -e "${RED}✗ FAIL: Main vulnerability not detected by refined query${NC}"
fi

# Summary
echo -e "${CYAN}=== Validation Summary ===${NC}"
echo "1. Initial query found: $initial_count potential issues"
echo "2. Refined query found: $refined_count potential issues"
echo "3. Main vulnerability detected: $([ $main_vuln_detected -gt 0 ] && echo "Yes" || echo "No")"

if [ $refined_count -le $initial_count ] && [ $main_vuln_detected -gt 0 ]; then
    echo -e "${GREEN}🎉 EXERCISE VALIDATION SUCCESSFUL!${NC}"
    echo -e "${GREEN}The refined query successfully reduces false positives while maintaining detection of real vulnerabilities.${NC}"
else
    echo -e "${YELLOW}⚠ Exercise needs review - check query logic and test cases${NC}"
fi

echo -e "${BLUE}=== Files Generated ===${NC}"
echo "- initial-results.bqrs (original query results)"
echo "- refined-results.bqrs (refined query results)"
echo ""
echo "Use 'codeql bqrs decode --format=table <file>' to view detailed results"
