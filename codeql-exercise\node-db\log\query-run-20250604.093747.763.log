[2025-06-04 09:37:47] This is codeql query run queries/custom-vulnerability.ql --database=node-db --output=custom-results.bqrs
[2025-06-04 09:37:47] Log file was started late.
[2025-06-04 09:37:47] Calling plumbing command: codeql resolve ram --dataset=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript --format=json
[2025-06-04 09:37:47] [PROGRESS] resolve ram> Stringpool size measured as 5013930
[2025-06-04 09:37:47] Plumbing command codeql resolve ram completed:
                      [
                        "-J-Xmx1346M"
                      ]
[2025-06-04 09:37:47] Spawning plumbing command: execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --output=E:\advance_javascript\codeQL\8\codeql-exercise\custom-results.bqrs -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:38:21] Plumbing command codeql execute queries terminated with status 0.
[2025-06-04 09:38:21] Exiting with code 0
