/**
 * @name Debug User Input Sources
 * @description Debug query to find user input sources
 * @kind table
 * @id js/debug-user-input
 */

import javascript

from PropAccess prop, File f
where (
  (prop.getBase().(PropAccess).getPropertyName() = "body" or
   prop.getBase().(PropAccess).getPropertyName() = "params" or
   prop.getBase().(PropAccess).getPropertyName() = "query") and
  f = prop.getFile() and
  f.getAbsolutePath().matches("%codeql-exercise%")
)
select f.getBaseName() as filename,
       prop.getLocation().getStartLine() as line_number,
       prop.toString() as user_input_access,
       prop.getBase().(PropAccess).getPropertyName() as input_type
