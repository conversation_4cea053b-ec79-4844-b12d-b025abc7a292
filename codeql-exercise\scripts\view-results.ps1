# PowerShell script to view CodeQL scan results

Write-Host "=== CodeQL Results Viewer ===" -ForegroundColor Cyan

# Function to display BQRS results
function Show-BqrsResults {
    param(
        [string]$FilePath,
        [string]$Title
    )
    
    if (Test-Path $FilePath) {
        Write-Host $Title -ForegroundColor Blue
        Write-Host "----------------------------------------"
        & codeql bqrs decode --format=table $FilePath 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "No results found or error decoding" -ForegroundColor Yellow
        }
        Write-Host ""
    } else {
        Write-Host "⚠ $FilePath not found" -ForegroundColor Yellow
    }
}

# Function to display SARIF summary
function Show-SarifSummary {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        Write-Host "Default Security Scan Results (SARIF Summary)" -ForegroundColor Blue
        Write-Host "----------------------------------------"
        
        # Check if the file contains valid JSON
        try {
            $sarifContent = Get-Content $FilePath -Raw | ConvertFrom-Json
            
            Write-Host "Total rules executed:" -ForegroundColor Green
            Write-Host $sarifContent.runs[0].tool.driver.rules.Count
            
            Write-Host "Total results found:" -ForegroundColor Green
            Write-Host $sarifContent.runs[0].results.Count
            
            Write-Host ""
            Write-Host "Results by severity:" -ForegroundColor Green
            $severities = $sarifContent.runs[0].results | ForEach-Object { 
                if ($_.level) { $_.level } else { "unknown" }
            } | Group-Object | Sort-Object Count -Descending
            
            foreach ($severity in $severities) {
                Write-Host "  $($severity.Name): $($severity.Count)"
            }
            
            Write-Host ""
            Write-Host "Top 5 vulnerability types found:" -ForegroundColor Green
            $ruleIds = $sarifContent.runs[0].results | ForEach-Object { $_.ruleId } | 
                       Group-Object | Sort-Object Count -Descending | Select-Object -First 5
            
            foreach ($rule in $ruleIds) {
                Write-Host "  $($rule.Name): $($rule.Count)"
            }
        }
        catch {
            Write-Host "Unable to parse SARIF file or file is empty" -ForegroundColor Yellow
            Write-Host "File exists: $FilePath"
        }
        Write-Host ""
    } else {
        Write-Host "⚠ $FilePath not found" -ForegroundColor Yellow
    }
}

# Display all results
Write-Host "Displaying CodeQL scan results..." -ForegroundColor Green
Write-Host ""

# 1. Default security scan results (SARIF)
Show-SarifSummary "default-results.sarif"

# 2. Custom vulnerability detection results
Show-BqrsResults "custom-results.bqrs" "Custom Vulnerability Detection Results (Hardcoded Sensitive Values)"

# 3. SQL injection results
Show-BqrsResults "sql-injection-results.bqrs" "SQL Injection Detection Results"

# 4. Command injection results
Show-BqrsResults "command-injection-results.bqrs" "Command Injection Detection Results"

# Additional analysis
Write-Host "=== Analysis Summary ===" -ForegroundColor Cyan
$jsFiles = Get-ChildItem -Path . -Filter "*.js" -Recurse | Where-Object { 
    $_.FullName -notlike "*node_modules*" -and $_.FullName -notlike "*codeql-db*" 
}
Write-Host "Files scanned: $($jsFiles.Count)"

Write-Host ""
Write-Host "Vulnerability categories detected:"
Write-Host "  1. Hardcoded sensitive values (custom query)"
Write-Host "  2. SQL injection vulnerabilities"
Write-Host "  3. Command injection vulnerabilities"
Write-Host "  4. Various other security issues (default queries)"

Write-Host ""
Write-Host "Note: Review the results above to identify:" -ForegroundColor Yellow
Write-Host "  - True positives (actual vulnerabilities)"
Write-Host "  - False positives (legitimate code flagged incorrectly)"
Write-Host "  - Areas where query refinement may be needed"

Write-Host ""
Write-Host "=== Results display completed ===" -ForegroundColor Green
