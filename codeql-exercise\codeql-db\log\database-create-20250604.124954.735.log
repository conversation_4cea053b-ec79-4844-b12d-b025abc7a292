[2025-06-04 12:49:54] This is codeql database create --language=javascript --source-root=. --overwrite codeql-db
[2025-06-04 12:49:54] Log file was started late.
[2025-06-04 12:49:54] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db.
[2025-06-04 12:49:54] Running plumbing command: codeql database init --overwrite --language=javascript --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --source-root=E:\advance_javascript\codeQL\8\codeql-exercise --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db
[2025-06-04 12:49:54] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-04 12:49:55] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-04 12:49:55] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-04 12:49:55] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-04 12:49:55] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 12:49:55] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-04 12:49:55] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-04 12:49:57] Using configure-baseline script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\configure-baseline.cmd.
[2025-06-04 12:49:57] [PROGRESS] database init> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\configure-baseline.cmd]
[2025-06-04 12:49:57] [SPAMMY] database init> Found 2 baseline files for javascript.
[2025-06-04 12:49:57] [PROGRESS] database init> Calculated baseline information for languages: javascript (1.8s).
[2025-06-04 12:49:57] [PROGRESS] database init> Resolving extractor javascript.
[2025-06-04 12:49:57] [DETAILS] database init> Found candidate extractor root for javascript: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-04 12:49:57] [PROGRESS] database init> Successfully loaded extractor JavaScript/TypeScript (javascript) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-04 12:49:57] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db. This in-progress database is ready to be populated by an extractor.
[2025-06-04 12:49:57] Plumbing command codeql database init completed.
[2025-06-04 12:49:57] [PROGRESS] database create> Running build command: []
[2025-06-04 12:49:57] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\8\codeql-exercise --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db
[2025-06-04 12:49:57] Using autobuild script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd.
[2025-06-04 12:49:57] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd]
[2025-06-04 12:49:57] [build-stdout] Single-threaded extraction.
[2025-06-04 12:49:57] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\app.js
[2025-06-04 12:49:58] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\app.js (244 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\package.json
[2025-06-04 12:49:58] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\package.json (9 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-vulnerabilities.js
[2025-06-04 12:49:58] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-vulnerabilities.js (42 ms)
[2025-06-04 12:49:58] [build-stderr] No externs trap cache found
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2016.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2016.js (42 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2017.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2017.js (18 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es3.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es3.js (213 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es5.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es5.js (40 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6.js (147 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6_collections.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6_collections.js (26 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\intl.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\intl.js (28 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\proxy.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\proxy.js (9 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\bdd.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\bdd.js (13 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\jquery-3.2.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\jquery-3.2.js (168 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\should.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\should.js (25 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\vows.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\vows.js (30 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert.js (29 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert_legacy.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert_legacy.js (6 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\buffer.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\buffer.js (21 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\child_process.js
[2025-06-04 12:49:58] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\child_process.js (71 ms)
[2025-06-04 12:49:58] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\cluster.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\cluster.js (114 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\console.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\console.js (9 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\constants.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\constants.js (210 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\crypto.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\crypto.js (88 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dgram.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dgram.js (19 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dns.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dns.js (34 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\domain.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\domain.js (35 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\events.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\events.js (21 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\fs.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\fs.js (217 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\globals.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\globals.js (147 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\http.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\http.js (54 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\https.js
[2025-06-04 12:49:59] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\https.js (20 ms)
[2025-06-04 12:49:59] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\module.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\module.js (17 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\net.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\net.js (75 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\os.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\os.js (20 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\path.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\path.js (27 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\process.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\process.js (7 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\punycode.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\punycode.js (10 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\querystring.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\querystring.js (19 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\readline.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\readline.js (36 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\repl.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\repl.js (43 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\stream.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\stream.js (94 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\string_decoder.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\string_decoder.js (15 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\sys.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\sys.js (18 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\timers.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\timers.js (26 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tls.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tls.js (64 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tty.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tty.js (18 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\url.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\url.js (13 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\util.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\util.js (47 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\v8.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\v8.js (16 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\vm.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\vm.js (31 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\zlib.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\zlib.js (44 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\jsshell.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\jsshell.js (61 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\rhino.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\rhino.js (15 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\spidermonkey.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\spidermonkey.js (7 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\v8.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\v8.js (26 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\chrome.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\chrome.js (51 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fetchapi.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fetchapi.js (31 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fileapi.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fileapi.js (57 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\flash.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\flash.js (15 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_css.js
[2025-06-04 12:50:00] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_css.js (18 ms)
[2025-06-04 12:50:00] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_dom.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_dom.js (45 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_event.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_event.js (13 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_ext.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_ext.js (5 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_xml.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_xml.js (12 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\html5.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\html5.js (218 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_css.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_css.js (18 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_dom.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_dom.js (64 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_event.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_event.js (21 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_vml.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_vml.js (9 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\intl.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\intl.js (16 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\iphone.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\iphone.js (13 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\mediasource.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\mediasource.js (16 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\page_visibility.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\page_visibility.js (6 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\streamsapi.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\streamsapi.js (31 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\url.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\url.js (13 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_anim_timing.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_anim_timing.js (11 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_batterystatus.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_batterystatus.js (10 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css.js (110 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css3d.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css3d.js (14 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_device_sensor_event.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_device_sensor_event.js (12 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom1.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom1.js (65 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom2.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom2.js (145 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom3.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom3.js (58 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom4.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom4.js (11 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_elementtraversal.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_elementtraversal.js (14 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_encoding.js
[2025-06-04 12:50:01] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_encoding.js (14 ms)
[2025-06-04 12:50:01] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event.js (49 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event3.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event3.js (15 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_gamepad.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_gamepad.js (16 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_geolocation.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_geolocation.js (16 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_indexeddb.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_indexeddb.js (57 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_midi.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_midi.js (21 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_navigation_timing.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_navigation_timing.js (27 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_permissions.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_permissions.js (57 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_pointer_events.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_pointer_events.js (36 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_range.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_range.js (23 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_requestidlecallback.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_requestidlecallback.js (11 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_rtc.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_rtc.js (129 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_screen_orientation.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_screen_orientation.js (7 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_selectors.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_selectors.js (11 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_serviceworker.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_serviceworker.js (47 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_touch_event.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_touch_event.js (19 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_webcrypto.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_webcrypto.js (31 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_xml.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_xml.js (58 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webgl.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webgl.js (167 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_css.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_css.js (77 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_dom.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_dom.js (19 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_event.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_event.js (8 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_notifications.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_notifications.js (15 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_usercontent.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_usercontent.js (14 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webstorage.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webstorage.js (15 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\whatwg_encoding.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\whatwg_encoding.js (10 ms)
[2025-06-04 12:50:02] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\window.js
[2025-06-04 12:50:02] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\window.js (14 ms)
[2025-06-04 12:50:03] Plumbing command codeql database trace-command completed.
[2025-06-04 12:50:03] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db.
[2025-06-04 12:50:03] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db
[2025-06-04 12:50:03] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db...
[2025-06-04 12:50:03] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\semmlecode.javascript.dbscheme -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\trap\javascript
[2025-06-04 12:50:03] Clearing disk cache since the version file E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript\default\cache\version does not exist
[2025-06-04 12:50:03] Tuple pool not found. Clearing relations with cached strings
[2025-06-04 12:50:03] Trimming disk cache at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript\default\cache in mode clear.
[2025-06-04 12:50:03] Sequence stamp origin is -6041895406512242860
[2025-06-04 12:50:03] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-04 12:50:03] Unpausing evaluation
[2025-06-04 12:50:03] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-04 12:50:03] Unpausing evaluation
[2025-06-04 12:50:03] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-04 12:50:03] Unpausing evaluation
[2025-06-04 12:50:03] Trimming completed (23ms): Purged everything.
[2025-06-04 12:50:04] Scanning for files in E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\trap\javascript
[2025-06-04 12:50:04] Found 114 TRAP files (8.30 MiB)
[2025-06-04 12:50:04] [PROGRESS] dataset import> Importing TRAP files
[2025-06-04 12:50:04] Importing es2016.js.trap.gz (1 of 114)
[2025-06-04 12:50:04] Importing es2017.js.trap.gz (2 of 114)
[2025-06-04 12:50:04] Importing es3.js.trap.gz (3 of 114)
[2025-06-04 12:50:04] Importing es5.js.trap.gz (4 of 114)
[2025-06-04 12:50:04] Importing es6.js.trap.gz (5 of 114)
[2025-06-04 12:50:04] Importing es6_collections.js.trap.gz (6 of 114)
[2025-06-04 12:50:04] Importing intl.js.trap.gz (7 of 114)
[2025-06-04 12:50:04] Importing proxy.js.trap.gz (8 of 114)
[2025-06-04 12:50:04] Importing bdd.js.trap.gz (9 of 114)
[2025-06-04 12:50:04] Importing jquery-3.2.js.trap.gz (10 of 114)
[2025-06-04 12:50:04] Importing should.js.trap.gz (11 of 114)
[2025-06-04 12:50:04] Importing vows.js.trap.gz (12 of 114)
[2025-06-04 12:50:04] Importing assert.js.trap.gz (13 of 114)
[2025-06-04 12:50:04] Importing assert_legacy.js.trap.gz (14 of 114)
[2025-06-04 12:50:04] Importing buffer.js.trap.gz (15 of 114)
[2025-06-04 12:50:04] Importing child_process.js.trap.gz (16 of 114)
[2025-06-04 12:50:04] Importing cluster.js.trap.gz (17 of 114)
[2025-06-04 12:50:04] Importing console.js.trap.gz (18 of 114)
[2025-06-04 12:50:04] Importing constants.js.trap.gz (19 of 114)
[2025-06-04 12:50:05] Importing crypto.js.trap.gz (20 of 114)
[2025-06-04 12:50:05] Importing dgram.js.trap.gz (21 of 114)
[2025-06-04 12:50:05] Importing dns.js.trap.gz (22 of 114)
[2025-06-04 12:50:05] Importing domain.js.trap.gz (23 of 114)
[2025-06-04 12:50:05] Importing events.js.trap.gz (24 of 114)
[2025-06-04 12:50:05] Importing fs.js.trap.gz (25 of 114)
[2025-06-04 12:50:05] Importing globals.js.trap.gz (26 of 114)
[2025-06-04 12:50:05] Importing http.js.trap.gz (27 of 114)
[2025-06-04 12:50:05] Importing https.js.trap.gz (28 of 114)
[2025-06-04 12:50:05] Importing module.js.trap.gz (29 of 114)
[2025-06-04 12:50:05] Importing net.js.trap.gz (30 of 114)
[2025-06-04 12:50:05] Importing os.js.trap.gz (31 of 114)
[2025-06-04 12:50:05] Importing path.js.trap.gz (32 of 114)
[2025-06-04 12:50:05] Importing process.js.trap.gz (33 of 114)
[2025-06-04 12:50:05] Importing punycode.js.trap.gz (34 of 114)
[2025-06-04 12:50:05] Importing querystring.js.trap.gz (35 of 114)
[2025-06-04 12:50:05] Importing readline.js.trap.gz (36 of 114)
[2025-06-04 12:50:05] Importing repl.js.trap.gz (37 of 114)
[2025-06-04 12:50:05] Importing stream.js.trap.gz (38 of 114)
[2025-06-04 12:50:05] Importing string_decoder.js.trap.gz (39 of 114)
[2025-06-04 12:50:05] Importing sys.js.trap.gz (40 of 114)
[2025-06-04 12:50:05] Importing timers.js.trap.gz (41 of 114)
[2025-06-04 12:50:05] Importing tls.js.trap.gz (42 of 114)
[2025-06-04 12:50:05] Importing tty.js.trap.gz (43 of 114)
[2025-06-04 12:50:05] Importing url.js.trap.gz (44 of 114)
[2025-06-04 12:50:05] Importing util.js.trap.gz (45 of 114)
[2025-06-04 12:50:05] Importing v8.js.trap.gz (46 of 114)
[2025-06-04 12:50:05] Importing vm.js.trap.gz (47 of 114)
[2025-06-04 12:50:05] Importing zlib.js.trap.gz (48 of 114)
[2025-06-04 12:50:05] Importing jsshell.js.trap.gz (49 of 114)
[2025-06-04 12:50:05] Importing rhino.js.trap.gz (50 of 114)
[2025-06-04 12:50:05] Importing spidermonkey.js.trap.gz (51 of 114)
[2025-06-04 12:50:05] Importing v8.js.trap.gz (52 of 114)
[2025-06-04 12:50:05] Importing chrome.js.trap.gz (53 of 114)
[2025-06-04 12:50:05] Importing fetchapi.js.trap.gz (54 of 114)
[2025-06-04 12:50:05] Importing fileapi.js.trap.gz (55 of 114)
[2025-06-04 12:50:05] Importing flash.js.trap.gz (56 of 114)
[2025-06-04 12:50:05] Importing gecko_css.js.trap.gz (57 of 114)
[2025-06-04 12:50:05] Importing gecko_dom.js.trap.gz (58 of 114)
[2025-06-04 12:50:05] Importing gecko_event.js.trap.gz (59 of 114)
[2025-06-04 12:50:05] Importing gecko_ext.js.trap.gz (60 of 114)
[2025-06-04 12:50:05] Importing gecko_xml.js.trap.gz (61 of 114)
[2025-06-04 12:50:05] Importing html5.js.trap.gz (62 of 114)
[2025-06-04 12:50:05] Importing ie_css.js.trap.gz (63 of 114)
[2025-06-04 12:50:05] Importing ie_dom.js.trap.gz (64 of 114)
[2025-06-04 12:50:05] Importing ie_event.js.trap.gz (65 of 114)
[2025-06-04 12:50:05] Importing ie_vml.js.trap.gz (66 of 114)
[2025-06-04 12:50:05] Importing intl.js.trap.gz (67 of 114)
[2025-06-04 12:50:05] Importing iphone.js.trap.gz (68 of 114)
[2025-06-04 12:50:05] Importing mediasource.js.trap.gz (69 of 114)
[2025-06-04 12:50:05] Importing page_visibility.js.trap.gz (70 of 114)
[2025-06-04 12:50:05] Importing streamsapi.js.trap.gz (71 of 114)
[2025-06-04 12:50:05] Importing url.js.trap.gz (72 of 114)
[2025-06-04 12:50:05] Importing w3c_anim_timing.js.trap.gz (73 of 114)
[2025-06-04 12:50:05] Importing w3c_batterystatus.js.trap.gz (74 of 114)
[2025-06-04 12:50:05] Importing w3c_css.js.trap.gz (75 of 114)
[2025-06-04 12:50:06] Importing w3c_css3d.js.trap.gz (76 of 114)
[2025-06-04 12:50:06] Importing w3c_device_sensor_event.js.trap.gz (77 of 114)
[2025-06-04 12:50:06] Importing w3c_dom1.js.trap.gz (78 of 114)
[2025-06-04 12:50:06] Importing w3c_dom2.js.trap.gz (79 of 114)
[2025-06-04 12:50:06] Importing w3c_dom3.js.trap.gz (80 of 114)
[2025-06-04 12:50:06] Importing w3c_dom4.js.trap.gz (81 of 114)
[2025-06-04 12:50:06] Importing w3c_elementtraversal.js.trap.gz (82 of 114)
[2025-06-04 12:50:06] Importing w3c_encoding.js.trap.gz (83 of 114)
[2025-06-04 12:50:06] Importing w3c_event.js.trap.gz (84 of 114)
[2025-06-04 12:50:06] Importing w3c_event3.js.trap.gz (85 of 114)
[2025-06-04 12:50:06] Importing w3c_gamepad.js.trap.gz (86 of 114)
[2025-06-04 12:50:06] Importing w3c_geolocation.js.trap.gz (87 of 114)
[2025-06-04 12:50:06] Importing w3c_indexeddb.js.trap.gz (88 of 114)
[2025-06-04 12:50:06] Importing w3c_midi.js.trap.gz (89 of 114)
[2025-06-04 12:50:06] Importing w3c_navigation_timing.js.trap.gz (90 of 114)
[2025-06-04 12:50:06] Importing w3c_permissions.js.trap.gz (91 of 114)
[2025-06-04 12:50:06] Importing w3c_pointer_events.js.trap.gz (92 of 114)
[2025-06-04 12:50:06] Importing w3c_range.js.trap.gz (93 of 114)
[2025-06-04 12:50:06] Importing w3c_requestidlecallback.js.trap.gz (94 of 114)
[2025-06-04 12:50:06] Importing w3c_rtc.js.trap.gz (95 of 114)
[2025-06-04 12:50:06] Importing w3c_screen_orientation.js.trap.gz (96 of 114)
[2025-06-04 12:50:06] Importing w3c_selectors.js.trap.gz (97 of 114)
[2025-06-04 12:50:06] Importing w3c_serviceworker.js.trap.gz (98 of 114)
[2025-06-04 12:50:06] Importing w3c_touch_event.js.trap.gz (99 of 114)
[2025-06-04 12:50:06] Importing w3c_webcrypto.js.trap.gz (100 of 114)
[2025-06-04 12:50:06] Importing w3c_xml.js.trap.gz (101 of 114)
[2025-06-04 12:50:06] Importing webgl.js.trap.gz (102 of 114)
[2025-06-04 12:50:06] Importing webkit_css.js.trap.gz (103 of 114)
[2025-06-04 12:50:06] Importing webkit_dom.js.trap.gz (104 of 114)
[2025-06-04 12:50:06] Importing webkit_event.js.trap.gz (105 of 114)
[2025-06-04 12:50:06] Importing webkit_notifications.js.trap.gz (106 of 114)
[2025-06-04 12:50:06] Importing webkit_usercontent.js.trap.gz (107 of 114)
[2025-06-04 12:50:06] Importing webstorage.js.trap.gz (108 of 114)
[2025-06-04 12:50:06] Importing whatwg_encoding.js.trap.gz (109 of 114)
[2025-06-04 12:50:06] Importing window.js.trap.gz (110 of 114)
[2025-06-04 12:50:06] Importing app.js.trap.gz (111 of 114)
[2025-06-04 12:50:06] Importing package.json.trap.gz (112 of 114)
[2025-06-04 12:50:06] Importing test-vulnerabilities.js.trap.gz (113 of 114)
[2025-06-04 12:50:06] Importing metadata.trap.gz (114 of 114)
[2025-06-04 12:50:06] [PROGRESS] dataset import> Merging relations
[2025-06-04 12:50:06] Merging 1 fragment for 'files'.
[2025-06-04 12:50:06] Merged 423 bytes for 'files'.
[2025-06-04 12:50:06] Merging 1 fragment for 'folders'.
[2025-06-04 12:50:06] Merged 77 bytes for 'folders'.
[2025-06-04 12:50:06] Merging 1 fragment for 'containerparent'.
[2025-06-04 12:50:06] Merged 321 bytes for 'containerparent'.
[2025-06-04 12:50:06] Merging 2 fragments for 'locations_default'.
[2025-06-04 12:50:06] Merged 816662 bytes (797.52 KiB) for 'locations_default'.
[2025-06-04 12:50:06] Merging 3 fragments for 'hasLocation'.
[2025-06-04 12:50:07] Merged 521404 bytes (509.18 KiB) for 'hasLocation'.
[2025-06-04 12:50:07] Merging 1 fragment for 'extraction_data'.
[2025-06-04 12:50:07] Merged 527 bytes for 'extraction_data'.
[2025-06-04 12:50:07] Merging 1 fragment for 'scopes'.
[2025-06-04 12:50:07] Merged 4216 bytes (4.12 KiB) for 'scopes'.
[2025-06-04 12:50:07] Merging 1 fragment for 'comments'.
[2025-06-04 12:50:07] Merged 37417 bytes (36.54 KiB) for 'comments'.
[2025-06-04 12:50:07] Merging 1 fragment for 'indentation'.
[2025-06-04 12:50:07] Merged 26976 bytes (26.34 KiB) for 'indentation'.
[2025-06-04 12:50:07] Merging 1 fragment for 'numlines'.
[2025-06-04 12:50:07] Merged 1597 bytes (1.56 KiB) for 'numlines'.
[2025-06-04 12:50:07] Merging 1 fragment for 'tokeninfo'.
[2025-06-04 12:50:07] Merged 441492 bytes (431.14 KiB) for 'tokeninfo'.
[2025-06-04 12:50:07] Merging 1 fragment for 'next_token'.
[2025-06-04 12:50:07] Merged 20442 bytes (19.96 KiB) for 'next_token'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc'.
[2025-06-04 12:50:07] Merged 20260 bytes (19.79 KiB) for 'jsdoc'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_tags'.
[2025-06-04 12:50:07] Merged 95396 bytes (93.16 KiB) for 'jsdoc_tags'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_tag_descriptions'.
[2025-06-04 12:50:07] Merged 7318 bytes (7.15 KiB) for 'jsdoc_tag_descriptions'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_tag_names'.
[2025-06-04 12:50:07] Merged 14299 bytes (13.96 KiB) for 'jsdoc_tag_names'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_type_exprs'.
[2025-06-04 12:50:07] Merged 125568 bytes (122.62 KiB) for 'jsdoc_type_exprs'.
[2025-06-04 12:50:07] Merging 1 fragment for 'toplevels'.
[2025-06-04 12:50:07] Merged 236 bytes for 'toplevels'.
[2025-06-04 12:50:07] Merging 1 fragment for 'stmts'.
[2025-06-04 12:50:07] Merged 103030 bytes (100.62 KiB) for 'stmts'.
[2025-06-04 12:50:07] Merging 1 fragment for 'stmt_containers'.
[2025-06-04 12:50:07] Merged 46963 bytes (45.86 KiB) for 'stmt_containers'.
[2025-06-04 12:50:07] Merging 1 fragment for 'exprs'.
[2025-06-04 12:50:07] Merged 414277 bytes (404.57 KiB) for 'exprs'.
[2025-06-04 12:50:07] Merging 1 fragment for 'enclosing_stmt'.
[2025-06-04 12:50:07] Merged 83840 bytes (81.88 KiB) for 'enclosing_stmt'.
[2025-06-04 12:50:07] Merging 1 fragment for 'expr_containers'.
[2025-06-04 12:50:07] Merged 92644 bytes (90.47 KiB) for 'expr_containers'.
[2025-06-04 12:50:07] Merging 1 fragment for 'literals'.
[2025-06-04 12:50:07] Merged 71064 bytes (69.40 KiB) for 'literals'.
[2025-06-04 12:50:07] Merging 1 fragment for 'variables'.
[2025-06-04 12:50:07] Merged 39810 bytes (38.88 KiB) for 'variables'.
[2025-06-04 12:50:07] Merging 1 fragment for 'bind'.
[2025-06-04 12:50:07] Merged 14913 bytes (14.56 KiB) for 'bind'.
[2025-06-04 12:50:07] Merging 1 fragment for 'scopenodes'.
[2025-06-04 12:50:07] Merged 8385 bytes (8.19 KiB) for 'scopenodes'.
[2025-06-04 12:50:07] Merging 1 fragment for 'scopenesting'.
[2025-06-04 12:50:07] Merged 4459 bytes (4.35 KiB) for 'scopenesting'.
[2025-06-04 12:50:07] Merging 1 fragment for 'decl'.
[2025-06-04 12:50:07] Merged 13455 bytes (13.14 KiB) for 'decl'.
[2025-06-04 12:50:07] Merging 1 fragment for 'is_arguments_object'.
[2025-06-04 12:50:07] Merged 4012 bytes (3.92 KiB) for 'is_arguments_object'.
[2025-06-04 12:50:07] Merging 1 fragment for 'entry_cfg_node'.
[2025-06-04 12:50:07] Merged 8892 bytes (8.68 KiB) for 'entry_cfg_node'.
[2025-06-04 12:50:07] Merging 1 fragment for 'exit_cfg_node'.
[2025-06-04 12:50:07] Merged 8892 bytes (8.68 KiB) for 'exit_cfg_node'.
[2025-06-04 12:50:07] Merging 1 fragment for 'successor'.
[2025-06-04 12:50:07] Merged 167029 bytes (163.11 KiB) for 'successor'.
[2025-06-04 12:50:07] Merging 1 fragment for 'is_externs'.
[2025-06-04 12:50:07] Merged 222 bytes for 'is_externs'.
[2025-06-04 12:50:07] Merging 1 fragment for 'filetype'.
[2025-06-04 12:50:07] Merged 243 bytes for 'filetype'.
[2025-06-04 12:50:07] Merging 1 fragment for 'extraction_time'.
[2025-06-04 12:50:07] Merged 14407 bytes (14.07 KiB) for 'extraction_time'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_prefix_qualifier'.
[2025-06-04 12:50:07] Merged 1894 bytes (1.85 KiB) for 'jsdoc_prefix_qualifier'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_errors'.
[2025-06-04 12:50:07] Merged 1139 bytes (1.11 KiB) for 'jsdoc_errors'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_record_field_name'.
[2025-06-04 12:50:07] Merged 1596 bytes (1.56 KiB) for 'jsdoc_record_field_name'.
[2025-06-04 12:50:07] Merging 1 fragment for 'jsdoc_has_new_parameter'.
[2025-06-04 12:50:07] Merged 35 bytes for 'jsdoc_has_new_parameter'.
[2025-06-04 12:50:07] Merging 1 fragment for 'properties'.
[2025-06-04 12:50:07] Merged 555 bytes for 'properties'.
[2025-06-04 12:50:07] Merging 1 fragment for 'regexpterm'.
[2025-06-04 12:50:07] Merged 385 bytes for 'regexpterm'.
[2025-06-04 12:50:07] Merging 1 fragment for 'regexp_const_value'.
[2025-06-04 12:50:07] Merged 237 bytes for 'regexp_const_value'.
[2025-06-04 12:50:07] Merging 1 fragment for 'guard_node'.
[2025-06-04 12:50:07] Merged 161 bytes for 'guard_node'.
[2025-06-04 12:50:07] Merging 1 fragment for 'is_module'.
[2025-06-04 12:50:07] Merged 91 bytes for 'is_module'.
[2025-06-04 12:50:07] Merging 1 fragment for 'is_nodejs'.
[2025-06-04 12:50:07] Merged 91 bytes for 'is_nodejs'.
[2025-06-04 12:50:07] Merging 1 fragment for 'json'.
[2025-06-04 12:50:07] Merged 176 bytes for 'json'.
[2025-06-04 12:50:07] Merging 1 fragment for 'json_locations'.
[2025-06-04 12:50:07] Merged 82 bytes for 'json_locations'.
[2025-06-04 12:50:07] Merging 1 fragment for 'json_literals'.
[2025-06-04 12:50:07] Merged 102 bytes for 'json_literals'.
[2025-06-04 12:50:07] Merging 1 fragment for 'json_properties'.
[2025-06-04 12:50:07] Merged 106 bytes for 'json_properties'.
[2025-06-04 12:50:07] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-04 12:50:07] Merged 19 bytes for 'sourceLocationPrefix'.
[2025-06-04 12:50:07] Saving string and id pools to disk.
[2025-06-04 12:50:07] Finished importing TRAP files.
[2025-06-04 12:50:07] Read 43.91 MiB of uncompressed TRAP data.
[2025-06-04 12:50:07] Uncompressed relation data size: 13.49 MiB
[2025-06-04 12:50:07] Relation data size: 3.09 MiB (merge rate: 5.20 MiB/s)
[2025-06-04 12:50:07] String pool size: 4.78 MiB
[2025-06-04 12:50:07] ID pool size: 13.00 MiB
[2025-06-04 12:50:07] [PROGRESS] dataset import> Finished writing database (relations: 3.09 MiB; string pool: 4.78 MiB).
[2025-06-04 12:50:07] Pausing evaluation to close the cache at sequence stamp o+111
[2025-06-04 12:50:07] The disk cache is freshly trimmed; leave it be.
[2025-06-04 12:50:07] Unpausing evaluation
[2025-06-04 12:50:07] Plumbing command codeql dataset import completed.
[2025-06-04 12:50:07] [PROGRESS] database finalize> TRAP import complete (4.4s).
[2025-06-04 12:50:07] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db
[2025-06-04 12:50:07] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-04 12:50:07] [PROGRESS] database cleanup> TRAP files cleaned up (31ms).
[2025-06-04 12:50:07] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-04 12:50:07] [PROGRESS] database cleanup> Scratch directory cleaned up (1ms).
[2025-06-04 12:50:07] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript
[2025-06-04 12:50:07] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript.
[2025-06-04 12:50:07] Trimming disk cache at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript\default\cache in mode trim.
[2025-06-04 12:50:07] Sequence stamp origin is -6041895387895573113
[2025-06-04 12:50:07] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-04 12:50:07] Unpausing evaluation
[2025-06-04 12:50:07] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-04 12:50:07] Unpausing evaluation
[2025-06-04 12:50:07] Trimming completed (2ms): Trimmed disposable data from cache.
[2025-06-04 12:50:07] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-04 12:50:07] The disk cache is freshly trimmed; leave it be.
[2025-06-04 12:50:07] Unpausing evaluation
[2025-06-04 12:50:07] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-04 12:50:07] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript
[2025-06-04 12:50:07] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript (4ms).
[2025-06-04 12:50:07] Plumbing command codeql dataset cleanup completed.
[2025-06-04 12:50:07] Plumbing command codeql database cleanup completed with status 0.
[2025-06-04 12:50:07] [PROGRESS] database finalize> Finished zipping source archive (252.54 KiB).
[2025-06-04 12:50:07] Plumbing command codeql database finalize completed.
[2025-06-04 12:50:07] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db.
[2025-06-04 12:50:07] Terminating normally.
