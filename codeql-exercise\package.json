{"name": "codeql-vulnerability-exercise", "version": "1.0.0", "description": "CodeQL exercise with multiple vulnerabilities for detection and analysis", "main": "app.js", "scripts": {"start": "node app.js", "test": "node test.js", "codeql-scan": "codeql database create --language=javascript --source-root=. codeql-db && codeql database analyze codeql-db --format=sarif-latest --output=results.sarif", "custom-scan": "codeql query run queries/custom-vulnerability.ql --database=codeql-db --output=custom-results.bqrs", "view-results": "codeql bqrs decode --format=table custom-results.bqrs"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.2", "helmet": "^7.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["codeql", "security", "vulnerability", "nodejs"], "author": "Security Exercise", "license": "MIT"}