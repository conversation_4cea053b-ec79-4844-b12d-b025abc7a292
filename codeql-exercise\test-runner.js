/**
 * Simple test runner to verify the vulnerable application functions
 * This helps confirm that the code is syntactically correct before CodeQL analysis
 */

const testVulns = require('./test-vulnerabilities');

console.log('=== CodeQL Exercise - Application Test Runner ===');
console.log('Testing vulnerable functions to ensure they work before CodeQL analysis...\n');

// Test 1: SQL Injection vulnerability
console.log('1. Testing SQL Injection vulnerability:');
try {
    const sqlResult = testVulns.vulnerableSqlQuery("admin' OR '1'='1");
    console.log('   ✓ SQL injection function works');
    console.log('   Query:', sqlResult);
} catch (error) {
    console.log('   ✗ SQL injection function failed:', error.message);
}

// Test 2: Command injection vulnerability
console.log('\n2. Testing Command Injection vulnerability:');
try {
    const cmdResult = testVulns.vulnerableCommand('test.log; cat /etc/passwd');
    console.log('   ✓ Command injection function works');
} catch (error) {
    console.log('   ✗ Command injection function failed:', error.message);
}

// Test 3: Weak hash function
console.log('\n3. Testing Weak Cryptography:');
try {
    const hashResult = testVulns.weakHash('password123');
    console.log('   ✓ Weak hash function works');
    console.log('   MD5 Hash:', hashResult);
} catch (error) {
    console.log('   ✗ Weak hash function failed:', error.message);
}

// Test 4: Debug endpoint (information disclosure)
console.log('\n4. Testing Information Disclosure:');
try {
    const debugResult = testVulns.debugEndpoint();
    console.log('   ✓ Debug endpoint works');
    console.log('   Exposed secret:', debugResult.secret);
} catch (error) {
    console.log('   ✗ Debug endpoint failed:', error.message);
}

// Test 5: Prototype pollution
console.log('\n5. Testing Prototype Pollution:');
try {
    const target = { a: 1 };
    const source = { "__proto__": { "polluted": true } };
    const mergeResult = testVulns.merge(target, source);
    console.log('   ✓ Merge function works (vulnerable to prototype pollution)');
} catch (error) {
    console.log('   ✗ Merge function failed:', error.message);
}

// Test 6: Insecure random token generation
console.log('\n6. Testing Insecure Random Generation:');
try {
    const token = testVulns.generateToken();
    console.log('   ✓ Token generation works');
    console.log('   Generated token:', token);
} catch (error) {
    console.log('   ✗ Token generation failed:', error.message);
}

// Test 7: False positive - masking function (should be legitimate)
console.log('\n7. Testing Legitimate Masking Function (False Positive Test):');
try {
    const maskedResult = testVulns.maskSensitiveInformation('password: secret123');
    console.log('   ✓ Masking function works (should NOT be flagged by refined query)');
    console.log('   Masked result:', maskedResult);
} catch (error) {
    console.log('   ✗ Masking function failed:', error.message);
}

// Test 8: False positive - secure function
console.log('\n8. Testing Secure Function (False Positive Test):');
try {
    const secureResult = testVulns.secureFunction();
    console.log('   ✓ Secure function works (should NOT be flagged)');
    console.log('   Secure result:', secureResult);
} catch (error) {
    console.log('   ✗ Secure function failed:', error.message);
}

console.log('\n=== Test Summary ===');
console.log('✓ All functions are syntactically correct and executable');
console.log('✓ Vulnerabilities are present and detectable');
console.log('✓ False positive scenarios are in place');
console.log('\nNext steps:');
console.log('1. Run: npm install (to install dependencies)');
console.log('2. Run: ./scripts/setup-codeql.sh (Linux/Mac) or .\\scripts\\run-exercise.ps1 (Windows)');
console.log('3. Run: ./scripts/view-results.sh (Linux/Mac) or .\\scripts\\view-results.ps1 (Windows)');
console.log('4. Run: ./scripts/validate-exercise.sh (to validate false positive handling)');

// Display the main injected vulnerability
console.log('\n=== Main Injected Vulnerability ===');
console.log('The custom vulnerability pattern "I am hacker\'s paradise" appears in:');
console.log('- app.js (line with VAL_VAR declaration)');
console.log('- test-vulnerabilities.js (multiple instances)');
console.log('- Should be detected by both custom queries');
console.log('- Refined query should exclude false positives in masking functions');

module.exports = {
    runTests: () => console.log('Tests completed successfully')
};
