# CodeQL Vulnerability Detection Exercise

This exercise demonstrates how to use CodeQL to detect security vulnerabilities in Node.js applications, including custom vulnerability patterns and handling false positives.

## Overview

This exercise covers all 8 activities requested:

1. ✅ **Existing Vulnerabilities**: Multiple known vulnerabilities in Node.js code
2. ✅ **CodeQL CLI Scanning**: Scripts to scan and identify vulnerabilities
3. ✅ **Custom Vulnerability Injection**: `VAL_VAR="I am hacker's paradise"`
4. ✅ **Custom CodeQL Query**: Detects the injected vulnerability
5. ✅ **CLI Results Display**: Scripts to show both default and custom query results
6. ✅ **False Positive Scenarios**: Legitimate code that might be flagged
7. ✅ **Query Refinement**: Improved query to avoid false positives
8. ✅ **Final Validation**: Confirms detection of real vulnerabilities while excluding false positives

## Project Structure

```
codeql-exercise/
├── app.js                          # Main vulnerable Node.js application
├── test-vulnerabilities.js         # Additional test cases with vulnerabilities
├── package.json                    # Node.js dependencies
├── queries/                        # Custom CodeQL queries
│   ├── custom-vulnerability.ql     # Initial custom query
│   ├── refined-custom-vulnerability.ql  # Refined query (avoids false positives)
│   ├── sql-injection.ql           # SQL injection detection
│   └── command-injection.ql       # Command injection detection
├── scripts/                        # Automation scripts
│   ├── setup-codeql.sh           # Linux/Mac setup script
│   ├── run-exercise.ps1           # Windows PowerShell script
│   ├── view-results.sh            # Linux/Mac results viewer
│   └── view-results.ps1           # Windows results viewer
└── README.md                       # This file
```

## Vulnerabilities Included

### 1. Custom Injected Vulnerability
- **Pattern**: `VAL_VAR="I am hacker's paradise"`
- **Location**: Multiple files (app.js, test-vulnerabilities.js)
- **Detection**: Custom CodeQL query

### 2. SQL Injection
- **Type**: String concatenation in SQL queries
- **Example**: `"SELECT * FROM users WHERE name = '" + userInput + "'"`
- **Detection**: Custom SQL injection query

### 3. Command Injection
- **Type**: Unsanitized user input in system commands
- **Example**: `exec("tar -czf backup_" + filename + ".tar.gz")`
- **Detection**: Custom command injection query

### 4. Other Vulnerabilities
- Hardcoded credentials
- Path traversal
- Weak cryptography (MD5)
- Information disclosure
- Prototype pollution
- Insecure random number generation
- Insecure deserialization

## False Positive Scenarios

The exercise includes legitimate code that might be incorrectly flagged:

1. **Password Masking Functions**: Functions that legitimately handle sensitive strings for security purposes
2. **Configuration Templates**: Template code with example sensitive values in comments
3. **Test Data**: Test cases that use sensitive patterns for validation

## Prerequisites

1. **CodeQL CLI**: Download from [GitHub CodeQL CLI releases](https://github.com/github/codeql-cli-binaries/releases)
2. **Node.js**: Version 14 or higher
3. **Git**: For cloning and version control

## Quick Start

### Option 1: Automated Setup (Recommended)

**Linux/Mac:**
```bash
cd codeql-exercise
chmod +x scripts/setup-codeql.sh scripts/view-results.sh
./scripts/setup-codeql.sh
./scripts/view-results.sh
```

**Windows:**
```powershell
cd codeql-exercise
.\scripts\run-exercise.ps1
.\scripts\view-results.ps1
```

### Option 2: Manual Steps

1. **Install Dependencies**
```bash
npm install
```

2. **Create CodeQL Database**
```bash
codeql database create --language=javascript --source-root=. codeql-db
```

3. **Run Default Security Queries**
```bash
codeql database analyze codeql-db javascript-security-and-quality.qls --format=sarif-latest --output=default-results.sarif
```

4. **Run Custom Vulnerability Query**
```bash
codeql query run queries/custom-vulnerability.ql --database=codeql-db --output=custom-results.bqrs
```

5. **Run Refined Query (Avoids False Positives)**
```bash
codeql query run queries/refined-custom-vulnerability.ql --database=codeql-db --output=refined-results.bqrs
```

6. **View Results**
```bash
codeql bqrs decode --format=table custom-results.bqrs
codeql bqrs decode --format=table refined-results.bqrs
```

## Expected Results

### Custom Vulnerability Query Results
The initial query should detect:
- ✅ `VAL_VAR = "I am hacker's paradise"` (True positive)
- ✅ Similar hardcoded sensitive values (True positives)
- ❌ Masking functions with sensitive strings (False positive)
- ❌ Template comments with examples (False positive)

### Refined Query Results
The refined query should detect:
- ✅ `VAL_VAR = "I am hacker's paradise"` (True positive)
- ✅ Similar hardcoded sensitive values (True positives)
- ✅ Excludes masking functions (False positive avoided)
- ✅ Excludes template comments (False positive avoided)

## Learning Objectives

1. **Understanding CodeQL**: Learn how to create and run CodeQL queries
2. **Vulnerability Detection**: Identify common security issues in Node.js applications
3. **Custom Queries**: Write custom queries for specific vulnerability patterns
4. **False Positive Handling**: Refine queries to reduce false positives
5. **CLI Usage**: Use CodeQL CLI for automated security scanning

## Next Steps

1. **Experiment**: Modify the queries to detect other vulnerability patterns
2. **Extend**: Add more test cases and vulnerability types
3. **Integrate**: Incorporate CodeQL scanning into CI/CD pipelines
4. **Learn**: Explore CodeQL's advanced features and query libraries

## Troubleshooting

### Common Issues

1. **CodeQL CLI not found**: Ensure CodeQL is installed and in your PATH
2. **Database creation fails**: Check that you're in the correct directory
3. **Query execution errors**: Verify query syntax and database compatibility
4. **No results displayed**: Check that result files were generated successfully

### Getting Help

- [CodeQL Documentation](https://codeql.github.com/docs/)
- [CodeQL Query Help](https://codeql.github.com/docs/writing-codeql-queries/)
- [GitHub Security Lab](https://securitylab.github.com/)

## License

This exercise is provided for educational purposes. Use responsibly and do not deploy the vulnerable code in production environments.
