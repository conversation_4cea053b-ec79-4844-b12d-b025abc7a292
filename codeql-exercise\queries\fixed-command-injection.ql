/**
 * @name Fixed Command Injection Detection
 * @description Properly detects command injection vulnerabilities
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/fixed-command-injection
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript

/**
 * Predicate to identify command execution calls
 */
predicate isCommandExecution(CallExpr call) {
  call.getCalleeName() = "exec" or
  call.getCalleeName() = "execSync" or
  call.getCalleeName() = "spawn" or
  call.getCalleeName() = "spawnSync" or
  exists(PropAccess prop |
    prop.getPropertyName() = "exec" and
    call.getCallee() = prop
  )
}

/**
 * Predicate to identify user input in HTTP requests
 */
predicate isUserInput(Expr expr) {
  // Direct access to request parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "body" or
    prop.getBase().(PropAccess).getPropertyName() = "params" or
    prop.getBase().(PropAccess).getPropertyName() = "query"
  |
    expr = prop
  ) or
  // Variables assigned from user input
  exists(VariableDeclarator decl, DestructuringPattern pattern |
    pattern = decl.getInit() and
    isUserInput(pattern.getASource()) and
    expr.(VarAccess).getVariable() = decl.getBindingPattern().getAVariable()
  ) or
  // Destructuring assignment from request body
  exists(DestructuringPattern pattern, PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "body" and
    pattern.getASource() = prop and
    expr.(VarAccess).getVariable() = pattern.getAVariable()
  )
}

/**
 * Predicate to check if an expression contains user input
 */
predicate containsUserInput(Expr expr) {
  isUserInput(expr) or
  exists(AddExpr add |
    add = expr and
    (containsUserInput(add.getLeftOperand()) or containsUserInput(add.getRightOperand()))
  ) or
  exists(TemplateLiteral template |
    template = expr and
    exists(Expr element | element = template.getAnElement() and containsUserInput(element))
  ) or
  exists(VarAccess var |
    var = expr and
    exists(VariableDeclarator decl |
      decl.getBindingPattern().getAVariable() = var.getVariable() and
      containsUserInput(decl.getInit())
    )
  )
}

from CallExpr call, Expr commandArg, string message
where 
  isCommandExecution(call) and
  commandArg = call.getArgument(0) and
  containsUserInput(commandArg) and
  message = "Command injection: user input flows to command execution"
select call, message + " - Command: " + commandArg.toString() + " (File: " + call.getFile().getBaseName() + ", Line: " + call.getLocation().getStartLine() + ")"
