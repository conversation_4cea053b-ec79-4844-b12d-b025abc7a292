# PowerShell script to test all CodeQL queries

Write-Host "=== CodeQL Query Testing Suite ===" -ForegroundColor Cyan
Write-Host "Testing all queries in the queries folder..." -ForegroundColor Green

# Create results directory
if (-not (Test-Path "results")) {
    New-Item -ItemType Directory -Path "results" | Out-Null
    Write-Host "Created results directory" -ForegroundColor Blue
}

# Function to test a problem query (uses database analyze)
function Test-ProblemQuery {
    param(
        [string]$QueryFile,
        [string]$OutputName
    )
    
    Write-Host "Testing: $QueryFile" -ForegroundColor Yellow
    
    & codeql database analyze node-db $QueryFile --format=csv --output="results/$OutputName.csv" 2>$null
    if ($LASTEXITCODE -eq 0) {
        $resultCount = (Get-Content "results/$OutputName.csv" | Select-Object -Skip 1).Count
        Write-Host "✓ $QueryFile - Found $resultCount results" -ForegroundColor Green
    } else {
        Write-Host "✗ $QueryFile - Failed to execute" -ForegroundColor Red
    }
}

# Function to test a table query (uses query run)
function Test-TableQuery {
    param(
        [string]$QueryFile,
        [string]$OutputName
    )
    
    Write-Host "Testing: $QueryFile" -ForegroundColor Yellow
    
    & codeql query run $QueryFile --database=node-db --output="results/$OutputName.bqrs" 2>$null
    if ($LASTEXITCODE -eq 0) {
        & codeql bqrs decode --format=csv "results/$OutputName.bqrs" > "results/$OutputName.csv" 2>$null
        if ($LASTEXITCODE -eq 0) {
            $resultCount = (Get-Content "results/$OutputName.csv" | Select-Object -Skip 1).Count
            Write-Host "✓ $QueryFile - Found $resultCount results" -ForegroundColor Green
        } else {
            Write-Host "✗ $QueryFile - Failed to decode results" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ $QueryFile - Failed to execute" -ForegroundColor Red
    }
}

Write-Host "=== Testing Security Queries (Problem Type) ===" -ForegroundColor Cyan

# Test problem queries (vulnerability detection)
Test-ProblemQuery "queries/custom-vulnerability.ql" "custom-vulnerability"
Test-ProblemQuery "queries/refined-custom-vulnerability.ql" "refined-custom-vulnerability"
Test-ProblemQuery "queries/sql-injection.ql" "sql-injection"
Test-ProblemQuery "queries/command-injection.ql" "command-injection"
Test-ProblemQuery "queries/simple-working-cmd.ql" "simple-working-cmd"
Test-ProblemQuery "queries/filtered-vulnerability.ql" "filtered-vulnerability"

Write-Host "=== Testing Diagnostic Queries (Table Type) ===" -ForegroundColor Cyan

# Test table queries (diagnostic/debug)
Test-TableQuery "queries/list-source-files.ql" "list-source-files"
Test-TableQuery "queries/vulnerability-by-file.ql" "vulnerability-by-file"
Test-TableQuery "queries/debug-command-injection.ql" "debug-command-injection"
Test-TableQuery "queries/debug-user-input.ql" "debug-user-input"
Test-TableQuery "queries/debug-why-empty.ql" "debug-why-empty"
Test-TableQuery "queries/list-files.ql" "list-files"

Write-Host "=== Testing Experimental Queries ===" -ForegroundColor Cyan

# Test other queries that might have issues
Test-ProblemQuery "queries/fixed-command-injection.ql" "fixed-command-injection"
Test-ProblemQuery "queries/working-command-injection.ql" "working-command-injection"

Write-Host "=== Results Summary ===" -ForegroundColor Cyan

Write-Host "Generated result files:"
Get-ChildItem "results/*.csv" | ForEach-Object {
    Write-Host "  $($_.Name) ($($_.Length) bytes)"
}

Write-Host ""
Write-Host "=== Query Testing Completed! ===" -ForegroundColor Green
Write-Host "All results are available in the 'results/' directory"
Write-Host ""
Write-Host "To view a specific result:" -ForegroundColor Yellow
Write-Host "  Get-Content results/[query-name].csv"
Write-Host ""
Write-Host "To view all non-empty results:" -ForegroundColor Yellow
Write-Host "  Get-ChildItem results/*.csv | Where-Object {$_.Length -gt 1} | ForEach-Object { Write-Host \"=== $($_.Name) ===\"; Get-Content $_.FullName }"
