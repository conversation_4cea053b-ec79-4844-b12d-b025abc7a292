---
queries: []
extensionPacks: []
packs:
  codeql/javascript-all#0:
    name: codeql/javascript-all
    version: 2.6.3
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-all/2.6.3/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-all/2.6.3/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  codeql/threat-models#1:
    name: codeql/threat-models
    version: 1.0.23
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  security-queries#3:
    name: security-queries
    version: 1.0.0
    isLibrary: false
    isExtensionPack: false
    localPath: file:///E:/advance_javascript/codeQL/8/codeql-exercise/
    localPackDefinitionFile: file:///E:/advance_javascript/codeQL/8/codeql-exercise/qlpack.yml
    runDataExtensions:
     -
      pack: codeql/javascript-all#0
      relativePath: ext/apollo-server.model.yml
      index: 0
      firstRowId: 0
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/apollo-server.model.yml
      index: 1
      firstRowId: 1
      rowCount: 4
      locations:
        lineNumbers: A=12+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/javascript-all#0
      relativePath: ext/aws-sdk.model.yml
      index: 0
      firstRowId: 5
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=7*3
     -
      pack: codeql/javascript-all#0
      relativePath: ext/axios.model.yml
      index: 0
      firstRowId: 8
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/axios.model.yml
      index: 1
      firstRowId: 9
      rowCount: 1
      locations:
        lineNumbers: A=12
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/default-threat-models-fixup.model.yml
      index: 0
      firstRowId: 10
      rowCount: 1
      locations:
        lineNumbers: A=8
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/hana-db-client.model.yml
      index: 0
      firstRowId: 11
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/javascript-all#0
      relativePath: ext/hana-db-client.model.yml
      index: 1
      firstRowId: 15
      rowCount: 2
      locations:
        lineNumbers: A=15+1
        columnNumbers: A=9*2
     -
      pack: codeql/javascript-all#0
      relativePath: ext/hana-db-client.model.yml
      index: 2
      firstRowId: 17
      rowCount: 6
      locations:
        lineNumbers: A=22+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/javascript-all#0
      relativePath: ext/make-dir.model.yml
      index: 0
      firstRowId: 23
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/markdown-table.model.yml
      index: 0
      firstRowId: 24
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/mkdirp.model.yml
      index: 0
      firstRowId: 25
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/javascript-all#0
      relativePath: ext/open.model.yml
      index: 0
      firstRowId: 27
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/javascript-all#0
      relativePath: ext/react-relay-threat.model.yml
      index: 0
      firstRowId: 29
      rowCount: 10
      locations:
        lineNumbers: A=6+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/javascript-all#0
      relativePath: ext/rimraf.model.yml
      index: 0
      firstRowId: 39
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/javascript-all#0
      relativePath: ext/shelljs.model.yml
      index: 0
      firstRowId: 42
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: ext/tanstack.model.yml
      index: 0
      firstRowId: 43
      rowCount: 6
      locations:
        lineNumbers: A=6+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/javascript-all#0
      relativePath: ext/underscore.string.model.yml
      index: 0
      firstRowId: 49
      rowCount: 5
      locations:
        lineNumbers: A=6+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: ext/underscore.string.model.yml
      index: 1
      firstRowId: 54
      rowCount: 20
      locations:
        lineNumbers: A=16+1*19
        columnNumbers: A=9*20
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/NoSQL.model.yml
      index: 0
      firstRowId: 74
      rowCount: 4
      locations:
        lineNumbers: A=8+3+1*2
        columnNumbers: A=9*4
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/NodeJSLib.model.yml
      index: 0
      firstRowId: 78
      rowCount: 5
      locations:
        lineNumbers: A=6+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/SQL.model.yml
      index: 0
      firstRowId: 83
      rowCount: 5
      locations:
        lineNumbers: A=6+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/SQL.model.yml
      index: 1
      firstRowId: 88
      rowCount: 4
      locations:
        lineNumbers: A=16+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/helmet/Helmet.Required.Setting.model.yml
      index: 0
      firstRowId: 92
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/minimongo/model.yml
      index: 0
      firstRowId: 94
      rowCount: 75
      locations:
        lineNumbers: A=6+1*74
        columnNumbers: A=9*75
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mongodb/model.yml
      index: 0
      firstRowId: 169
      rowCount: 26
      locations:
        lineNumbers: A=6+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mongodb/model.yml
      index: 1
      firstRowId: 195
      rowCount: 611
      locations:
        lineNumbers: A=37+1*610
        columnNumbers: A=9*611
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mongodb/model.yml
      index: 2
      firstRowId: 806
      rowCount: 32
      locations:
        lineNumbers: A=653+1*31
        columnNumbers: A=9*32
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mongodb/model.yml
      index: 3
      firstRowId: 838
      rowCount: 102
      locations:
        lineNumbers: A=690+1*101
        columnNumbers: A=9*102
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mssql/model.yml
      index: 0
      firstRowId: 940
      rowCount: 34
      locations:
        lineNumbers: A=6+1*33
        columnNumbers: A=9*34
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mysql/model.yml
      index: 0
      firstRowId: 974
      rowCount: 57
      locations:
        lineNumbers: A=6+1*56
        columnNumbers: A=9*57
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/mysql/model.yml
      index: 1
      firstRowId: 1031
      rowCount: 3
      locations:
        lineNumbers: A=68+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/pg/model.yml
      index: 0
      firstRowId: 1034
      rowCount: 65
      locations:
        lineNumbers: A=6+1*64
        columnNumbers: A=9*65
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/pg/model.yml
      index: 1
      firstRowId: 1099
      rowCount: 5
      locations:
        lineNumbers: A=76+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/pg/model.yml
      index: 2
      firstRowId: 1104
      rowCount: 23
      locations:
        lineNumbers: A=86+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sequelize/model.yml
      index: 0
      firstRowId: 1127
      rowCount: 7
      locations:
        lineNumbers: A=6+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sequelize/model.yml
      index: 1
      firstRowId: 1134
      rowCount: 248
      locations:
        lineNumbers: A=18+1*247
        columnNumbers: A=9*248
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sequelize/model.yml
      index: 2
      firstRowId: 1382
      rowCount: 5
      locations:
        lineNumbers: A=271+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sequelize/model.yml
      index: 3
      firstRowId: 1387
      rowCount: 2
      locations:
        lineNumbers: A=281+1
        columnNumbers: A=9*2
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/spanner/model.yml
      index: 0
      firstRowId: 1389
      rowCount: 174
      locations:
        lineNumbers: A=6+1*173
        columnNumbers: A=9*174
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/spanner/model.yml
      index: 1
      firstRowId: 1563
      rowCount: 5
      locations:
        lineNumbers: A=185+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sqlite3/model.yml
      index: 0
      firstRowId: 1568
      rowCount: 15
      locations:
        lineNumbers: A=6+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/frameworks/sqlite3/model.yml
      index: 1
      firstRowId: 1583
      rowCount: 3
      locations:
        lineNumbers: A=26+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/security/domains/IntegrityCheckingRequired/integrity_checking_required.model.yml
      index: 0
      firstRowId: 1586
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/security/domains/compromised/compromised_domains.model.yml
      index: 0
      firstRowId: 1589
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/javascript-all#0
      relativePath: semmle/javascript/security/domains/untrusted/untrusted_domains.model.yml
      index: 0
      firstRowId: 1590
      rowCount: 6
      locations:
        lineNumbers: A=7+1+3+1*3
        columnNumbers: A=9*6
     -
      pack: codeql/threat-models#1
      relativePath: ext/supported-threat-models.model.yml
      index: 0
      firstRowId: 1596
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/threat-models#1
      relativePath: ext/threat-model-grouping.model.yml
      index: 0
      firstRowId: 1597
      rowCount: 15
      locations:
        lineNumbers: A=8+3+1+3+1*5+3+1+5+1*3
        columnNumbers: A=9*15
  codeql/util#2:
    name: codeql/util
    version: 2.0.10
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
