#!/bin/bash

# Script to view CodeQL scan results in human-readable format

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}=== CodeQL Results Viewer ===${NC}"

# Function to display results from BQRS files
display_bqrs_results() {
    local file=$1
    local title=$2
    
    if [ -f "$file" ]; then
        echo -e "${BLUE}$title${NC}"
        echo "----------------------------------------"
        codeql bqrs decode --format=table "$file" 2>/dev/null || echo "No results found or error decoding"
        echo ""
    else
        echo -e "${YELLOW}⚠ $file not found${NC}"
    fi
}

# Function to display SARIF results summary
display_sarif_summary() {
    local file=$1
    
    if [ -f "$file" ]; then
        echo -e "${BLUE}Default Security Scan Results (SARIF Summary)${NC}"
        echo "----------------------------------------"
        
        # Extract key information from SARIF file
        if command -v jq &> /dev/null; then
            echo "Total rules executed:"
            jq '.runs[0].tool.driver.rules | length' "$file" 2>/dev/null || echo "Unable to parse"
            
            echo "Total results found:"
            jq '.runs[0].results | length' "$file" 2>/dev/null || echo "Unable to parse"
            
            echo ""
            echo "Results by severity:"
            jq -r '.runs[0].results[] | .level // "unknown"' "$file" 2>/dev/null | sort | uniq -c || echo "Unable to parse severity levels"
            
            echo ""
            echo "Top 5 vulnerability types found:"
            jq -r '.runs[0].results[] | .ruleId' "$file" 2>/dev/null | sort | uniq -c | sort -nr | head -5 || echo "Unable to parse rule IDs"
        else
            echo "Install 'jq' for detailed SARIF analysis"
            echo "File exists: $file"
        fi
        echo ""
    else
        echo -e "${YELLOW}⚠ $file not found${NC}"
    fi
}

# Display all results
echo -e "${GREEN}Displaying CodeQL scan results...${NC}"
echo ""

# 1. Default security scan results (SARIF)
display_sarif_summary "default-results.sarif"

# 2. Custom vulnerability detection results
display_bqrs_results "custom-results.bqrs" "Custom Vulnerability Detection Results (Hardcoded Sensitive Values)"

# 3. SQL injection results
display_bqrs_results "sql-injection-results.bqrs" "SQL Injection Detection Results"

# 4. Command injection results
display_bqrs_results "command-injection-results.bqrs" "Command Injection Detection Results"

# Additional analysis
echo -e "${CYAN}=== Analysis Summary ===${NC}"
echo "Files scanned:"
find . -name "*.js" -not -path "./node_modules/*" -not -path "./codeql-db/*" | wc -l

echo ""
echo "Vulnerability categories detected:"
echo "  1. Hardcoded sensitive values (custom query)"
echo "  2. SQL injection vulnerabilities"
echo "  3. Command injection vulnerabilities"
echo "  4. Various other security issues (default queries)"

echo ""
echo -e "${YELLOW}Note: Review the results above to identify:${NC}"
echo "  - True positives (actual vulnerabilities)"
echo "  - False positives (legitimate code flagged incorrectly)"
echo "  - Areas where query refinement may be needed"

echo ""
echo -e "${GREEN}=== Results display completed ===${NC}"
