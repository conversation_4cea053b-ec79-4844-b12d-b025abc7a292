# CodeQL Exercise Setup Guide

This guide walks you through setting up and running the complete CodeQL vulnerability detection exercise.

## Prerequisites Installation

### 1. Install CodeQL CLI

**Option A: Direct Download (Recommended)**
1. Go to [CodeQL CLI Releases](https://github.com/github/codeql-cli-binaries/releases)
2. Download the latest release for your platform:
   - Windows: `codeql-win64.zip`
   - macOS: `codeql-osx64.zip`
   - Linux: `codeql-linux64.zip`
3. Extract to a directory (e.g., `/opt/codeql` or `C:\codeql`)
4. Add to your PATH environment variable

**Option B: Using Package Managers**
```bash
# macOS with Homebrew
brew install codeql

# Or download and setup manually
wget https://github.com/github/codeql-cli-binaries/releases/latest/download/codeql-linux64.zip
unzip codeql-linux64.zip
sudo mv codeql /opt/
echo 'export PATH="/opt/codeql:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### 2. Verify CodeQL Installation
```bash
codeql --version
```
You should see output like: `CodeQL command-line toolchain release 2.x.x`

### 3. Install Node.js
- Download from [nodejs.org](https://nodejs.org/) (version 14 or higher)
- Or use a package manager:
```bash
# Ubuntu/Debian
sudo apt install nodejs npm

# macOS
brew install node

# Windows (using Chocolatey)
choco install nodejs
```

## Exercise Setup

### Step 1: Navigate to Exercise Directory
```bash
cd codeql-exercise
```

### Step 2: Install Node.js Dependencies
```bash
npm install
```

### Step 3: Test the Application (Optional)
```bash
node test-runner.js
```
This verifies that all vulnerable functions work correctly before analysis.

### Step 4: Make Scripts Executable (Linux/Mac)
```bash
chmod +x scripts/*.sh
```

## Running the Exercise

### Automated Approach (Recommended)

**Linux/macOS:**
```bash
# Run the complete exercise
./scripts/setup-codeql.sh

# View results
./scripts/view-results.sh

# Validate false positive handling
./scripts/validate-exercise.sh
```

**Windows PowerShell:**
```powershell
# Run the complete exercise
.\scripts\run-exercise.ps1

# View results
.\scripts\view-results.ps1
```

### Manual Approach

If you prefer to run commands manually:

#### 1. Create CodeQL Database
```bash
codeql database create --language=javascript --source-root=. codeql-db
```

#### 2. Run Default Security Analysis
```bash
codeql database analyze codeql-db javascript-security-and-quality.qls --format=sarif-latest --output=default-results.sarif
```

#### 3. Run Custom Queries
```bash
# Initial custom vulnerability query
codeql query run queries/custom-vulnerability.ql --database=codeql-db --output=custom-results.bqrs

# Refined query (handles false positives)
codeql query run queries/refined-custom-vulnerability.ql --database=codeql-db --output=refined-results.bqrs

# SQL injection detection
codeql query run queries/sql-injection.ql --database=codeql-db --output=sql-results.bqrs

# Command injection detection
codeql query run queries/command-injection.ql --database=codeql-db --output=cmd-results.bqrs
```

#### 4. View Results
```bash
# View custom vulnerability results
codeql bqrs decode --format=table custom-results.bqrs

# View refined results
codeql bqrs decode --format=table refined-results.bqrs

# View SARIF results (requires jq for pretty formatting)
cat default-results.sarif | jq '.runs[0].results[] | {ruleId, message: .message.text, location: .locations[0].physicalLocation.artifactLocation.uri}'
```

## Expected Output

### 1. Custom Vulnerability Detection
The queries should detect instances of `"I am hacker's paradise"` in:
- Variable declarations (`VAL_VAR`, `ANOTHER_VAL`, etc.)
- Console.log statements
- Assignment expressions

### 2. False Positive Handling
The refined query should **exclude**:
- Masking functions (functions with "mask" in the name)
- Template comments
- Configuration examples
- Test data marked as templates

### 3. Other Vulnerabilities
Default queries should detect:
- SQL injection vulnerabilities
- Command injection vulnerabilities
- Hardcoded credentials
- Weak cryptography
- Path traversal issues
- Information disclosure

## Troubleshooting

### Common Issues

**1. "codeql: command not found"**
- Ensure CodeQL CLI is installed and in your PATH
- Restart your terminal after adding to PATH

**2. "Database creation failed"**
- Check you're in the correct directory (`codeql-exercise`)
- Ensure you have write permissions
- Try removing existing `codeql-db` directory: `rm -rf codeql-db`

**3. "Query execution failed"**
- Verify the database was created successfully
- Check query syntax in the `.ql` files
- Ensure you're using a compatible CodeQL version

**4. "No results found"**
- This might be expected for some queries
- Check that the vulnerable code patterns exist in the source files
- Verify query logic matches the vulnerability patterns

**5. Permission denied (Linux/Mac)**
- Make scripts executable: `chmod +x scripts/*.sh`
- Or run with bash: `bash scripts/setup-codeql.sh`

### Getting Detailed Logs

Add `--verbose` flag to CodeQL commands for more detailed output:
```bash
codeql database create --language=javascript --source-root=. --verbose codeql-db
```

### Cleaning Up

To start fresh:
```bash
# Remove generated files
rm -rf codeql-db *.sarif *.bqrs

# Reinstall dependencies
rm -rf node_modules
npm install
```

## Next Steps

After completing the exercise:

1. **Experiment**: Modify queries to detect other patterns
2. **Extend**: Add more vulnerability types to the codebase
3. **Learn**: Study the CodeQL documentation for advanced features
4. **Practice**: Try creating queries for other programming languages
5. **Integrate**: Set up CodeQL in CI/CD pipelines

## Resources

- [CodeQL Documentation](https://codeql.github.com/docs/)
- [CodeQL Query Console](https://lgtm.com/query) (for testing queries)
- [GitHub Security Lab](https://securitylab.github.com/)
- [CodeQL Query Help](https://codeql.github.com/docs/writing-codeql-queries/)

## Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review the CodeQL documentation
3. Check GitHub issues in the CodeQL repository
4. Ensure all prerequisites are correctly installed
