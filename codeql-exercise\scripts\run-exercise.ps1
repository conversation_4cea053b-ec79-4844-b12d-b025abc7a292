# PowerShell script for Windows users to run the CodeQL exercise

Write-Host "=== CodeQL Vulnerability Detection Exercise ===" -ForegroundColor Cyan
Write-Host "Setting up CodeQL database and running scans..." -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if CodeQL is installed
if (-not (Test-Command "codeql")) {
    Write-Host "Error: CodeQL CLI not found. Please install CodeQL CLI first." -ForegroundColor Red
    Write-Host "Download from: https://github.com/github/codeql-cli-binaries/releases" -ForegroundColor Yellow
    exit 1
}

# Step 1: Create CodeQL database
Write-Host "Step 1: Creating CodeQL database..." -ForegroundColor Blue
if (Test-Path "codeql-db") {
    Write-Host "Removing existing database..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "codeql-db"
}

& codeql database create --language=javascript --source-root=. --overwrite codeql-db

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ CodeQL database created successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create CodeQL database" -ForegroundColor Red
    exit 1
}

# Step 2: Run default JavaScript security queries
Write-Host "Step 2: Running default security queries..." -ForegroundColor Blue
& codeql database analyze codeql-db javascript-security-and-quality.qls --format=sarif-latest --output=default-results.sarif

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Default security scan completed" -ForegroundColor Green
} else {
    Write-Host "⚠ Default security scan completed with warnings" -ForegroundColor Yellow
}

# Step 3: Run custom vulnerability query
Write-Host "Step 3: Running custom vulnerability detection query..." -ForegroundColor Blue
& codeql query run queries/custom-vulnerability.ql --database=codeql-db --output=custom-results.bqrs

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Custom vulnerability query executed" -ForegroundColor Green
} else {
    Write-Host "✗ Custom vulnerability query failed" -ForegroundColor Red
}

# Step 4: Run SQL injection query
Write-Host "Step 4: Running SQL injection detection query..." -ForegroundColor Blue
& codeql query run queries/sql-injection.ql --database=codeql-db --output=sql-injection-results.bqrs

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ SQL injection query executed" -ForegroundColor Green
} else {
    Write-Host "✗ SQL injection query failed" -ForegroundColor Red
}

# Step 5: Run command injection query
Write-Host "Step 5: Running command injection detection query..." -ForegroundColor Blue
& codeql query run queries/command-injection.ql --database=codeql-db --output=command-injection-results.bqrs

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Command injection query executed" -ForegroundColor Green
} else {
    Write-Host "✗ Command injection query failed" -ForegroundColor Red
}

Write-Host "=== CodeQL scanning completed! ===" -ForegroundColor Green
Write-Host "Results files generated:" -ForegroundColor Cyan
Write-Host "  - default-results.sarif (SARIF format)"
Write-Host "  - custom-results.bqrs (Binary results)"
Write-Host "  - sql-injection-results.bqrs"
Write-Host "  - command-injection-results.bqrs"
Write-Host ""
Write-Host "Use the view-results.ps1 script to display results in human-readable format." -ForegroundColor Yellow
