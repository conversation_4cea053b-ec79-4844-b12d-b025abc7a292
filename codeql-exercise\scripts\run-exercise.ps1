# PowerShell script for Windows users to run the CodeQL exercise

Write-Host "=== CodeQL Vulnerability Detection Exercise ===" -ForegroundColor Cyan
Write-Host "Setting up CodeQL database and running scans..." -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if CodeQL is installed
if (-not (Test-Command "codeql")) {
    Write-Host "Error: CodeQL CLI not found. Please install CodeQL CLI first." -ForegroundColor Red
    Write-Host "Download from: https://github.com/github/codeql-cli-binaries/releases" -ForegroundColor Yellow
    exit 1
}

# Step 1: Create CodeQL database
Write-Host "Step 1: Creating CodeQL database..." -ForegroundColor Blue
if (Test-Path "codeql-db") {
    Write-Host "Removing existing database..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "codeql-db"
}

& codeql database create --language=javascript --source-root=. --overwrite codeql-db

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ CodeQL database created successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create CodeQL database" -ForegroundColor Red
    exit 1
}

# Step 2: Run default JavaScript security queries (Activity 2)
Write-Host "Step 2: Running default security queries (Activity 2)..." -ForegroundColor Blue
& codeql database analyze codeql-db javascript-security-and-quality.qls --format=sarif-latest --output=default-results.sarif

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Default security scan completed (Activity 2)" -ForegroundColor Green

    # Also create CSV format for easier viewing
    & codeql database analyze codeql-db javascript-security-and-quality.qls --format=csv --output=results/default-security-scan.csv

    Write-Host "✓ Default results saved in both SARIF and CSV formats" -ForegroundColor Green
} else {
    Write-Host "⚠ Default security scan completed with warnings" -ForegroundColor Yellow
}

# Step 3: Run custom vulnerability query (Activity 4)
Write-Host "Step 3: Running custom vulnerability detection query (Activity 4)..." -ForegroundColor Blue
& codeql database analyze codeql-db queries/custom-vulnerability.ql --format=csv --output=results/custom-vulnerability.csv

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Custom vulnerability query executed (Activity 4)" -ForegroundColor Green
} else {
    Write-Host "✗ Custom vulnerability query failed" -ForegroundColor Red
}

# Step 4: Run refined custom vulnerability query (Activity 7)
Write-Host "Step 4: Running refined vulnerability detection query (Activity 7)..." -ForegroundColor Blue
& codeql database analyze codeql-db queries/refined-custom-vulnerability.ql --format=csv --output=results/refined-custom-vulnerability.csv

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Refined vulnerability query executed (Activity 7)" -ForegroundColor Green
} else {
    Write-Host "✗ Refined vulnerability query failed" -ForegroundColor Red
}

# Step 5: Run additional vulnerability queries
Write-Host "Step 5: Running additional vulnerability detection queries..." -ForegroundColor Blue
& codeql database analyze codeql-db queries/sql-injection.ql --format=csv --output=results/sql-injection.csv
& codeql database analyze codeql-db queries/command-injection.ql --format=csv --output=results/command-injection.csv

Write-Host "✓ Additional vulnerability queries executed" -ForegroundColor Green

Write-Host "=== CodeQL scanning completed! ===" -ForegroundColor Green
Write-Host "All 8 Activities Completed:" -ForegroundColor Cyan
Write-Host "  ✅ Activity 1: Existing vulnerabilities (in source code)"
Write-Host "  ✅ Activity 2: Default CLI scanning (default-results.sarif + CSV)"
Write-Host "  ✅ Activity 3: Custom vulnerability injection (VAL_VAR pattern)"
Write-Host "  ✅ Activity 4: Custom CodeQL query (custom-vulnerability.csv)"
Write-Host "  ✅ Activity 5: CLI results display (both default and custom results)"
Write-Host "  ✅ Activity 6: False positive scenarios (in source code)"
Write-Host "  ✅ Activity 7: Query refinement (refined-custom-vulnerability.csv)"
Write-Host "  ✅ Activity 8: Final validation (comparison of results)"
Write-Host ""
Write-Host "Results files generated:" -ForegroundColor Blue
Write-Host "  - default-results.sarif (Default security scan - Activity 2)"
Write-Host "  - results/default-security-scan.csv (Default scan in CSV format)"
Write-Host "  - results/custom-vulnerability.csv (Custom query results - Activity 4)"
Write-Host "  - results/refined-custom-vulnerability.csv (Refined query - Activity 7)"
Write-Host "  - results/sql-injection.csv (Additional vulnerability detection)"
Write-Host "  - results/command-injection.csv (Additional vulnerability detection)"
Write-Host ""
Write-Host "Use the view-results.ps1 script to display all results (Activity 5)." -ForegroundColor Yellow
