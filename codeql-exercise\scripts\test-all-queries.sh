#!/bin/bash

# Test All CodeQL Queries Script
# This script runs all queries in the queries folder and generates results

echo "=== CodeQL Query Testing Suite ==="
echo "Testing all queries in the queries folder..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Create results directory
mkdir -p results
echo -e "${BLUE}Created results directory${NC}"

# Function to test a problem query (uses database analyze)
test_problem_query() {
    local query_file=$1
    local output_name=$2
    
    echo -e "${YELLOW}Testing: $query_file${NC}"
    
    if codeql database analyze node-db "$query_file" --format=csv --output="results/${output_name}.csv" 2>/dev/null; then
        local result_count=$(tail -n +2 "results/${output_name}.csv" | wc -l)
        echo -e "${GREEN}✓ $query_file - Found $result_count results${NC}"
    else
        echo -e "${RED}✗ $query_file - Failed to execute${NC}"
    fi
}

# Function to test a table query (uses query run)
test_table_query() {
    local query_file=$1
    local output_name=$2
    
    echo -e "${YELLOW}Testing: $query_file${NC}"
    
    if codeql query run "$query_file" --database=node-db --output="results/${output_name}.bqrs" 2>/dev/null; then
        if codeql bqrs decode --format=csv "results/${output_name}.bqrs" > "results/${output_name}.csv" 2>/dev/null; then
            local result_count=$(tail -n +2 "results/${output_name}.csv" | wc -l)
            echo -e "${GREEN}✓ $query_file - Found $result_count results${NC}"
        else
            echo -e "${RED}✗ $query_file - Failed to decode results${NC}"
        fi
    else
        echo -e "${RED}✗ $query_file - Failed to execute${NC}"
    fi
}

echo -e "${CYAN}=== Testing Security Queries (Problem Type) ===${NC}"

# Test problem queries (vulnerability detection)
test_problem_query "queries/custom-vulnerability.ql" "custom-vulnerability"
test_problem_query "queries/refined-custom-vulnerability.ql" "refined-custom-vulnerability"
test_problem_query "queries/sql-injection.ql" "sql-injection"
test_problem_query "queries/command-injection.ql" "command-injection"
test_problem_query "queries/simple-working-cmd.ql" "simple-working-cmd"
test_problem_query "queries/filtered-vulnerability.ql" "filtered-vulnerability"

echo -e "${CYAN}=== Testing Diagnostic Queries (Table Type) ===${NC}"

# Test table queries (diagnostic/debug)
test_table_query "queries/list-source-files.ql" "list-source-files"
test_table_query "queries/vulnerability-by-file.ql" "vulnerability-by-file"
test_table_query "queries/debug-command-injection.ql" "debug-command-injection"
test_table_query "queries/debug-user-input.ql" "debug-user-input"
test_table_query "queries/debug-why-empty.ql" "debug-why-empty"
test_table_query "queries/list-files.ql" "list-files"

echo -e "${CYAN}=== Testing Experimental Queries ===${NC}"

# Test other queries that might have issues
test_problem_query "queries/fixed-command-injection.ql" "fixed-command-injection"
test_problem_query "queries/working-command-injection.ql" "working-command-injection"
test_table_query "queries/simple-command-injection.ql" "simple-command-injection"

echo -e "${CYAN}=== Results Summary ===${NC}"

echo "Generated result files:"
ls -la results/*.csv | awk '{print "  " $9 " (" $5 " bytes)"}'

echo ""
echo -e "${GREEN}=== Query Testing Completed! ===${NC}"
echo "All results are available in the 'results/' directory"
echo ""
echo "To view a specific result:"
echo "  cat results/[query-name].csv"
echo ""
echo "To view all non-empty results:"
echo "  find results -name '*.csv' -size +1c -exec echo '=== {} ===' \\; -exec cat {} \\;"
