[2025-06-04 11:43:50] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --output=E:\advance_javascript\codeQL\8\codeql-exercise\debug-empty.bqrs -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:43:50] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:43:51] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 11:43:51] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\debug-why-empty.ql"
                      ]
[2025-06-04 11:43:51] Refusing fancy output: The terminal is not an xterm: 
[2025-06-04 11:43:51] Creating executor with 1 threads.
[2025-06-04 11:43:51] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:43:51] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:43:51] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 11:43:51] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise"
                      ]
[2025-06-04 11:43:51] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 1] security-queries: 1.0.0
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 11:43:51] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-04 11:43:51] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-04 11:43:51] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-04 11:43:51] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 11:43:51] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 11:43:52] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql --format=json
[2025-06-04 11:43:52] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:43:52] [DETAILS] resolve library-path> Found enclosing pack 'security-queries' at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 11:43:52] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 11:43:52] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml.
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 1] security-queries: 1.0.0
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 11:43:52] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 11:43:52] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise resolved OK.
[2025-06-04 11:43:52] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-04 11:43:52] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "security-queries\\queries\\debug-why-empty.ql",
                        "qlPackName" : "security-queries"
                      }
[2025-06-04 11:43:52] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:43:52] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:43:53] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql hashes to 776ba2ff796c6c596b009d2eb9a553ba.
[2025-06-04 11:43:53] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:43:53] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 11:43:54] ExternalModuleBindingPass ...
[2025-06-04 11:43:55] ExternalModuleBindingPass time: 00:01.085
[2025-06-04 11:43:55] CollectInstantiationsPass ...
[2025-06-04 11:43:55] CollectInstantiationsPass time: 00:00.324
[2025-06-04 11:43:55] Ql checks ...
[2025-06-04 11:44:00] Ql checks time: 00:04.645
[2025-06-04 11:44:09] Compilation pipeline
[2025-06-04 11:44:09] Type Inference ...
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,232,360
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,695,2112
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 11:44:09] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1014,46301
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,14,14
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,26,26
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,12,15
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,22,32
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,22,22
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,16,18
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,7,10
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 11:44:10] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 11:44:12] Type Inference time: 00:02.580
[2025-06-04 11:44:12] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:44:12] Processing compilation pipeline
[2025-06-04 11:44:12] Started compiling a query
[2025-06-04 11:44:12] Initialising compiler...
[2025-06-04 11:44:12] 	 ... compiler initialised
[2025-06-04 11:44:12] About to start query optimisation
[2025-06-04 11:44:13] Compilation cache hit - skipping compilation.
[2025-06-04 11:44:13] Compilation cache hit - skipping compilation.
[2025-06-04 11:44:13] Compilation cache hit - skipping compilation.
[2025-06-04 11:44:13] Compilation cache miss for 599e7e884ae1e68a083280d5a1a4c9c3.
[2025-06-04 11:44:14] Stored compiled program for 599e7e884ae1e68a083280d5a1a4c9c3.
[2025-06-04 11:44:14] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-04 11:44:14] CSV_COMPILATION: 129,283,177,483
[2025-06-04 11:44:14] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:44:14] [PROGRESS] execute queries> [1/1 comp 21.9s] Compiled E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql.
[2025-06-04 11:44:14] [PROGRESS] execute queries> Starting evaluation of security-queries\queries\debug-why-empty.ql.
[2025-06-04 11:44:14] Starting evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:44:14] (0s) Start query execution
[2025-06-04 11:44:14] (0s) Beginning execution of E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 with 34962 rows and digest fea75dtn6e02l4jtpj0fir9mba6.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me with 61500 rows and digest 1d4de0tjs28ikf4osuddegn1ppe.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 192887 rows and digest bf1ab4ujhkf0don2emb08cu4701.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 237718 rows and digest e2e365km2cv53omf8vjemqdbtr1.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre with 108660 rows and digest 15f8bc8s4t79u5lv8seojntm508.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 with 76021 rows and digest 978f95qfnc1amiq09gqe53hk4k3.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s with 75908 rows and digest f21e59taour23ovre1f9u53nu74.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 with 3906 rows and digest 3444e3v02bl020ava9qii82e3c3.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j with 55376 rows and digest 6f6f02aupa0djiruh6s5gbgfrt6.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 with 147 rows and digest 9221c9o1tle1tvjggqsraki2ve9.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 385 rows and digest 1999b8doaea4airlo4e9tr0a623.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air with empty relation.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 with empty relation.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 with 447 rows and digest 43325c7h18ff672hcvcd7qm0tkd.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::ConstantString#e55e40cc/1@43d594jb with 147 rows and digest 607b8ehoc6an67h29jl5uhfdfl7.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 with empty relation.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 with empty relation.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn with empty relation.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv with empty relation.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 with 5 rows and digest b418cdppe5vhu6uicbdbvhi2d71.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go with 289 rows and digest 681059jinboal2khcnn5qgtb7p2.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t with empty relation.
[2025-06-04 11:44:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t with empty relation.
[2025-06-04 11:44:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 11:44:14] (0s)  >>> Created relation files/2@ec93749g with 115 rows and digest 1da27am6grhuehngssp5m5htnd9.
[2025-06-04 11:44:14] (0s)  >>> Created relation exprs/5@a1831agj with 61500 rows and digest f83d74t85c4ldl9dpg3028d06p5.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate exprs_10#join_rhs/2@27dbbd1m
[2025-06-04 11:44:14] (0s)  >>> Created relation exprs_10#join_rhs/2@27dbbd1m with 61500 rows and digest 6da27335elt5b4ik9drf3iig65c.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::PropAccess#c63e67c9/1@ebfb7bse
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::PropAccess#c63e67c9/1@ebfb7bse with 18553 rows and digest 9a88d4st57gdhtues73ne15n814.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate exprs_230#join_rhs/3@dfee24vq
[2025-06-04 11:44:14] (0s)  >>> Created relation exprs_230#join_rhs/3@dfee24vq with 61500 rows and digest 121fe0ao19alrk2qhpb5jmfd474.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::InvokeExpr#b783e907/1@f74db5bt
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::InvokeExpr#b783e907/1@f74db5bt with 142 rows and digest 26c26b2pg3feckem0aot5smps2c.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate m#Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/1@cfb72dkb
[2025-06-04 11:44:14] (0s)  >>> Created relation m#Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/1@cfb72dkb with 3 rows and digest b761a2qdr5p18lu5dtoaa4fnlp2.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate m#Expr::Identifier#299f46d8#b/1@0d3eeb3r
[2025-06-04 11:44:14] (0s)  >>> Created relation m#Expr::Identifier#299f46d8#b/1@0d3eeb3r with 18532 rows and digest 2342f9etk0o0ojr9p9nis2o8ve1.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate exprs_20#count_range/2@b5deeecs
[2025-06-04 11:44:14] (0s)  >>> Created relation exprs_20#count_range/2@b5deeecs with 61500 rows and digest 967da6c0h8uohfp8b93k21t4l61.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037/2@de20e2ff
[2025-06-04 11:44:14] (0s)  >>> Created relation Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037/2@de20e2ff with 22 rows and digest 6f57ec5hf9q3in59mjcvdir7p58.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate m#@varaccess#b/1@ae1a803r
[2025-06-04 11:44:14] (0s)  >>> Created relation m#@varaccess#b/1@ae1a803r with 18554 rows and digest 6eba665m2c1s7r54uvoimts91ua.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate @varaccess#b/1@dbc0b2i8
[2025-06-04 11:44:14] (0s)  >>> Created relation @varaccess#b/1@dbc0b2i8 with 9 rows and digest 81f92fmtqho41g8qmg6f4075pf3.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::Identifier#299f46d8#b/1@e72058tt
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::Identifier#299f46d8#b/1@e72058tt with 18532 rows and digest 2342f9etk0o0ojr9p9nis2o8ve1.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate _@varaccess#b_Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037#shared/1@c3882a3l
[2025-06-04 11:44:14] (0s)  >>> Created relation _@varaccess#b_Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037#shared/1@c3882a3l with 7 rows and digest ab2eaams0nefr3nn291m8niret1.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::PropAccess.getBase/0#dispred#51cb3016/2@1fa3c18i
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::PropAccess.getBase/0#dispred#51cb3016/2@1fa3c18i with 18553 rows and digest ccf24br8o5klnas0uq6d2oq3903.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate m#Expr::Literal.getValue/0#dispred#c75e715e#bf/1@93543672
[2025-06-04 11:44:14] (0s)  >>> Created relation m#Expr::Literal.getValue/0#dispred#c75e715e#bf/1@93543672 with 155 rows and digest 3c99bb7scl8t3ae4nvlgk0db0c6.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::Literal#62045a1b#b/1@64a9a2o7
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::Literal#62045a1b#b/1@64a9a2o7 with 134 rows and digest d8d427l7gt87f6tpt5rjp7dnfu0.
[2025-06-04 11:44:14] (0s)  >>> Created relation literals/3@f05366h0 with 35271 rows and digest abee41aghqnk1iifnu6cd40i64c.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate literals_20#join_rhs/2@2856f00u
[2025-06-04 11:44:14] (0s)  >>> Created relation literals_20#join_rhs/2@2856f00u with 35271 rows and digest b64a885cknnrqpeoohi2lobda72.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::Literal.getValue/0#dispred#c75e715e#bf/2@1f5a73o6
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::Literal.getValue/0#dispred#c75e715e#bf/2@1f5a73o6 with 134 rows and digest 3dc4c3krnq8dq2acce8h2tlvtv0.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/2@8f35a9a1
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/2@8f35a9a1 with 28 rows and digest 8bf408v4ol6vcpmphibjkbp37a8.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate _Expr::PropAccess.getBase/0#dispred#51cb3016_Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb#shared#1/1@b54311cr
[2025-06-04 11:44:14] (0s)  >>> Created relation _Expr::PropAccess.getBase/0#dispred#51cb3016_Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb#shared#1/1@b54311cr with 1 rows and digest f6d3e3o6im3ktn7n4alaguut676.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate _Expr::PropAccess.getBase/0#dispred#51cb3016_Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb#shared/1@87db31sa
[2025-06-04 11:44:14] (0s)  >>> Created relation _Expr::PropAccess.getBase/0#dispred#51cb3016_Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb#shared/1@87db31sa with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 11:44:14] (0s) Inferred that project##select#query#nullary#2/0@b21c23sd is empty, due to _Expr::PropAccess.getBase/0#dispred#51cb3016_Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb#shared/1@87db31sa.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb#cpe#1/1@62e2a0tm
[2025-06-04 11:44:14] (0s)  >>> Created relation Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb#cpe#1/1@62e2a0tm with 2 rows and digest 039346dnp26gafufqdoq54mc1vf.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate m#AST::AstNode.getFile/0#dispred#dec40f60#bf/1@0117a5g5
[2025-06-04 11:44:14] (0s)  >>> Created relation m#AST::AstNode.getFile/0#dispred#dec40f60#bf/1@0117a5g5 with 144 rows and digest 6e7f83fb933355rrpbg28l22sk2.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate AST::AstNode.getFile/0#dispred#dec40f60#bf/2@f3cac74o
[2025-06-04 11:44:14] (0s)  >>> Created relation AST::AstNode.getFile/0#dispred#dec40f60#bf/2@f3cac74o with 144 rows and digest 68fa55sgnriukik915s4euacjdb.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate project##select#query#nullary#5/0@bd321a88
[2025-06-04 11:44:14] (0s)  >>> Created relation project##select#query#nullary#5/0@bd321a88 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate project##select#query#nullary#4/0@1934bals
[2025-06-04 11:44:14] (0s)  >>> Created relation project##select#query#nullary#4/0@1934bals with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate project##select#query#nullary/0@778bb9r5
[2025-06-04 11:44:14] (0s)  >>> Created relation project##select#query#nullary/0@778bb9r5 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate project##select#query#nullary#3/0@e77dabah
[2025-06-04 11:44:14] (0s)  >>> Created relation project##select#query#nullary#3/0@e77dabah with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 11:44:14] (0s) Starting to evaluate predicate #select#query/3@fb7dbbh3
[2025-06-04 11:44:14] (0s)  >>> Created relation #select#query/3@fb7dbbh3 with 4 rows and digest 8034d5luhtvopc1dc7r4m00slg4.
[2025-06-04 11:44:14] (0s) Query done
[2025-06-04 11:44:14] (0s) Sequence stamp origin is -6041912467702968555
[2025-06-04 11:44:14] (0s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-04 11:44:14] (0s) Unpausing evaluation
[2025-06-04 11:44:14] Evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-why-empty.ql produced BQRS results.
[2025-06-04 11:44:14] [PROGRESS] execute queries> [1/1 eval 515ms] Evaluation done; writing results to E:\advance_javascript\codeQL\8\codeql-exercise\debug-empty.bqrs.
[2025-06-04 11:44:14] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-04 11:44:14] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-04 11:44:14] The disk cache is freshly trimmed; leave it be.
[2025-06-04 11:44:14] Unpausing evaluation
[2025-06-04 11:44:14] Exiting with code 0
