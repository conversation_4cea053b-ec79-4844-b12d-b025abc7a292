/**
 * @name Simple Command Injection Detection
 * @description Detects command injection vulnerabilities with simplified logic
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision medium
 * @id js/simple-command-injection
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript

from CallExpr call, Expr commandArg, string message
where 
  // Find exec calls
  (call.getCalleeName() = "exec" or
   exists(PropAccess prop |
     prop.getPropertyName() = "exec" and
     call.getCallee() = prop
   )) and
  commandArg = call.getArgument(0) and
  (
    // Case 1: Template literal with variable interpolation
    exists(TemplateLiteral template |
      template = commandArg and
      exists(VarAccess var | var = template.getAnElement()) and
      message = "Command injection: variable interpolation in command template"
    ) or
    // Case 2: String concatenation with variables
    exists(AddExpr add |
      add = commandArg and
      exists(VarAccess var | var = add.getAnOperand()) and
      message = "Command injection: variable concatenation in command string"
    ) or
    // Case 3: Direct variable usage
    exists(VarAccess var |
      var = commandArg and
      message = "Command injection: direct variable usage in command"
    )
  )
select call, message + " - Command: " + commandArg.toString() + " (File: " + call.getFile().getBaseName() + ", Line: " + call.getLocation().getStartLine() + ")"
