/**
 * @name SQL Injection Detection
 * @description Detects potential SQL injection vulnerabilities
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/sql-injection-custom
 * @tags security
 *       external/cwe/cwe-89
 */

import javascript

/**
 * Predicate to identify SQL query construction sinks
 */
predicate isSqlSink(DataFlow::Node sink) {
  // String concatenation that forms SQL queries
  exists(AddExpr add |
    exists(StringLiteral str |
      str = add.getAnOperand() and
      str.getValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE).*")
    ) and
    sink.asExpr() = add.getAnOperand()
  ) or
  // Template literals with SQL keywords
  exists(TemplateLiteral template |
    exists(TemplateElement elem |
      elem = template.getAnElement() and
      elem.getValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE).*")
    ) and
    sink.asExpr() = template.getAnElement()
  )
}

/**
 * Predicate to identify user input sources
 */
predicate isUserInputSource(DataFlow::Node source) {
  // HTTP request body parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "body" and
    source.asExpr() = prop
  ) or
  // HTTP request URL parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "params" and
    source.asExpr() = prop
  ) or
  // HTTP request query parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "query" and
    source.asExpr() = prop
  )
}

/**
 * Predicate to identify SQL query construction
 */
predicate isSqlConstruction(DataFlow::Node node, DataFlow::Node userInput) {
  exists(AddExpr add |
    // One operand is user input, another contains SQL keywords
    add.getAnOperand() = userInput.asExpr() and
    exists(StringLiteral str |
      str = add.getAnOperand() and
      str.getValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE|FROM|WHERE).*")
    ) and
    node.asExpr() = add
  ) or
  exists(TemplateLiteral template |
    // Template literal with user input and SQL keywords
    template.getAnElement() = userInput.asExpr() and
    exists(TemplateElement elem |
      elem = template.getAnElement() and
      elem.getValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE|FROM|WHERE).*")
    ) and
    node.asExpr() = template
  )
}

from DataFlow::Node source, DataFlow::Node sink, string message
where
  (
    // Direct flow from user input to SQL construction
    isUserInputSource(source) and
    isSqlSink(sink) and
    source = sink and
    message = "SQL injection: user input flows directly to SQL query construction"
  ) or
  (
    // Flow through SQL construction
    isUserInputSource(source) and
    exists(DataFlow::Node construction |
      isSqlConstruction(construction, source) and
      isSqlSink(sink) and
      construction = sink
    ) and
    message = "SQL injection: user input used in SQL query construction"
  ) or
  (
    // Simple case: user input in SQL string
    isUserInputSource(source) and
    isSqlSink(sink) and
    exists(AddExpr add |
      add.getAnOperand() = source.asExpr() and
      sink.asExpr() = add
    ) and
    message = "SQL injection: user input concatenated into SQL query"
  )
select sink, message + " at " + sink.getLocation().toString()
