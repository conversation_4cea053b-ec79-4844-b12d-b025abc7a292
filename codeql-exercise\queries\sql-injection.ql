/**
 * @name SQL Injection Detection
 * @description Detects potential SQL injection vulnerabilities
 * @kind path-problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/sql-injection-custom
 * @tags security
 *       external/cwe/cwe-89
 */

import javascript
import semmle.javascript.security.dataflow.SqlInjectionQuery

/**
 * Custom SQL injection configuration
 */
class CustomSqlInjectionConfiguration extends TaintTracking::Configuration {
  CustomSqlInjectionConfiguration() { this = "CustomSqlInjectionConfiguration" }

  override predicate isSource(DataFlow::Node source) {
    // HTTP request parameters
    source instanceof RemoteFlowSource or
    // Request body properties
    exists(PropAccess prop |
      prop.getBase().(PropAccess).getPropertyName() = "body" and
      source.asExpr() = prop
    )
  }

  override predicate isSink(DataFlow::Node sink) {
    // String concatenation that forms SQL queries
    exists(AddExpr add |
      add.getAnOperand().getStringValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE).*") and
      sink.asExpr() = add.getAnOperand()
    ) or
    // Template literals with SQL keywords
    exists(TemplateLiteral template |
      template.getStringValue().regexpMatch("(?i).*(SELECT|INSERT|UPDATE|DELETE).*") and
      sink.asExpr() = template.getAnElement()
    )
  }

  override predicate isSanitizer(DataFlow::Node node) {
    // Parameterized queries or escape functions
    exists(CallExpr call |
      call.getCalleeName().regexpMatch("(?i).*(escape|sanitize|prepare).*") and
      node.asExpr() = call
    )
  }
}

from CustomSqlInjectionConfiguration cfg, DataFlow::PathNode source, DataFlow::PathNode sink
where cfg.hasFlowPath(source, sink)
select sink.getNode(), source, sink, "SQL injection vulnerability: user input flows to SQL query construction."
