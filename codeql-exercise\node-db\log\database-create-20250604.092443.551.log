[2025-06-04 09:24:43] This is codeql database create node-db --language=javascript --source-root=.
[2025-06-04 09:24:43] Log file was started late.
[2025-06-04 09:24:43] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\8\codeql-exercise\node-db.
[2025-06-04 09:24:43] Running plumbing command: codeql database init --language=javascript --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --source-root=E:\advance_javascript\codeQL\8\codeql-exercise --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db
[2025-06-04 09:24:43] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-04 09:24:43] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-04 09:24:43] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-04 09:24:43] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-04 09:24:43] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-04 09:24:43] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-04 09:24:44] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-04 09:24:44] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-04 09:24:44] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 09:24:44] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-04 09:24:44] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-04 09:24:44] Using configure-baseline script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\configure-baseline.cmd.
[2025-06-04 09:24:44] [PROGRESS] database init> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\configure-baseline.cmd]
[2025-06-04 09:24:44] [SPAMMY] database init> Found 3 baseline files for javascript.
[2025-06-04 09:24:44] [PROGRESS] database init> Calculated baseline information for languages: javascript (343ms).
[2025-06-04 09:24:44] [PROGRESS] database init> Resolving extractor javascript.
[2025-06-04 09:24:44] [DETAILS] database init> Found candidate extractor root for javascript: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-04 09:24:44] [PROGRESS] database init> Successfully loaded extractor JavaScript/TypeScript (javascript) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-04 09:24:44] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\8\codeql-exercise\node-db. This in-progress database is ready to be populated by an extractor.
[2025-06-04 09:24:44] Plumbing command codeql database init completed.
[2025-06-04 09:24:44] [PROGRESS] database create> Running build command: []
[2025-06-04 09:24:44] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\8\codeql-exercise --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db
[2025-06-04 09:24:44] Using autobuild script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd.
[2025-06-04 09:24:44] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\8\codeql-exercise: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd]
[2025-06-04 09:24:45] [build-stdout] Single-threaded extraction.
[2025-06-04 09:24:45] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\app.js
[2025-06-04 09:24:45] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\app.js (219 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\package.json
[2025-06-04 09:24:45] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\package.json (6 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml
[2025-06-04 09:24:45] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml (38 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-runner.js
[2025-06-04 09:24:45] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-runner.js (67 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-vulnerabilities.js
[2025-06-04 09:24:45] [build-stdout] Done extracting E:\advance_javascript\codeQL\8\codeql-exercise\test-vulnerabilities.js (24 ms)
[2025-06-04 09:24:45] [build-stderr] No externs trap cache found
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2016.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2016.js (21 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2017.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es2017.js (18 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es3.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es3.js (206 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es5.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es5.js (47 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6.js (133 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6_collections.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\es6_collections.js (19 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\intl.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\intl.js (24 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\proxy.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\es\proxy.js (9 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\bdd.js
[2025-06-04 09:24:45] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\bdd.js (43 ms)
[2025-06-04 09:24:45] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\jquery-3.2.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\jquery-3.2.js (161 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\should.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\should.js (24 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\vows.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\lib\vows.js (104 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert.js (22 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert_legacy.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\assert_legacy.js (10 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\buffer.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\buffer.js (23 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\child_process.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\child_process.js (62 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\cluster.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\cluster.js (75 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\console.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\console.js (9 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\constants.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\constants.js (107 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\crypto.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\crypto.js (67 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dgram.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dgram.js (16 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dns.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\dns.js (23 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\domain.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\domain.js (12 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\events.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\events.js (15 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\fs.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\fs.js (112 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\globals.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\globals.js (122 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\http.js
[2025-06-04 09:24:46] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\http.js (46 ms)
[2025-06-04 09:24:46] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\https.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\https.js (21 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\module.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\module.js (10 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\net.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\net.js (34 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\os.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\os.js (17 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\path.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\path.js (20 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\process.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\process.js (8 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\punycode.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\punycode.js (10 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\querystring.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\querystring.js (14 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\readline.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\readline.js (19 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\repl.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\repl.js (14 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\stream.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\stream.js (56 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\string_decoder.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\string_decoder.js (7 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\sys.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\sys.js (7 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\timers.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\timers.js (10 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tls.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tls.js (39 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tty.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\tty.js (11 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\url.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\url.js (12 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\util.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\util.js (31 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\v8.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\v8.js (11 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\vm.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\vm.js (15 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\zlib.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\nodejs\zlib.js (34 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\jsshell.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\jsshell.js (45 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\rhino.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\rhino.js (8 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\spidermonkey.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\spidermonkey.js (8 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\v8.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\vm\v8.js (10 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\chrome.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\chrome.js (40 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fetchapi.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fetchapi.js (32 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fileapi.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\fileapi.js (64 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\flash.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\flash.js (19 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_css.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_css.js (23 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_dom.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_dom.js (57 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_event.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_event.js (17 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_ext.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_ext.js (9 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_xml.js
[2025-06-04 09:24:47] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\gecko_xml.js (12 ms)
[2025-06-04 09:24:47] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\html5.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\html5.js (252 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_css.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_css.js (17 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_dom.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_dom.js (52 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_event.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_event.js (13 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_vml.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\ie_vml.js (6 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\intl.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\intl.js (14 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\iphone.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\iphone.js (11 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\mediasource.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\mediasource.js (11 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\page_visibility.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\page_visibility.js (8 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\streamsapi.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\streamsapi.js (23 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\url.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\url.js (13 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_anim_timing.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_anim_timing.js (12 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_batterystatus.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_batterystatus.js (7 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css.js (102 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css3d.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_css3d.js (12 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_device_sensor_event.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_device_sensor_event.js (10 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom1.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom1.js (59 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom2.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom2.js (88 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom3.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom3.js (35 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom4.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_dom4.js (9 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_elementtraversal.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_elementtraversal.js (8 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_encoding.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_encoding.js (12 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event.js (32 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event3.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_event3.js (9 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_gamepad.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_gamepad.js (7 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_geolocation.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_geolocation.js (11 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_indexeddb.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_indexeddb.js (72 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_midi.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_midi.js (19 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_navigation_timing.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_navigation_timing.js (25 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_permissions.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_permissions.js (8 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_pointer_events.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_pointer_events.js (9 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_range.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_range.js (13 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_requestidlecallback.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_requestidlecallback.js (7 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_rtc.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_rtc.js (92 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_screen_orientation.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_screen_orientation.js (7 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_selectors.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_selectors.js (12 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_serviceworker.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_serviceworker.js (33 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_touch_event.js
[2025-06-04 09:24:48] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_touch_event.js (13 ms)
[2025-06-04 09:24:48] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_webcrypto.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_webcrypto.js (375 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_xml.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\w3c_xml.js (16 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webgl.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webgl.js (139 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_css.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_css.js (68 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_dom.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_dom.js (17 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_event.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_event.js (8 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_notifications.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_notifications.js (12 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_usercontent.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webkit_usercontent.js (7 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webstorage.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\webstorage.js (9 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\whatwg_encoding.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\whatwg_encoding.js (6 ms)
[2025-06-04 09:24:49] [build-stdout] Extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\window.js
[2025-06-04 09:24:49] [build-stdout] Done extracting C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\data\externs\web\window.js (9 ms)
[2025-06-04 09:24:49] Plumbing command codeql database trace-command completed.
[2025-06-04 09:24:49] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\8\codeql-exercise\node-db.
[2025-06-04 09:24:49] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db
[2025-06-04 09:24:49] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\8\codeql-exercise\node-db...
[2025-06-04 09:24:49] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\semmlecode.javascript.dbscheme -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript E:\advance_javascript\codeQL\8\codeql-exercise\node-db\trap\javascript
[2025-06-04 09:24:49] Clearing disk cache since the version file E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript\default\cache\version does not exist
[2025-06-04 09:24:49] Tuple pool not found. Clearing relations with cached strings
[2025-06-04 09:24:49] Trimming disk cache at E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript\default\cache in mode clear.
[2025-06-04 09:24:49] Sequence stamp origin is -6041948293619557677
[2025-06-04 09:24:49] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-04 09:24:49] Unpausing evaluation
[2025-06-04 09:24:49] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-04 09:24:49] Unpausing evaluation
[2025-06-04 09:24:49] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-04 09:24:49] Unpausing evaluation
[2025-06-04 09:24:49] Trimming completed (19ms): Purged everything.
[2025-06-04 09:24:50] Scanning for files in E:\advance_javascript\codeQL\8\codeql-exercise\node-db\trap\javascript
[2025-06-04 09:24:50] Found 116 TRAP files (8.35 MiB)
[2025-06-04 09:24:50] [PROGRESS] dataset import> Importing TRAP files
[2025-06-04 09:24:50] Importing es2016.js.trap.gz (1 of 116)
[2025-06-04 09:24:50] Importing es2017.js.trap.gz (2 of 116)
[2025-06-04 09:24:50] Importing es3.js.trap.gz (3 of 116)
[2025-06-04 09:24:50] Importing es5.js.trap.gz (4 of 116)
[2025-06-04 09:24:50] Importing es6.js.trap.gz (5 of 116)
[2025-06-04 09:24:50] Importing es6_collections.js.trap.gz (6 of 116)
[2025-06-04 09:24:50] Importing intl.js.trap.gz (7 of 116)
[2025-06-04 09:24:50] Importing proxy.js.trap.gz (8 of 116)
[2025-06-04 09:24:50] Importing bdd.js.trap.gz (9 of 116)
[2025-06-04 09:24:50] Importing jquery-3.2.js.trap.gz (10 of 116)
[2025-06-04 09:24:50] Importing should.js.trap.gz (11 of 116)
[2025-06-04 09:24:50] Importing vows.js.trap.gz (12 of 116)
[2025-06-04 09:24:50] Importing assert.js.trap.gz (13 of 116)
[2025-06-04 09:24:50] Importing assert_legacy.js.trap.gz (14 of 116)
[2025-06-04 09:24:50] Importing buffer.js.trap.gz (15 of 116)
[2025-06-04 09:24:50] Importing child_process.js.trap.gz (16 of 116)
[2025-06-04 09:24:50] Importing cluster.js.trap.gz (17 of 116)
[2025-06-04 09:24:50] Importing console.js.trap.gz (18 of 116)
[2025-06-04 09:24:50] Importing constants.js.trap.gz (19 of 116)
[2025-06-04 09:24:50] Importing crypto.js.trap.gz (20 of 116)
[2025-06-04 09:24:50] Importing dgram.js.trap.gz (21 of 116)
[2025-06-04 09:24:50] Importing dns.js.trap.gz (22 of 116)
[2025-06-04 09:24:50] Importing domain.js.trap.gz (23 of 116)
[2025-06-04 09:24:50] Importing events.js.trap.gz (24 of 116)
[2025-06-04 09:24:50] Importing fs.js.trap.gz (25 of 116)
[2025-06-04 09:24:50] Importing globals.js.trap.gz (26 of 116)
[2025-06-04 09:24:51] Importing http.js.trap.gz (27 of 116)
[2025-06-04 09:24:51] Importing https.js.trap.gz (28 of 116)
[2025-06-04 09:24:51] Importing module.js.trap.gz (29 of 116)
[2025-06-04 09:24:51] Importing net.js.trap.gz (30 of 116)
[2025-06-04 09:24:51] Importing os.js.trap.gz (31 of 116)
[2025-06-04 09:24:51] Importing path.js.trap.gz (32 of 116)
[2025-06-04 09:24:51] Importing process.js.trap.gz (33 of 116)
[2025-06-04 09:24:51] Importing punycode.js.trap.gz (34 of 116)
[2025-06-04 09:24:51] Importing querystring.js.trap.gz (35 of 116)
[2025-06-04 09:24:51] Importing readline.js.trap.gz (36 of 116)
[2025-06-04 09:24:51] Importing repl.js.trap.gz (37 of 116)
[2025-06-04 09:24:51] Importing stream.js.trap.gz (38 of 116)
[2025-06-04 09:24:51] Importing string_decoder.js.trap.gz (39 of 116)
[2025-06-04 09:24:51] Importing sys.js.trap.gz (40 of 116)
[2025-06-04 09:24:51] Importing timers.js.trap.gz (41 of 116)
[2025-06-04 09:24:51] Importing tls.js.trap.gz (42 of 116)
[2025-06-04 09:24:51] Importing tty.js.trap.gz (43 of 116)
[2025-06-04 09:24:51] Importing url.js.trap.gz (44 of 116)
[2025-06-04 09:24:51] Importing util.js.trap.gz (45 of 116)
[2025-06-04 09:24:51] Importing v8.js.trap.gz (46 of 116)
[2025-06-04 09:24:51] Importing vm.js.trap.gz (47 of 116)
[2025-06-04 09:24:51] Importing zlib.js.trap.gz (48 of 116)
[2025-06-04 09:24:51] Importing jsshell.js.trap.gz (49 of 116)
[2025-06-04 09:24:51] Importing rhino.js.trap.gz (50 of 116)
[2025-06-04 09:24:51] Importing spidermonkey.js.trap.gz (51 of 116)
[2025-06-04 09:24:51] Importing v8.js.trap.gz (52 of 116)
[2025-06-04 09:24:51] Importing chrome.js.trap.gz (53 of 116)
[2025-06-04 09:24:51] Importing fetchapi.js.trap.gz (54 of 116)
[2025-06-04 09:24:51] Importing fileapi.js.trap.gz (55 of 116)
[2025-06-04 09:24:51] Importing flash.js.trap.gz (56 of 116)
[2025-06-04 09:24:51] Importing gecko_css.js.trap.gz (57 of 116)
[2025-06-04 09:24:51] Importing gecko_dom.js.trap.gz (58 of 116)
[2025-06-04 09:24:51] Importing gecko_event.js.trap.gz (59 of 116)
[2025-06-04 09:24:51] Importing gecko_ext.js.trap.gz (60 of 116)
[2025-06-04 09:24:51] Importing gecko_xml.js.trap.gz (61 of 116)
[2025-06-04 09:24:51] Importing html5.js.trap.gz (62 of 116)
[2025-06-04 09:24:51] Importing ie_css.js.trap.gz (63 of 116)
[2025-06-04 09:24:51] Importing ie_dom.js.trap.gz (64 of 116)
[2025-06-04 09:24:51] Importing ie_event.js.trap.gz (65 of 116)
[2025-06-04 09:24:51] Importing ie_vml.js.trap.gz (66 of 116)
[2025-06-04 09:24:51] Importing intl.js.trap.gz (67 of 116)
[2025-06-04 09:24:51] Importing iphone.js.trap.gz (68 of 116)
[2025-06-04 09:24:51] Importing mediasource.js.trap.gz (69 of 116)
[2025-06-04 09:24:51] Importing page_visibility.js.trap.gz (70 of 116)
[2025-06-04 09:24:51] Importing streamsapi.js.trap.gz (71 of 116)
[2025-06-04 09:24:51] Importing url.js.trap.gz (72 of 116)
[2025-06-04 09:24:51] Importing w3c_anim_timing.js.trap.gz (73 of 116)
[2025-06-04 09:24:51] Importing w3c_batterystatus.js.trap.gz (74 of 116)
[2025-06-04 09:24:51] Importing w3c_css.js.trap.gz (75 of 116)
[2025-06-04 09:24:51] Importing w3c_css3d.js.trap.gz (76 of 116)
[2025-06-04 09:24:51] Importing w3c_device_sensor_event.js.trap.gz (77 of 116)
[2025-06-04 09:24:51] Importing w3c_dom1.js.trap.gz (78 of 116)
[2025-06-04 09:24:51] Importing w3c_dom2.js.trap.gz (79 of 116)
[2025-06-04 09:24:51] Importing w3c_dom3.js.trap.gz (80 of 116)
[2025-06-04 09:24:51] Importing w3c_dom4.js.trap.gz (81 of 116)
[2025-06-04 09:24:51] Importing w3c_elementtraversal.js.trap.gz (82 of 116)
[2025-06-04 09:24:51] Importing w3c_encoding.js.trap.gz (83 of 116)
[2025-06-04 09:24:51] Importing w3c_event.js.trap.gz (84 of 116)
[2025-06-04 09:24:51] Importing w3c_event3.js.trap.gz (85 of 116)
[2025-06-04 09:24:51] Importing w3c_gamepad.js.trap.gz (86 of 116)
[2025-06-04 09:24:51] Importing w3c_geolocation.js.trap.gz (87 of 116)
[2025-06-04 09:24:51] Importing w3c_indexeddb.js.trap.gz (88 of 116)
[2025-06-04 09:24:51] Importing w3c_midi.js.trap.gz (89 of 116)
[2025-06-04 09:24:51] Importing w3c_navigation_timing.js.trap.gz (90 of 116)
[2025-06-04 09:24:51] Importing w3c_permissions.js.trap.gz (91 of 116)
[2025-06-04 09:24:51] Importing w3c_pointer_events.js.trap.gz (92 of 116)
[2025-06-04 09:24:51] Importing w3c_range.js.trap.gz (93 of 116)
[2025-06-04 09:24:51] Importing w3c_requestidlecallback.js.trap.gz (94 of 116)
[2025-06-04 09:24:51] Importing w3c_rtc.js.trap.gz (95 of 116)
[2025-06-04 09:24:51] Importing w3c_screen_orientation.js.trap.gz (96 of 116)
[2025-06-04 09:24:51] Importing w3c_selectors.js.trap.gz (97 of 116)
[2025-06-04 09:24:51] Importing w3c_serviceworker.js.trap.gz (98 of 116)
[2025-06-04 09:24:51] Importing w3c_touch_event.js.trap.gz (99 of 116)
[2025-06-04 09:24:51] Importing w3c_webcrypto.js.trap.gz (100 of 116)
[2025-06-04 09:24:51] Importing w3c_xml.js.trap.gz (101 of 116)
[2025-06-04 09:24:51] Importing webgl.js.trap.gz (102 of 116)
[2025-06-04 09:24:52] Importing webkit_css.js.trap.gz (103 of 116)
[2025-06-04 09:24:52] Importing webkit_dom.js.trap.gz (104 of 116)
[2025-06-04 09:24:52] Importing webkit_event.js.trap.gz (105 of 116)
[2025-06-04 09:24:52] Importing webkit_notifications.js.trap.gz (106 of 116)
[2025-06-04 09:24:52] Importing webkit_usercontent.js.trap.gz (107 of 116)
[2025-06-04 09:24:52] Importing webstorage.js.trap.gz (108 of 116)
[2025-06-04 09:24:52] Importing whatwg_encoding.js.trap.gz (109 of 116)
[2025-06-04 09:24:52] Importing window.js.trap.gz (110 of 116)
[2025-06-04 09:24:52] Importing app.js.trap.gz (111 of 116)
[2025-06-04 09:24:52] Importing package.json.trap.gz (112 of 116)
[2025-06-04 09:24:52] Importing qlpack.yml.trap.gz (113 of 116)
[2025-06-04 09:24:52] Importing test-runner.js.trap.gz (114 of 116)
[2025-06-04 09:24:52] Importing test-vulnerabilities.js.trap.gz (115 of 116)
[2025-06-04 09:24:52] Importing metadata.trap.gz (116 of 116)
[2025-06-04 09:24:52] [PROGRESS] dataset import> Merging relations
[2025-06-04 09:24:52] Merging 1 fragment for 'files'.
[2025-06-04 09:24:52] Merged 429 bytes for 'files'.
[2025-06-04 09:24:52] Merging 1 fragment for 'folders'.
[2025-06-04 09:24:52] Merged 77 bytes for 'folders'.
[2025-06-04 09:24:52] Merging 1 fragment for 'containerparent'.
[2025-06-04 09:24:52] Merged 324 bytes for 'containerparent'.
[2025-06-04 09:24:52] Merging 2 fragments for 'locations_default'.
[2025-06-04 09:24:52] Merged 820826 bytes (801.59 KiB) for 'locations_default'.
[2025-06-04 09:24:52] Merging 3 fragments for 'hasLocation'.
[2025-06-04 09:24:52] Merged 524027 bytes (511.75 KiB) for 'hasLocation'.
[2025-06-04 09:24:52] Merging 1 fragment for 'extraction_data'.
[2025-06-04 09:24:52] Merged 534 bytes for 'extraction_data'.
[2025-06-04 09:24:52] Merging 1 fragment for 'scopes'.
[2025-06-04 09:24:52] Merged 4254 bytes (4.15 KiB) for 'scopes'.
[2025-06-04 09:24:52] Merging 1 fragment for 'comments'.
[2025-06-04 09:24:52] Merged 37459 bytes (36.58 KiB) for 'comments'.
[2025-06-04 09:24:52] Merging 1 fragment for 'indentation'.
[2025-06-04 09:24:52] Merged 27023 bytes (26.39 KiB) for 'indentation'.
[2025-06-04 09:24:52] Merging 1 fragment for 'numlines'.
[2025-06-04 09:24:52] Merged 1612 bytes (1.57 KiB) for 'numlines'.
[2025-06-04 09:24:52] Merging 1 fragment for 'tokeninfo'.
[2025-06-04 09:24:52] Merged 444523 bytes (434.10 KiB) for 'tokeninfo'.
[2025-06-04 09:24:52] Merging 1 fragment for 'next_token'.
[2025-06-04 09:24:52] Merged 20466 bytes (19.99 KiB) for 'next_token'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc'.
[2025-06-04 09:24:52] Merged 20266 bytes (19.79 KiB) for 'jsdoc'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_tags'.
[2025-06-04 09:24:52] Merged 95396 bytes (93.16 KiB) for 'jsdoc_tags'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_tag_descriptions'.
[2025-06-04 09:24:52] Merged 7318 bytes (7.15 KiB) for 'jsdoc_tag_descriptions'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_tag_names'.
[2025-06-04 09:24:52] Merged 14299 bytes (13.96 KiB) for 'jsdoc_tag_names'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_type_exprs'.
[2025-06-04 09:24:52] Merged 125568 bytes (122.62 KiB) for 'jsdoc_type_exprs'.
[2025-06-04 09:24:52] Merging 1 fragment for 'toplevels'.
[2025-06-04 09:24:52] Merged 238 bytes for 'toplevels'.
[2025-06-04 09:24:52] Merging 1 fragment for 'stmts'.
[2025-06-04 09:24:52] Merged 103571 bytes (101.14 KiB) for 'stmts'.
[2025-06-04 09:24:52] Merging 1 fragment for 'stmt_containers'.
[2025-06-04 09:24:52] Merged 47059 bytes (45.96 KiB) for 'stmt_containers'.
[2025-06-04 09:24:52] Merging 1 fragment for 'exprs'.
[2025-06-04 09:24:52] Merged 416814 bytes (407.04 KiB) for 'exprs'.
[2025-06-04 09:24:52] Merging 1 fragment for 'enclosing_stmt'.
[2025-06-04 09:24:52] Merged 84380 bytes (82.40 KiB) for 'enclosing_stmt'.
[2025-06-04 09:24:52] Merging 1 fragment for 'expr_containers'.
[2025-06-04 09:24:52] Merged 93016 bytes (90.84 KiB) for 'expr_containers'.
[2025-06-04 09:24:52] Merging 1 fragment for 'literals'.
[2025-06-04 09:24:52] Merged 71483 bytes (69.81 KiB) for 'literals'.
[2025-06-04 09:24:52] Merging 1 fragment for 'variables'.
[2025-06-04 09:24:52] Merged 39929 bytes (38.99 KiB) for 'variables'.
[2025-06-04 09:24:52] Merging 1 fragment for 'bind'.
[2025-06-04 09:24:52] Merged 15155 bytes (14.80 KiB) for 'bind'.
[2025-06-04 09:24:52] Merging 1 fragment for 'scopenodes'.
[2025-06-04 09:24:52] Merged 8427 bytes (8.23 KiB) for 'scopenodes'.
[2025-06-04 09:24:52] Merging 1 fragment for 'scopenesting'.
[2025-06-04 09:24:52] Merged 4484 bytes (4.38 KiB) for 'scopenesting'.
[2025-06-04 09:24:52] Merging 1 fragment for 'decl'.
[2025-06-04 09:24:52] Merged 13495 bytes (13.18 KiB) for 'decl'.
[2025-06-04 09:24:52] Merging 1 fragment for 'is_arguments_object'.
[2025-06-04 09:24:52] Merged 4012 bytes (3.92 KiB) for 'is_arguments_object'.
[2025-06-04 09:24:52] Merging 1 fragment for 'entry_cfg_node'.
[2025-06-04 09:24:52] Merged 8903 bytes (8.69 KiB) for 'entry_cfg_node'.
[2025-06-04 09:24:52] Merging 1 fragment for 'exit_cfg_node'.
[2025-06-04 09:24:52] Merged 8903 bytes (8.69 KiB) for 'exit_cfg_node'.
[2025-06-04 09:24:52] Merging 1 fragment for 'successor'.
[2025-06-04 09:24:52] Merged 168078 bytes (164.14 KiB) for 'successor'.
[2025-06-04 09:24:52] Merging 1 fragment for 'is_externs'.
[2025-06-04 09:24:52] Merged 222 bytes for 'is_externs'.
[2025-06-04 09:24:52] Merging 1 fragment for 'filetype'.
[2025-06-04 09:24:52] Merged 252 bytes for 'filetype'.
[2025-06-04 09:24:52] Merging 1 fragment for 'extraction_time'.
[2025-06-04 09:24:52] Merged 14526 bytes (14.19 KiB) for 'extraction_time'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_prefix_qualifier'.
[2025-06-04 09:24:52] Merged 1894 bytes (1.85 KiB) for 'jsdoc_prefix_qualifier'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_errors'.
[2025-06-04 09:24:52] Merged 1139 bytes (1.11 KiB) for 'jsdoc_errors'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_record_field_name'.
[2025-06-04 09:24:52] Merged 1596 bytes (1.56 KiB) for 'jsdoc_record_field_name'.
[2025-06-04 09:24:52] Merging 1 fragment for 'jsdoc_has_new_parameter'.
[2025-06-04 09:24:52] Merged 35 bytes for 'jsdoc_has_new_parameter'.
[2025-06-04 09:24:52] Merging 1 fragment for 'properties'.
[2025-06-04 09:24:52] Merged 577 bytes for 'properties'.
[2025-06-04 09:24:52] Merging 1 fragment for 'regexpterm'.
[2025-06-04 09:24:52] Merged 1288 bytes (1.26 KiB) for 'regexpterm'.
[2025-06-04 09:24:52] Merging 1 fragment for 'regexp_const_value'.
[2025-06-04 09:24:52] Merged 479 bytes for 'regexp_const_value'.
[2025-06-04 09:24:52] Merging 1 fragment for 'guard_node'.
[2025-06-04 09:24:52] Merged 161 bytes for 'guard_node'.
[2025-06-04 09:24:52] Merging 1 fragment for 'is_module'.
[2025-06-04 09:24:52] Merged 93 bytes for 'is_module'.
[2025-06-04 09:24:52] Merging 1 fragment for 'is_nodejs'.
[2025-06-04 09:24:52] Merged 93 bytes for 'is_nodejs'.
[2025-06-04 09:24:52] Merging 1 fragment for 'json'.
[2025-06-04 09:24:52] Merged 176 bytes for 'json'.
[2025-06-04 09:24:52] Merging 1 fragment for 'json_locations'.
[2025-06-04 09:24:52] Merged 82 bytes for 'json_locations'.
[2025-06-04 09:24:52] Merging 1 fragment for 'json_literals'.
[2025-06-04 09:24:52] Merged 102 bytes for 'json_literals'.
[2025-06-04 09:24:52] Merging 1 fragment for 'json_properties'.
[2025-06-04 09:24:52] Merged 106 bytes for 'json_properties'.
[2025-06-04 09:24:52] Merging 1 fragment for 'yaml_scalars'.
[2025-06-04 09:24:52] Merged 62 bytes for 'yaml_scalars'.
[2025-06-04 09:24:52] Merging 1 fragment for 'yaml'.
[2025-06-04 09:24:52] Merged 128 bytes for 'yaml'.
[2025-06-04 09:24:52] Merging 1 fragment for 'yaml_locations'.
[2025-06-04 09:24:52] Merged 48 bytes for 'yaml_locations'.
[2025-06-04 09:24:52] Merging 1 fragment for 'is_capture'.
[2025-06-04 09:24:52] Merged 50 bytes for 'is_capture'.
[2025-06-04 09:24:52] Merging 1 fragment for 'char_class_escape'.
[2025-06-04 09:24:52] Merged 35 bytes for 'char_class_escape'.
[2025-06-04 09:24:52] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-04 09:24:52] Merged 19 bytes for 'sourceLocationPrefix'.
[2025-06-04 09:24:52] Saving string and id pools to disk.
[2025-06-04 09:24:53] Finished importing TRAP files.
[2025-06-04 09:24:53] Read 44.14 MiB of uncompressed TRAP data.
[2025-06-04 09:24:53] Uncompressed relation data size: 13.56 MiB
[2025-06-04 09:24:53] Relation data size: 3.10 MiB (merge rate: 5.54 MiB/s)
[2025-06-04 09:24:53] String pool size: 4.78 MiB
[2025-06-04 09:24:53] ID pool size: 13.00 MiB
[2025-06-04 09:24:53] [PROGRESS] dataset import> Finished writing database (relations: 3.10 MiB; string pool: 4.78 MiB).
[2025-06-04 09:24:53] Pausing evaluation to close the cache at sequence stamp o+121
[2025-06-04 09:24:53] The disk cache is freshly trimmed; leave it be.
[2025-06-04 09:24:53] Unpausing evaluation
[2025-06-04 09:24:53] Plumbing command codeql dataset import completed.
[2025-06-04 09:24:53] [PROGRESS] database finalize> TRAP import complete (3.4s).
[2025-06-04 09:24:53] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db
[2025-06-04 09:24:53] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-04 09:24:53] [PROGRESS] database cleanup> TRAP files cleaned up (20ms).
[2025-06-04 09:24:53] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-04 09:24:53] [PROGRESS] database cleanup> Scratch directory cleaned up (0ms).
[2025-06-04 09:24:53] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript
[2025-06-04 09:24:53] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript.
[2025-06-04 09:24:53] Trimming disk cache at E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript\default\cache in mode trim.
[2025-06-04 09:24:53] Sequence stamp origin is -6041948278832312124
[2025-06-04 09:24:53] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-04 09:24:53] Unpausing evaluation
[2025-06-04 09:24:53] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-04 09:24:53] Unpausing evaluation
[2025-06-04 09:24:53] Trimming completed (3ms): Trimmed disposable data from cache.
[2025-06-04 09:24:53] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-04 09:24:53] The disk cache is freshly trimmed; leave it be.
[2025-06-04 09:24:53] Unpausing evaluation
[2025-06-04 09:24:53] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-04 09:24:53] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript
[2025-06-04 09:24:53] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript (4ms).
[2025-06-04 09:24:53] Plumbing command codeql dataset cleanup completed.
[2025-06-04 09:24:53] Plumbing command codeql database cleanup completed with status 0.
[2025-06-04 09:24:53] [PROGRESS] database finalize> Finished zipping source archive (254.39 KiB).
[2025-06-04 09:24:53] Plumbing command codeql database finalize completed.
[2025-06-04 09:24:53] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\8\codeql-exercise\node-db.
[2025-06-04 09:24:53] Terminating normally.
