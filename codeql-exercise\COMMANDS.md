# Essential CodeQL Commands

## 🚀 Quick Start

### **Automated Execution (Recommended)**

**Linux/macOS:**
```bash
./scripts/setup-codeql.sh      # Run complete exercise
./scripts/view-results.sh       # View all results
```

**Windows:**
```powershell
.\scripts\run-exercise.ps1     # Run complete exercise
.\scripts\view-results.ps1     # View all results
```

## 📋 Manual Commands for Each Activity

### **Activity 1 & 3: Vulnerable Code**
- ✅ `app.js` - Contains multiple vulnerabilities + `VAL_VAR="I am hacker's paradise"`
- ✅ `test-vulnerabilities.js` - Additional vulnerabilities + false positive scenarios

### **Activity 2: CodeQL CLI Scanning**
```bash
# Create database
codeql database create --language=javascript --source-root=. codeql-db

# Run default security queries (SARIF format)
codeql database analyze codeql-db javascript-security-and-quality.qls --format=sarif-latest --output=default-results.sarif

# Run default security queries (CSV format for easier viewing)
codeql database analyze codeql-db javascript-security-and-quality.qls --format=csv --output=results/default-security-scan.csv
```

### **Activity 4: Custom CodeQL Query**
```bash
# Run initial custom vulnerability query
codeql database analyze codeql-db queries/custom-vulnerability.ql --format=csv --output=results/custom-vulnerability.csv
```

### **Activity 5: Display Results**
```bash
# View default scan results (SARIF format)
cat default-results.sarif

# View default scan results (CSV format - easier to read)
cat results/default-security-scan.csv

# View custom query results
cat results/custom-vulnerability.csv

# Use automated results viewer (recommended)
./scripts/view-results.sh  # Linux/Mac
.\scripts\view-results.ps1  # Windows
```

### **Activity 6: False Positive Scenarios**
- ✅ Already implemented in `app.js` (masking functions)
- ✅ Already implemented in `test-vulnerabilities.js` (template examples)

### **Activity 7: Refined Query**
```bash
# Run refined query that avoids false positives
codeql database analyze codeql-db queries/refined-custom-vulnerability.ql --format=csv --output=results/refined-custom-vulnerability.csv
```

### **Activity 8: Final Validation**
```bash
# Compare results (manual approach)
echo "Initial query results:"
wc -l results/custom-vulnerability.csv

echo "Refined query results:"
wc -l results/refined-custom-vulnerability.csv

# View both results
cat results/custom-vulnerability.csv
cat results/refined-custom-vulnerability.csv

# Use automated validation (recommended)
./scripts/view-results.sh  # Linux/Mac - includes Activity 8 validation
.\scripts\view-results.ps1  # Windows - includes Activity 8 validation
```

## 🔍 Additional Vulnerability Detection

### **SQL Injection Detection**
```bash
codeql database analyze node-db queries/sql-injection.ql --format=csv --output=results/sql-injection.csv
```

### **Command Injection Detection**
```bash
codeql database analyze node-db queries/command-injection.ql --format=csv --output=results/command-injection.csv
```

## 📊 Expected Results

| Query | Expected Results | Purpose |
|-------|------------------|---------|
| `custom-vulnerability.ql` | 6 detections | Initial detection (includes false positives) |
| `refined-custom-vulnerability.ql` | 4 detections | Refined detection (excludes false positives) |
| `sql-injection.ql` | 0-2 detections | SQL injection patterns |
| `command-injection.ql` | 0 detections | Command injection patterns (may need fixing) |

## 🎯 Success Criteria

✅ **Activity 1**: Multiple vulnerabilities present in codebase  
✅ **Activity 2**: Default queries execute and find vulnerabilities  
✅ **Activity 3**: Custom pattern `VAL_VAR="I am hacker's paradise"` injected  
✅ **Activity 4**: Custom query detects injected vulnerability  
✅ **Activity 5**: Results displayed via CLI  
✅ **Activity 6**: False positive scenarios implemented  
✅ **Activity 7**: Refined query reduces false positives  
✅ **Activity 8**: Final validation shows improvement  

## 🔧 Troubleshooting

**Database creation fails:**
```bash
rm -rf node-db  # Remove existing database
# Then retry database creation
```

**Query execution fails:**
- Check query syntax in `.ql` files
- Ensure database exists: `ls -la node-db/`
- Verify CodeQL CLI is installed: `codeql --version`

**No results found:**
- This may be expected for some queries
- Check that vulnerable code patterns exist in source files
- Verify query logic matches vulnerability patterns
