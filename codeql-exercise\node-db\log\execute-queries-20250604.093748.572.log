[2025-06-04 09:37:48] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --output=E:\advance_javascript\codeQL\8\codeql-exercise\custom-results.bqrs -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:37:48] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:37:49] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:37:49] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\custom-vulnerability.ql"
                      ]
[2025-06-04 09:37:49] Refusing fancy output: The terminal is not an xterm: 
[2025-06-04 09:37:49] Creating executor with 1 threads.
[2025-06-04 09:37:49] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:37:49] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:37:49] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:37:49] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise"
                      ]
[2025-06-04 09:37:49] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 09:37:49] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 09:37:49] [SPAMMY] resolve extensions-by-pack> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 1] security-queries: 1.0.0
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-04 09:37:50] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-04 09:37:50] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-04 09:37:50] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 09:37:50] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 09:37:50] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql --format=json
[2025-06-04 09:37:50] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:37:50] [DETAILS] resolve library-path> Found enclosing pack 'security-queries' at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:37:50] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 09:37:50] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml.
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 1] security-queries: 1.0.0
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 09:37:50] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 09:37:50] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise resolved OK.
[2025-06-04 09:37:50] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-04 09:37:50] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "security-queries\\queries\\custom-vulnerability.ql",
                        "qlPackName" : "security-queries"
                      }
[2025-06-04 09:37:50] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:37:50] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:37:52] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql hashes to 633a3e44f6c1d7c4c403761275bd7ca5.
[2025-06-04 09:37:52] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:37:52] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 09:37:53] ExternalModuleBindingPass ...
[2025-06-04 09:37:54] ExternalModuleBindingPass time: 00:01.395
[2025-06-04 09:37:54] CollectInstantiationsPass ...
[2025-06-04 09:37:55] CollectInstantiationsPass time: 00:00.696
[2025-06-04 09:37:55] Ql checks ...
[2025-06-04 09:38:01] Ql checks time: 00:06.347
[2025-06-04 09:38:11] Compilation pipeline
[2025-06-04 09:38:12] Type Inference ...
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,232,360
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,695,2112
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-04 09:38:12] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1014,46301
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,14,14
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,26,26
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,12,15
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,22,32
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,22,22
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,11,11
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,12,12
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,16,18
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,7,10
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-04 09:38:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-04 09:38:15] Type Inference time: 00:03.494
[2025-06-04 09:38:16] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:38:16] Processing compilation pipeline
[2025-06-04 09:38:16] Started compiling a query
[2025-06-04 09:38:17] Initialising compiler...
[2025-06-04 09:38:17] 	 ... compiler initialised
[2025-06-04 09:38:17] About to start query optimisation
[2025-06-04 09:38:17] Compilation cache hit - skipping compilation.
[2025-06-04 09:38:17] Compilation cache hit - skipping compilation.
[2025-06-04 09:38:17] Compilation cache hit - skipping compilation.
[2025-06-04 09:38:17] Compilation cache miss for 2a474d387b7a1b02c123131a1f6e11b0.
[2025-06-04 09:38:19] Stored compiled program for 2a474d387b7a1b02c123131a1f6e11b0.
[2025-06-04 09:38:19] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-04 09:38:19] CSV_COMPILATION: 241,401,372,1655
[2025-06-04 09:38:19] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:38:19] [PROGRESS] execute queries> [1/1 comp 29.1s] Compiled E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 09:38:20] [PROGRESS] execute queries> Starting evaluation of security-queries\queries\custom-vulnerability.ql.
[2025-06-04 09:38:20] Starting evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:38:20] (0s) Start query execution
[2025-06-04 09:38:20] (0s) Beginning execution of E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s with 75908 rows and digest f21e59taour23ovre1f9u53nu74.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 with 34962 rows and digest fea75dtn6e02l4jtpj0fir9mba6.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me with 61500 rows and digest 1d4de0tjs28ikf4osuddegn1ppe.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 385 rows and digest 1999b8doaea4airlo4e9tr0a623.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 192887 rows and digest bf1ab4ujhkf0don2emb08cu4701.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 237718 rows and digest e2e365km2cv53omf8vjemqdbtr1.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 with empty relation.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 with 76021 rows and digest 978f95qfnc1amiq09gqe53hk4k3.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air with empty relation.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j with 55376 rows and digest 6f6f02aupa0djiruh6s5gbgfrt6.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre with 108660 rows and digest 15f8bc8s4t79u5lv8seojntm508.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 with 3906 rows and digest 3444e3v02bl020ava9qii82e3c3.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 with 147 rows and digest 9221c9o1tle1tvjggqsraki2ve9.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 with 447 rows and digest 43325c7h18ff672hcvcd7qm0tkd.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::ConstantString#e55e40cc/1@43d594jb with 147 rows and digest 607b8ehoc6an67h29jl5uhfdfl7.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 with empty relation.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 with empty relation.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn with empty relation.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv with empty relation.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 with 5 rows and digest b418cdppe5vhu6uicbdbvhi2d71.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go with 289 rows and digest 681059jinboal2khcnn5qgtb7p2.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t with empty relation.
[2025-06-04 09:38:20] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t with empty relation.
[2025-06-04 09:38:20] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 09:38:20] (0s)  >>> Created relation literals/3@f05366h0 with 35271 rows and digest abee41aghqnk1iifnu6cd40i64c.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate literals_20#join_rhs/2@2856f00u
[2025-06-04 09:38:20] (0s)  >>> Created relation literals_20#join_rhs/2@2856f00u with 35271 rows and digest b64a885cknnrqpeoohi2lobda72.
[2025-06-04 09:38:20] (0s)  >>> Created relation exprs/5@a1831agj with 61500 rows and digest f83d74t85c4ldl9dpg3028d06p5.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate exprs_10#join_rhs/2@27dbbd1m
[2025-06-04 09:38:20] (0s)  >>> Created relation exprs_10#join_rhs/2@27dbbd1m with 61500 rows and digest 6da27335elt5b4ik9drf3iig65c.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Literal#62045a1b/1@81201fg9
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Literal#62045a1b/1@81201fg9 with 296 rows and digest 8c8b4e1u6vkn0co1nnlbqs4p75d.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih with 296 rows and digest 6a513al0b7tpveio3enc8kfubp5.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate customvulnerability::isSensitiveString/1#3d1e9f93/1@aac22bqp
[2025-06-04 09:38:20] (0s)  >>> Created relation customvulnerability::isSensitiveString/1#3d1e9f93/1@aac22bqp with 8 rows and digest 64ee72i6ut8ma8cqafhvhj8al48.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::InvokeExpr#b783e907#b/1@350cd74r
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::InvokeExpr#b783e907#b/1@350cd74r with 1 rows and digest 3e9154jrfu7vlcehi1s1mdkc6ve.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/2@8a368a35
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb/2@8a368a35 with 1 rows and digest 29c5afjkl513ufvrjfsa2ho0g9a.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs/2@5b27a4fe
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs/2@5b27a4fe with 1 rows and digest 28518ergkqh1n2qa41v5imsskp4.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate exprs_230#join_rhs/3@dfee24vq
[2025-06-04 09:38:20] (0s)  >>> Created relation exprs_230#join_rhs/3@dfee24vq with 61500 rows and digest 121fe0ao19alrk2qhpb5jmfd474.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Assignment#b84ed089/1@16fa64dv
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Assignment#b84ed089/1@16fa64dv with 4035 rows and digest 0ba380natrj0vi7dd9nomsk7go4.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e with 4035 rows and digest 0483269apge48a8mhobr9ok3vm8.
[2025-06-04 09:38:20] (0s)  >>> Created relation stmts/5@bcd8ccid with 14276 rows and digest ef40790u9odu79q3hd2u9u8ntr4.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate stmts_10#join_rhs/2@71c68aq8
[2025-06-04 09:38:20] (0s)  >>> Created relation stmts_10#join_rhs/2@71c68aq8 with 14276 rows and digest b9a0380171luskbthbsaf8nihg7.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i1#5b8b2cqg (iteration 1)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 134 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i2#5b8b2cqg (iteration 2)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 134 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i3#5b8b2cqg (iteration 3)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 134 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i4#5b8b2cqg (iteration 4)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 92 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i5#5b8b2cqg (iteration 5)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 65 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i6#5b8b2cqg (iteration 6)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 41 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i7#5b8b2cqg (iteration 7)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 23 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i8#5b8b2cqg (iteration 8)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 12 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i9#5b8b2cqg (iteration 9)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 8 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i10#5b8b2cqg (iteration 10)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i11#5b8b2cqg (iteration 11)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i12#5b8b2cqg (iteration 12)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i13#5b8b2cqg (iteration 13)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i14#5b8b2cqg (iteration 14)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i15#5b8b2cqg (iteration 15)
[2025-06-04 09:38:20] (0s) 			 - #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@i16#5b8b2cqg (iteration 16)
[2025-06-04 09:38:20] (0s) Empty delta for #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:20] (0s) Accumulating deltas
[2025-06-04 09:38:20] (0s)  >>> Created relation #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf/2@5b8b2cqg with 662 rows and digest 68e690tkd4nm7cij0mrmvr6gntd.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_10#join_rhs/2@c44ab3u1
[2025-06-04 09:38:20] (0s)  >>> Created relation #AST::AstNode.getParent/0#dispred#21e1ccebPlus#bf_10#join_rhs/2@c44ab3u1 with 662 rows and digest 33f5f9udc0rct8ntu7ndf3q7tm3.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate @function/1@8ce12abm
[2025-06-04 09:38:20] (0s)  >>> Created relation @function/1@8ce12abm with 3906 rows and digest 9accd3io59tprva7qjah572c5fb.
[2025-06-04 09:38:20] (0s)  >>> Created relation properties/5@1908b117 with 132 rows and digest bab3982mukhq450ff251k976gd7.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Assignment.getLhs/0#dispred#9387a69c/2@3ed53867
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Assignment.getLhs/0#dispred#9387a69c/2@3ed53867 with 4035 rows and digest 121e52ot23oqr8s5obv0421sfr2.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::Assignment.getLhs/0#dispred#9387a69c_1#join_rhs/1@dff072kh
[2025-06-04 09:38:20] (0s)  >>> Created relation Expr::Assignment.getLhs/0#dispred#9387a69c_1#join_rhs/1@dff072kh with 4035 rows and digest 32facbdpjbadllethkmvi6es6i0.
[2025-06-04 09:38:20] (0s) Starting to evaluate predicate Expr::PropAccess.getPropertyName/0#dispred#80dcc485#bf/2@603ce15t
[2025-06-04 09:38:21] (0s)  >>> Created relation Expr::PropAccess.getPropertyName/0#dispred#80dcc485#bf/2@603ce15t with 4025 rows and digest c51180j8a8si1umhmoqeehfbaff.
[2025-06-04 09:38:21] (0s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@0b6c7fvm
[2025-06-04 09:38:21] (0s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@0b6c7fvm with 4035 rows and digest 62d161o0m9dsatncptk9c31lqpc.
[2025-06-04 09:38:21] (0s) Starting to evaluate predicate exprs_032#join_rhs/3@30fda5lr
[2025-06-04 09:38:21] (1s)  >>> Created relation exprs_032#join_rhs/3@30fda5lr with 61500 rows and digest fbaf47vfruub7rmboh4vg49mj55.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_10#join_rhs/2@79d996jd
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_10#join_rhs/2@79d996jd with 61500 rows and digest 2e11dcr8uip9q9gjik4445pk6ce.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate properties_10#join_rhs/2@5d3367rj
[2025-06-04 09:38:21] (1s)  >>> Created relation properties_10#join_rhs/2@5d3367rj with 132 rows and digest 97646f9f2cslg7f8mqr0fpsvq08.
[2025-06-04 09:38:21] (1s)  >>> Created relation typeexprs/5@5ef91d9v with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Inferred that typeexprs_0#antijoin_rhs/1@e1d4c0cm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:38:21] (1s) Inferred that typeexprs_10#join_rhs/2@af1f3114 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate @interface_definition/1@9a73d7n8
[2025-06-04 09:38:21] (1s)  >>> Created relation @interface_definition/1@9a73d7n8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Classes::ClassOrInterface#9b4808c4/1@4d8d6b9c
[2025-06-04 09:38:21] (1s)  >>> Created relation Classes::ClassOrInterface#9b4808c4/1@4d8d6b9c with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration#5cd163da/1@9e0d13mo is empty, due to Classes::ClassOrInterface#9b4808c4/1@4d8d6b9c.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration.getDeclaringType/0#01768920/2@3c2770ck is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MethodDeclaration#65d11f8e/1@9c500cqs is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that Classes::ParameterField#eec2307f/1@0b7c26pk is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that _Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclaration.isConcrete/0#dispred#23806190#b#shared/1@d324c65t is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that _Classes::MemberDeclaration#5cd163da_Classes::ParameterField#eec2307f#shared/1@0592ab71 is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration.getNameExpr/0#dispred#78be4cce/2@5a7e0av5 is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberSignature#1c3a3175#b/1@dbea1ate is empty, due to Classes::MemberDeclaration#5cd163da/1@9e0d13mo.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration.getDeclaringType/0#dispred#022e924a/2@43c6eav8 is empty, due to Classes::MemberDeclaration.getDeclaringType/0#01768920/2@3c2770ck.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MethodDeclaration.getBody/0#dispred#075fc454/2@9da060e2 is empty, due to Classes::MethodDeclaration#65d11f8e/1@9c500cqs.
[2025-06-04 09:38:21] (1s) Inferred that _Classes::MemberDeclaration#5cd163da__Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclarati__#antijoin_rhs/1@88d55euf is empty, due to _Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclaration.isConcrete/0#dispred#23806190#b#shared/1@d324c65t.
[2025-06-04 09:38:21] (1s) Inferred that __Classes::MemberDeclaration#5cd163da__Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclarat__#shared/1@57b792jl is empty, due to _Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclaration.isConcrete/0#dispred#23806190#b#shared/1@d324c65t.
[2025-06-04 09:38:21] (1s) Inferred that _Classes::MemberDeclaration.isConcrete/0#dispred#23806190#b__Classes::MemberDeclaration#5cd163da_Cla__#antijoin_rhs/1@24c08eb1 is empty, due to _Classes::MemberDeclaration#5cd163da_Classes::ParameterField#eec2307f#shared/1@0592ab71.
[2025-06-04 09:38:21] (1s) Inferred that _Classes::MemberDeclaration#5cd163da_Classes::MemberDeclaration.getNameExpr/0#dispred#78be4cce_is_co__#antijoin_rhs/2@7d4f5a83 is empty, due to Classes::MemberDeclaration.getNameExpr/0#dispred#78be4cce/2@5a7e0av5.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration.getName/0#dispred#6b69f910/2@9ea942ri is empty, due to Classes::MemberDeclaration.getNameExpr/0#dispred#78be4cce/2@5a7e0av5.
[2025-06-04 09:38:21] (1s) Inferred that Classes::CallSignature#99ea87ee/1@c98278qb is empty, due to Classes::MemberSignature#1c3a3175#b/1@dbea1ate.
[2025-06-04 09:38:21] (1s) Inferred that Classes::IndexSignature#d999d3a1/1@4f4cb255 is empty, due to Classes::MemberSignature#1c3a3175#b/1@dbea1ate.
[2025-06-04 09:38:21] (1s) Inferred that _AST::AstNode.getTopLevel/0#dispred#10343479_AST::AstNode.isAmbientInternal/0#ebd19382_AST::isAmbien__#antijoin_rhs/1@db834902 is empty, due to __Classes::MemberDeclaration#5cd163da__Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclarat__#shared/1@57b792jl.
[2025-06-04 09:38:21] (1s) Inferred that Classes::MemberDeclaration.isConcrete/0#dispred#23806190#b/1@ded1dflg is empty, due to __Classes::MemberDeclaration#5cd163da__Classes::MemberDeclaration#5cd163da_m#Classes::MemberDeclarat__#shared/1@57b792jl.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate @varref/1@ac5ae271
[2025-06-04 09:38:21] (1s)  >>> Created relation @varref/1@ac5ae271 with 16300 rows and digest ed34d8jls3stle01soc4up906c5.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#06e2b126/2@454fa2a6
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::VariableDeclarator.getInit/0#06e2b126/2@454fa2a6 with 163 rows and digest 9c49705gs6p4iv040lqjjkr3aj8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property#e5a4273a/1@cd6f62vd
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property#e5a4273a/1@cd6f62vd with 124 rows and digest 874da91tjadm3h9a06k1f6nn04d.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property.getNameExpr/0#dispred#e65c960a/2@fec9a6fv
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property.getNameExpr/0#dispred#e65c960a/2@fec9a6fv with 124 rows and digest 310333ce1tu2nhbvhdn42vkvor4.
[2025-06-04 09:38:21] (1s)  >>> Created relation is_computed/1@47913233 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Inferred that _Expr::Property#e5a4273a_Expr::Property.getNameExpr/0#dispred#e65c960a_is_computed#antijoin_rhs/2@bcd5a3th is empty, due to is_computed/1@47913233.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property.getName/0#dispred#212cae8f/2@bbf760a1
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property.getName/0#dispred#212cae8f/2@bbf760a1 with 124 rows and digest c646e6k1pp9crtbmvgn6scu5219.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property.getInit/0#e4cc9ea0/2@11919er1
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property.getInit/0#e4cc9ea0/2@11919er1 with 124 rows and digest b96d84lkcv79omqsrl27nkeduh6.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate properties_30#join_rhs/2@894cc42q
[2025-06-04 09:38:21] (1s)  >>> Created relation properties_30#join_rhs/2@894cc42q with 132 rows and digest 8694386q4kd4ss9t3pf5n8nv7ff.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate @property_accessor/1@dc1620u6
[2025-06-04 09:38:21] (1s)  >>> Created relation @property_accessor/1@dc1620u6 with 14 rows and digest 914629h29a5ljg19luriehmea1d.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::PropertyAccessor#c8c53e42/1@9421cb5d
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::PropertyAccessor#c8c53e42/1@9421cb5d with 14 rows and digest 914629h29a5ljg19luriehmea1d.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property.getInit/0#dispred#58a697bb/2@13ffc54p
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property.getInit/0#dispred#58a697bb/2@13ffc54p with 124 rows and digest b96d84lkcv79omqsrl27nkeduh6.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Property.getInit/0#dispred#58a697bb_10#join_rhs/2@5d8db07h
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Property.getInit/0#dispred#58a697bb_10#join_rhs/2@5d8db07h with 124 rows and digest 97650dkgc3k2kidrgguisk2d0ob.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::BindingPattern.getName/0#dispred#38c8fb21/2@7835c9kj
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::BindingPattern.getName/0#dispred#38c8fb21/2@7835c9kj with 16300 rows and digest 3a60e5cqql8m2603388bs6p2ab6.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::BindingPattern#efe8ec12#b/1@c3b1ff9s
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::BindingPattern#efe8ec12#b/1@c3b1ff9s with 16306 rows and digest d9fc58a4riirbjaav968o0m203a.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::VariableDeclarator.getBindingPattern/0#dispred#376645aa/2@f6fabc4m
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::VariableDeclarator.getBindingPattern/0#dispred#376645aa/2@f6fabc4m with 290 rows and digest 5ff0f2l6lm18cn7ujcfi960613d.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Identifier#299f46d8/1@52162dj4
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Identifier#299f46d8/1@52162dj4 with 34962 rows and digest be4798f7lm93pqua6lrgj05bkn8.
[2025-06-04 09:38:21] (1s)  >>> Created relation comments/5@047a73l1 with 10629 rows and digest d1135dre6j64r0gd0f27qpi4gl4.
[2025-06-04 09:38:21] (1s)  >>> Created relation jsdoc/3@a172dbu7 with 9175 rows and digest d621f15eoa1c48gqem08u7ed368.
[2025-06-04 09:38:21] (1s)  >>> Created relation jsdoc_tags/5@1637af13 with 18122 rows and digest a40d3frdjcanr2e8ks2nstfe07a.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate jsdoc_tags_12#join_rhs/2@f0316em6
[2025-06-04 09:38:21] (1s)  >>> Created relation jsdoc_tags_12#join_rhs/2@f0316em6 with 15744 rows and digest 2fc8e4osfom1p0fp5a2qkap1vqa.
[2025-06-04 09:38:21] (1s)  >>> Created relation is_externs/1@68886e91 with 110 rows and digest 63269arci3euoe8kacmbrjubg67.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate AST::TopLevel.isExterns/0#dispred#6ecfdfe6/1@52be5e5o
[2025-06-04 09:38:21] (1s)  >>> Created relation AST::TopLevel.isExterns/0#dispred#6ecfdfe6/1@52be5e5o with 110 rows and digest 63269arci3euoe8kacmbrjubg67.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate AST::AstNode.inExternsFile/0#dispred#a8c15025#b/1@f96fc81b
[2025-06-04 09:38:21] (1s)  >>> Created relation AST::AstNode.inExternsFile/0#dispred#a8c15025#b/1@f96fc81b with 74703 rows and digest edaf399jj9ean0ecehg043s156e.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _AST::AstNode.inExternsFile/0#dispred#a8c15025#b_Expr::Identifier#299f46d8_Variables::VariableDeclar__#shared/1@bfb99flc
[2025-06-04 09:38:21] (1s)  >>> Created relation _AST::AstNode.inExternsFile/0#dispred#a8c15025#b_Expr::Identifier#299f46d8_Variables::VariableDeclar__#shared/1@bfb99flc with 236 rows and digest 16bd8402eunn01hgj7fkp8jojs8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s
[2025-06-04 09:38:21] (1s)  >>> Created relation CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::DeclStmt#28398b2c/1@fb8d64fs
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::DeclStmt#28398b2c/1@fb8d64fs with 290 rows and digest 7145315gffie3cfcegkst59912e.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate exprs_12034#join_rhs/5@d7abdd0t
[2025-06-04 09:38:21] (1s)  >>> Created relation exprs_12034#join_rhs/5@d7abdd0t with 61500 rows and digest 460adak2j5nagtfpsn5tvg98stb.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::DeclStmt.getADecl/0#dispred#b3d64307/2@2c1ebas8
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::DeclStmt.getADecl/0#dispred#b3d64307/2@2c1ebas8 with 290 rows and digest 42401ammptmr3p0tdgqqfpn71u9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::DeclStmt.getADecl/0#dispred#b3d64307_10#join_rhs/2@331cdfid
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::DeclStmt.getADecl/0#dispred#b3d64307_10#join_rhs/2@331cdfid with 290 rows and digest 868a90svl67hj1ug0npi7hutktd.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Externs::hasTypedefAnnotation/1#626d2238#b/1@d91044rj
[2025-06-04 09:38:21] (1s)  >>> Created relation Externs::hasTypedefAnnotation/1#626d2238#b/1@d91044rj with 82 rows and digest 286847r55fumfhqkqbjond81dr8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Externs::hasTypedefAnnotation/1#626d2238#b_Stmt::DeclStmt.getADecl/0#dispred#b3d64307_10#join_rhs____#antijoin_rhs/1@fd650ep2
[2025-06-04 09:38:21] (1s)  >>> Created relation _Externs::hasTypedefAnnotation/1#626d2238#b_Stmt::DeclStmt.getADecl/0#dispred#b3d64307_10#join_rhs____#antijoin_rhs/1@fd650ep2 with 82 rows and digest c094bf1mnfnq1783al1t7uernd9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Externs::ExternalGlobalVarDecl#c56ad568/1@d353f2aa
[2025-06-04 09:38:21] (1s)  >>> Created relation Externs::ExternalGlobalVarDecl#c56ad568/1@d353f2aa with 154 rows and digest 894951r3o215e6asn54jl8oj4v2.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#dispred#92991ad2/2@8db11b86
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::VariableDeclarator.getInit/0#dispred#92991ad2/2@8db11b86 with 163 rows and digest 9c49705gs6p4iv040lqjjkr3aj8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#antijoin_rhs/1@a4ffd51k
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#antijoin_rhs/1@a4ffd51k with 163 rows and digest 695802p2d7t9j89qrg3174cq4g9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate exprs_12304#join_rhs/5@5428d56d
[2025-06-04 09:38:21] (1s)  >>> Created relation exprs_12304#join_rhs/5@5428d56d with 61500 rows and digest ad48c8jogqsiaqp5cm4m8dkfjub.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate exprs_01243#join_rhs/5@47b36007
[2025-06-04 09:38:21] (1s)  >>> Created relation exprs_01243#join_rhs/5@47b36007 with 61500 rows and digest 2a6f15570i7kij84llhj0aj03sc.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Functions::Function.getIdentifier/0#dispred#a47930cd/2@5b2845qm
[2025-06-04 09:38:21] (1s)  >>> Created relation Functions::Function.getIdentifier/0#dispred#a47930cd/2@5b2845qm with 804 rows and digest a2e636lpe3u6vp9uihuet060vb8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Functions::Function.getIdentifier/0#dispred#a47930cd_0#antijoin_rhs/1@025615pb
[2025-06-04 09:38:21] (1s)  >>> Created relation Functions::Function.getIdentifier/0#dispred#a47930cd_0#antijoin_rhs/1@025615pb with 804 rows and digest 53c5d1gp8f10c1c9bblde9iv3ud.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Inferred that Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3 is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m.
[2025-06-04 09:38:21] (1s) Inferred that _Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr:__#join_rhs/2@619dfbbb is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::EnhancedForLoop#929242da/1@a656dc3j
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::EnhancedForLoop#929242da/1@a656dc3j with 2 rows and digest 82e165lam3md374eicsotet5mf0.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate stmts_230#join_rhs/3@958ca2ec
[2025-06-04 09:38:21] (1s)  >>> Created relation stmts_230#join_rhs/3@958ca2ec with 14276 rows and digest 1df45dologlarcfeqgb4tislfe4.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate m#Stmt::ExprOrVarDecl#10e3e413#b/1@43bbf50r
[2025-06-04 09:38:21] (1s)  >>> Created relation m#Stmt::ExprOrVarDecl#10e3e413#b/1@43bbf50r with 2 rows and digest 058414si4hlhvnj3j259kn97a63.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::ExprOrVarDecl#10e3e413#b/1@70f77akj
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::ExprOrVarDecl#10e3e413#b/1@70f77akj with 2 rows and digest 058414si4hlhvnj3j259kn97a63.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::EnhancedForLoop.getIterator/0#dispred#986f0c16/2@7245fe64
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::EnhancedForLoop.getIterator/0#dispred#986f0c16/2@7245fe64 with 2 rows and digest 86ed27gfkpiud50ijvcc0obfg8f.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Stmt::EnhancedForLoop.getIteratorExpr/0#dispred#f127b527/2@f133e5h4
[2025-06-04 09:38:21] (1s)  >>> Created relation Stmt::EnhancedForLoop.getIteratorExpr/0#dispred#f127b527/2@f133e5h4 with 2 rows and digest f2b459mrnbtvb92pg47gog1ndhb.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate m#Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/1@i1#341af1av (iteration 1)
[2025-06-04 09:38:21] (1s) 			 - m#Expr::Expr.stripParens/0#dispred#5fca6ccf#bf_delta has 4037 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate m#Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/1@i2#341af1av (iteration 2)
[2025-06-04 09:38:21] (1s) Empty delta for m#Expr::Expr.stripParens/0#dispred#5fca6ccf#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:21] (1s) Accumulating deltas
[2025-06-04 09:38:21] (1s)  >>> Created relation m#Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/1@341af1av with 4037 rows and digest 7db22avghqi1c5tfp47ruad8452.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#4/1@fd80deh5
[2025-06-04 09:38:21] (1s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#4/1@fd80deh5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/2@i1#aea4delo (iteration 1)
[2025-06-04 09:38:21] (1s) 			 - Expr::Expr.stripParens/0#dispred#5fca6ccf#bf_delta has 4037 rows (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/2@i2#aea4delo (iteration 2)
[2025-06-04 09:38:21] (1s) Empty delta for Expr::Expr.stripParens/0#dispred#5fca6ccf#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-04 09:38:21] (1s) Accumulating deltas
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::Expr.stripParens/0#dispred#5fca6ccf#bf/2@aea4delo with 4037 rows and digest dab1939qqfdvqa7bbkq7vp09o6c.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate TypeScript::EnumMember.getInitializer/0#dispred#b4636f29/2@74fafa29
[2025-06-04 09:38:21] (1s)  >>> Created relation TypeScript::EnumMember.getInitializer/0#dispred#b4636f29/2@74fafa29 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Inferred that TypeScript::EnumMember.getInitializer/0#dispred#b4636f29_0#antijoin_rhs/1@f8d459kl is empty, due to TypeScript::EnumMember.getInitializer/0#dispred#b4636f29/2@74fafa29.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate TypeScript::EnumMember.getIdentifier/0#dispred#8bd7916f/2@c597f7ue
[2025-06-04 09:38:21] (1s)  >>> Created relation TypeScript::EnumMember.getIdentifier/0#dispred#8bd7916f/2@c597f7ue with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate DefUse::defn/3#7e397e61/3@3d74bf5q
[2025-06-04 09:38:21] (1s)  >>> Created relation DefUse::defn/3#7e397e61/3@3d74bf5q with 5002 rows and digest 717a0cf10cvllbqvce2peie0sq8.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::Parameterized#305a8e1f/1@dccda2nc
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::Parameterized#305a8e1f/1@dccda2nc with 3915 rows and digest a2e7c4pb7gc0n2rs1737idun023.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::Parameter#a0c1c8e9#b/1@c8c532rp
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::Parameter#a0c1c8e9#b/1@c8c532rp with 4961 rows and digest afa308b5175ki1j0e4oj70c4fa2.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Expr::ComprehensionBlock#2c083548/1@84f18d4l
[2025-06-04 09:38:21] (1s)  >>> Created relation Expr::ComprehensionBlock#2c083548/1@84f18d4l with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate project#DefUse::defn/3#7e397e61/2@ad637cso
[2025-06-04 09:38:21] (1s)  >>> Created relation project#DefUse::defn/3#7e397e61/2@ad637cso with 5002 rows and digest 3323937l8h3tbhfdgcme3vp7ird.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate DefUse::defn/2#e1c951ea#bf/2@b36aacdq
[2025-06-04 09:38:21] (1s)  >>> Created relation DefUse::defn/2#e1c951ea#bf/2@b36aacdq with 9967 rows and digest 5c258f5fshvu0rsgklbv7sg6vb1.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate project#DefUse::defn/2#e1c951ea#bf/1@4e3fbfvj
[2025-06-04 09:38:21] (1s)  >>> Created relation project#DefUse::defn/2#e1c951ea#bf/1@4e3fbfvj with 9965 rows and digest 71dd8bfqobn79t50mnc42c1jrv2.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Variables::TDestructuringPattern#fc4a8e30/1@d86ad7hl
[2025-06-04 09:38:21] (1s)  >>> Created relation Variables::TDestructuringPattern#fc4a8e30/1@d86ad7hl with 6 rows and digest 1210eapjit8q2nuuondbor4o3n0.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate DefUse::VarDef.getSource/0#dispred#44462829/2@5a89060r
[2025-06-04 09:38:21] (1s)  >>> Created relation DefUse::VarDef.getSource/0#dispred#44462829/2@5a89060r with 4996 rows and digest 1d4ea93klq7n9on2a7kgoec1k38.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate project#DefUse::VarDef.getSource/0#dispred#44462829/1@79c39egk
[2025-06-04 09:38:21] (1s)  >>> Created relation project#DefUse::VarDef.getSource/0#dispred#44462829/1@79c39egk with 4996 rows and digest 420a267m4n4bm97m8fg5heh0600.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate DefUse::VarDef.getTarget/0#dispred#7342d618#bf/2@4876a475
[2025-06-04 09:38:21] (1s)  >>> Created relation DefUse::VarDef.getTarget/0#dispred#7342d618#bf/2@4876a475 with 4996 rows and digest d06654l0c7gj11mbns3t6fqmile.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate DefUse::VarDef.getSource/0#dispred#44462829_10#join_rhs/2@0e66e00o
[2025-06-04 09:38:21] (1s)  >>> Created relation DefUse::VarDef.getSource/0#dispred#44462829_10#join_rhs/2@0e66e00o with 4996 rows and digest 274fb09hl28cu9ijdu190p3o9i0.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Functions::Function.getName/0#ea5b77b1/2@0b0bd2dc
[2025-06-04 09:38:21] (1s)  >>> Created relation Functions::Function.getName/0#ea5b77b1/2@0b0bd2dc with 3889 rows and digest a41eed8nm1r6j7hio7270gpn5a2.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Externs::ExternalGlobalFunctionDecl#9d74009a/1@11a44cmn
[2025-06-04 09:38:21] (1s)  >>> Created relation Externs::ExternalGlobalFunctionDecl#9d74009a/1@11a44cmn with 789 rows and digest c15e01dqk2lranou1a1eeltr2cd.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Functions::Function.getName/0#dispred#921d00d3/2@7ac4d9q2
[2025-06-04 09:38:21] (1s)  >>> Created relation Functions::Function.getName/0#dispred#921d00d3/2@7ac4d9q2 with 3889 rows and digest a41eed8nm1r6j7hio7270gpn5a2.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate customvulnerability::isInMaskingContext/1#50bfa211/1@ea8139vl
[2025-06-04 09:38:21] (1s)  >>> Created relation customvulnerability::isInMaskingContext/1#50bfa211/1@ea8139vl with 4 rows and digest 013b32r33joo1on2cre3k026vf5.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#antijoin_rhs_customvulnerability::isInMa__#shared/1@51cb73mh
[2025-06-04 09:38:21] (1s)  >>> Created relation _Variables::VariableDeclarator.getInit/0#dispred#92991ad2_1#antijoin_rhs_customvulnerability::isInMa__#shared/1@51cb73mh with 1 rows and digest c06202dkr8dp7mibihgjfuf25p9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#dis__#antijoin_rhs/1@b7483eep
[2025-06-04 09:38:21] (1s)  >>> Created relation _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#dis__#antijoin_rhs/1@b7483eep with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate __Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#di__#shared/1@e043970v
[2025-06-04 09:38:21] (1s)  >>> Created relation __Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Variables::VariableDeclarator.getInit/0#di__#shared/1@e043970v with 1 rows and digest c06202dkr8dp7mibihgjfuf25p9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs___Expr::Assignment.getRhs/0#dispre__#antijoin_rhs/1@f0c39830
[2025-06-04 09:38:21] (1s)  >>> Created relation _Expr::InvokeExpr.getAnArgument/0#dispred#c371933f#fb_10#join_rhs___Expr::Assignment.getRhs/0#dispre__#antijoin_rhs/1@f0c39830 with 1 rows and digest c06202dkr8dp7mibihgjfuf25p9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate #select/2@687205ji
[2025-06-04 09:38:21] (1s)  >>> Created relation #select/2@687205ji with 6 rows and digest 1372e1n1f4st5ka2opbv3h40a2e.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate exprs_0#antijoin_rhs/1@d222edj4
[2025-06-04 09:38:21] (1s)  >>> Created relation exprs_0#antijoin_rhs/1@d222edj4 with 61500 rows and digest 6dc383aq5t2h0sumsk6vbc39u36.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _#select_exprs_exprs_0#antijoin_rhs#shared/3@da952faf
[2025-06-04 09:38:21] (1s)  >>> Created relation _#select_exprs_exprs_0#antijoin_rhs#shared/3@da952faf with 6 rows and digest d41540pjk33f5d28re88505l209.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@046a48aa
[2025-06-04 09:38:21] (1s)  >>> Created relation _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@046a48aa with 6 rows and digest 2b9bd4u4mbb1ainl0pl5rl2u60c.
[2025-06-04 09:38:21] (1s)  >>> Created relation files/2@ec93749g with 115 rows and digest 1da27am6grhuehngssp5m5htnd9.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate project##select/1@6b0542f6
[2025-06-04 09:38:21] (1s)  >>> Created relation project##select/1@6b0542f6 with 6 rows and digest f8650aum43tjggvd3608o3405j3.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a#bfffff/6@339fc0p0
[2025-06-04 09:38:21] (1s)  >>> Created relation Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a#bfffff/6@339fc0p0 with 6 rows and digest c4da60dop6evoelkvoh3fc6cuh7.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a#bfffff__Locations::getLocatableLocation/1#__#antijoin_rhs/3@f9d4d02r
[2025-06-04 09:38:21] (1s)  >>> Created relation _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a#bfffff__Locations::getLocatableLocation/1#__#antijoin_rhs/3@f9d4d02r with 6 rows and digest d41540pjk33f5d28re88505l209.
[2025-06-04 09:38:21] (1s) Starting to evaluate predicate #select#query/8@7a895ffb
[2025-06-04 09:38:21] (1s)  >>> Created relation #select#query/8@7a895ffb with 6 rows and digest 1135a7k6ph6q0tav29jlob5q6s5.
[2025-06-04 09:38:21] (1s) Query done
[2025-06-04 09:38:21] (1s) Sequence stamp origin is -6041944945912711667
[2025-06-04 09:38:21] (1s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-04 09:38:21] (1s) Unpausing evaluation
[2025-06-04 09:38:21] Evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql produced BQRS results.
[2025-06-04 09:38:21] [PROGRESS] execute queries> [1/1 eval 1.5s] Evaluation done; writing results to E:\advance_javascript\codeQL\8\codeql-exercise\custom-results.bqrs.
[2025-06-04 09:38:21] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-04 09:38:21] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-04 09:38:21] The disk cache is freshly trimmed; leave it be.
[2025-06-04 09:38:21] Unpausing evaluation
[2025-06-04 09:38:21] Exiting with code 0
