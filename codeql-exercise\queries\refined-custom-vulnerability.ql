/**
 * @name Refined Custom Vulnerability Detection - Hardcoded Sensitive Values
 * @description Detects hardcoded sensitive values while avoiding false positives
 * @kind problem
 * @problem.severity error
 * @security-severity 8.0
 * @precision high
 * @id js/hardcoded-sensitive-value-refined
 * @tags security
 *       external/cwe/cwe-798
 */

import javascript

/**
 * Predicate to identify suspicious string literals that contain sensitive patterns
 */
predicate isSensitiveString(StringLiteral str) {
  str.getValue().toLowerCase().matches("%hacker%paradise%") or
  str.getValue().toLowerCase().matches("%i am hacker%") or
  str.getValue() = "I am hacker's paradise"
}

/**
 * Predicate to identify false positive contexts where sensitive strings are legitimate
 */
predicate isFalsePositiveContext(ASTNode node) {
  // 1. Inside masking/sanitization functions
  exists(Function f |
    f.getName().toLowerCase().matches("%mask%") or
    f.getName().toLowerCase().matches("%sanitize%") or
    f.getName().toLowerCase().matches("%clean%") or
    f.getName().toLowerCase().matches("%secure%")
  |
    node.getContainer*() = f
  ) or
  
  // 2. In comments (template examples, documentation)
  exists(Comment c |
    c.getText().toLowerCase().matches("%template%") or
    c.getText().toLowerCase().matches("%example%") or
    c.getText().toLowerCase().matches("%masked%") or
    c.getText().toLowerCase().matches("%documentation%")
  |
    c.getLocation().getStartLine() = node.getLocation().getStartLine() or
    (c.getLocation().getEndLine() >= node.getLocation().getStartLine() - 2 and
     c.getLocation().getStartLine() <= node.getLocation().getEndLine() + 2)
  ) or
  
  // 3. String contains "MASKED" or similar indicators
  exists(StringLiteral str |
    str = node and
    (str.getValue().matches("%MASKED%") or
     str.getValue().matches("%TEMPLATE%") or
     str.getValue().matches("%EXAMPLE%"))
  ) or
  
  // 4. Variable names suggest it's for testing/masking
  exists(VarDecl decl |
    decl = node and
    (decl.getName().toLowerCase().matches("%mask%") or
     decl.getName().toLowerCase().matches("%template%") or
     decl.getName().toLowerCase().matches("%example%") or
     decl.getName().toLowerCase().matches("%test%"))
  ) or
  
  // 5. Inside configuration templates or test data
  exists(ObjectExpr obj |
    node.getContainer*() = obj and
    exists(Property prop |
      prop.getContainer() = obj and
      (prop.getName().toLowerCase().matches("%template%") or
       prop.getName().toLowerCase().matches("%config%") or
       prop.getName().toLowerCase().matches("%example%"))
    )
  )
}

/**
 * Predicate to identify variable assignments with sensitive values (excluding false positives)
 */
predicate isSensitiveAssignment(AssignExpr assign, StringLiteral str) {
  assign.getRhs() = str and
  isSensitiveString(str) and
  not isFalsePositiveContext(assign) and
  not isFalsePositiveContext(str)
}

/**
 * Predicate to identify variable declarations with sensitive values (excluding false positives)
 */
predicate isSensitiveVarDecl(VarDecl decl, StringLiteral str) {
  decl.getInit() = str and
  isSensitiveString(str) and
  not isFalsePositiveContext(decl) and
  not isFalsePositiveContext(str)
}

/**
 * Predicate to identify sensitive logging (excluding false positives)
 */
predicate isSensitiveLogging(CallExpr call, StringLiteral str) {
  (call.getCalleeName() = "log" or 
   call.getCalleeName() = "console.log" or
   call.getCalleeName() = "console.error" or
   call.getCalleeName() = "console.warn") and
  call.getAnArgument() = str and
  isSensitiveString(str) and
  not isFalsePositiveContext(call) and
  not isFalsePositiveContext(str)
}

from ASTNode node, StringLiteral str, string message
where 
  (
    // Case 1: Variable assignment
    exists(AssignExpr assign | 
      isSensitiveAssignment(assign, str) and
      node = assign and
      message = "Hardcoded sensitive value found in assignment: '" + str.getValue() + "'"
    )
  ) or (
    // Case 2: Variable declaration
    exists(VarDecl decl | 
      isSensitiveVarDecl(decl, str) and
      node = decl and
      message = "Hardcoded sensitive value found in variable declaration: '" + str.getValue() + "'"
    )
  ) or (
    // Case 3: Direct string usage in logging
    exists(CallExpr call |
      isSensitiveLogging(call, str) and
      node = call and
      message = "Sensitive value exposed in logging: '" + str.getValue() + "'"
    )
  )
select node, message
