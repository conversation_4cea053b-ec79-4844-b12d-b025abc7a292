[2025-06-04 10:55:27] This is codeql query run queries/debug-command-injection.ql --database=node-db --output=debug-cmd.bqrs
[2025-06-04 10:55:27] Log file was started late.
[2025-06-04 10:55:27] Calling plumbing command: codeql resolve ram --dataset=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript --format=json
[2025-06-04 10:55:27] [PROGRESS] resolve ram> Stringpool size measured as 5013930
[2025-06-04 10:55:27] Plumbing command codeql resolve ram completed:
                      [
                        "-J-Xmx1346M"
                      ]
[2025-06-04 10:55:27] Spawning plumbing command: execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --output=E:\advance_javascript\codeQL\8\codeql-exercise\debug-cmd.bqrs -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript path:E:\advance_javascript\codeQL\8\codeql-exercise\queries\debug-command-injection.ql
[2025-06-04 10:55:33] Plumbing command codeql execute queries terminated with status 0.
[2025-06-04 10:55:33] Exiting with code 0
