/**
 * @name Debug Command Injection Detection
 * @description Debug query to understand what command injection patterns exist
 * @kind table
 * @id js/debug-command-injection
 */

import javascript

from CallExpr call, File f
where (
  call.getCalleeName() = "exec" or
  call.getCalleeName() = "execSync" or
  call.getCalleeName() = "spawn" or
  call.getCalleeName() = "spawnSync" or
  exists(PropAccess prop |
    prop.getPropertyName() = "exec" and
    call.getCallee() = prop
  )
) and
f = call.getFile() and
f.getAbsolutePath().matches("%codeql-exercise%")
select f.getBaseName() as filename, 
       call.getLocation().getStartLine() as line_number,
       call.toString() as call_expression,
       call.getArgument(0).toString() as first_argument
