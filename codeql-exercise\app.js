const express = require('express');
const mysql = require('mysql2');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const { exec } = require('child_process');

const app = express();
app.use(express.json());

// VULNERABILITY 1: Hardcoded credentials
const DB_PASSWORD = "admin123";
const JWT_SECRET = "super_secret_key_123";

// VULNERABILITY 2: Custom injected vulnerability - Hardcoded sensitive value
const VAL_VAR = "I am hacker's paradise";
console.log("Sensitive data exposed:", VAL_VAR);

// VULNERABILITY 3: SQL Injection
app.post('/login', (req, res) => {
    const { username, password } = req.body;
    
    // Vulnerable SQL query - direct string concatenation
    const query = `SELECT * FROM users WHERE username = '${username}' AND password = '${password}'`;
    
    // Simulate database connection
    console.log("Executing query:", query);
    res.json({ message: "Login attempt logged" });
});

// VULNERABILITY 4: Command Injection
app.post('/backup', (req, res) => {
    const { filename } = req.body;
    
    // Vulnerable command execution
    const command = `tar -czf backup_${filename}.tar.gz ./data`;
    exec(command, (error, stdout, stderr) => {
        if (error) {
            res.status(500).json({ error: error.message });
            return;
        }
        res.json({ message: "Backup created", output: stdout });
    });
});

// VULNERABILITY 5: Path Traversal
app.get('/file/:filename', (req, res) => {
    const filename = req.params.filename;
    
    // Vulnerable file access - no path validation
    const filePath = `./uploads/${filename}`;
    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
            res.status(404).json({ error: "File not found" });
            return;
        }
        res.json({ content: data });
    });
});

// VULNERABILITY 6: Weak cryptographic practices
app.post('/hash', (req, res) => {
    const { data } = req.body;
    
    // Vulnerable - using weak hashing
    const crypto = require('crypto');
    const hash = crypto.createHash('md5').update(data).digest('hex');
    
    res.json({ hash: hash });
});

// VULNERABILITY 7: Information disclosure
app.get('/debug', (req, res) => {
    const debugInfo = {
        environment: process.env,
        config: {
            database_password: DB_PASSWORD,
            jwt_secret: JWT_SECRET,
            sensitive_value: VAL_VAR
        },
        system: process.platform
    };
    
    res.json(debugInfo);
});

// FALSE POSITIVE SCENARIO: Legitimate password masking
function maskSensitiveData(data) {
    // This is legitimate password masking - should NOT be flagged
    const MASK_VAR = "I am hacker's paradise - MASKED";
    return data.replace(/password/gi, '***');
}

// FALSE POSITIVE SCENARIO: Configuration template
const CONFIG_TEMPLATE = {
    // This is a template comment: VAL_VAR="I am hacker's paradise"
    database: {
        host: 'localhost',
        user: 'app_user'
    }
};

// VULNERABILITY 8: Insecure random number generation
app.get('/token', (req, res) => {
    // Vulnerable - using Math.random for security tokens
    const token = Math.random().toString(36).substring(2);
    res.json({ token: token });
});

// VULNERABILITY 9: Prototype pollution
app.post('/merge', (req, res) => {
    const { source, target } = req.body;
    
    // Vulnerable merge function
    function merge(target, source) {
        for (let key in source) {
            if (typeof source[key] === 'object' && source[key] !== null) {
                if (!target[key]) target[key] = {};
                merge(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
        return target;
    }
    
    const result = merge(target, source);
    res.json(result);
});

// VULNERABILITY 10: Insecure deserialization
app.post('/deserialize', (req, res) => {
    const { data } = req.body;
    
    try {
        // Vulnerable - eval on user input
        const result = eval(`(${data})`);
        res.json({ result: result });
    } catch (error) {
        res.status(400).json({ error: "Invalid data" });
    }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log("Vulnerable application started with sensitive data:", VAL_VAR);
});

module.exports = app;
