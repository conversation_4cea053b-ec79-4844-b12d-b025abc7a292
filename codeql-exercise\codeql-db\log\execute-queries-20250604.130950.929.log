[2025-06-04 13:09:50] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\results -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript queries/custom-vulnerability.ql
[2025-06-04 13:09:50] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- queries/custom-vulnerability.ql
[2025-06-04 13:09:51] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 13:09:51] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\custom-vulnerability.ql"
                      ]
[2025-06-04 13:09:51] Refusing fancy output: The terminal is not an xterm: 
[2025-06-04 13:09:51] Creating executor with 1 threads.
[2025-06-04 13:09:51] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations queries/custom-vulnerability.ql
[2025-06-04 13:09:51] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- queries/custom-vulnerability.ql
[2025-06-04 13:09:51] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 13:09:51] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                      ]
[2025-06-04 13:09:51] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise\queries
[2025-06-04 13:09:51] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 13:09:51] [SPAMMY] resolve extensions-by-pack> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 13:09:51] [SPAMMY] resolve extensions-by-pack> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 13:09:51] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 13:09:51] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 13:09:51] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql --format=json
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Found no pack; trying after symlink resolution with E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Found enclosing pack 'queries' at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] [SPAMMY] resolve library-path> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 13:09:51] [SPAMMY] resolve library-path> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 13:09:51] [SPAMMY] resolve library-path> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 13:09:51] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise\queries resolved OK.
[2025-06-04 13:09:51] [DETAILS] resolve library-path> Found no dbscheme through dependencies.
[2025-06-04 13:09:51] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                        ],
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "queries\\custom-vulnerability.ql",
                        "possibleAdvice" : "There should probably be a qlpack.yml file declaring dependencies in E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries or an enclosing directory.",
                        "qlPackName" : "queries"
                      }
[2025-06-04 13:09:51] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql hashes to 1968c4959ad248d1a6762363ba6c3dd7.
[2025-06-04 13:09:51] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql.
[2025-06-04 13:09:51] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 13:09:52] ExternalModuleBindingPass ...
[2025-06-04 13:09:52] ExternalModuleBindingPass time: 00:00.001
[2025-06-04 13:09:52] CollectInstantiationsPass ...
[2025-06-04 13:09:52] CollectInstantiationsPass time: 00:00.009
[2025-06-04 13:09:52] Ql checks ...
[2025-06-04 13:09:52] Ql checks time: 00:00.099
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve module javascript (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:13,8-18)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:18,29-42)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:27,30-43)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type Function (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:33,10-18)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:42,6-19)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type VariableDeclarator (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:48,12-30)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type AssignExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:53,12-22)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type CallExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:58,12-20)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type VariableDeclarator (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:63,17-35)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type AssignExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:64,17-27)
[2025-06-04 13:09:52] [ERROR] execute queries> ERROR: could not resolve type CallExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\custom-vulnerability.ql:65,17-25)
[2025-06-04 13:09:52] Sequence stamp origin is -6041890304759070001
[2025-06-04 13:09:52] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-04 13:09:52] The disk cache is freshly trimmed; leave it be.
[2025-06-04 13:09:52] Unpausing evaluation
[2025-06-04 13:09:52] Exiting with code 2
