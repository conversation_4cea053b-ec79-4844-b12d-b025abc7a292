[2025-06-04 12:52:10] This is codeql database analyze codeql-db queries/custom-vulnerability.ql --format=csv --output=results/custom-vulnerability.csv
[2025-06-04 12:52:10] Log file was started late.
[2025-06-04 12:52:10] [PROGRESS] database analyze> Running queries.
[2025-06-04 12:52:10] Running plumbing command: codeql database run-queries --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db queries/custom-vulnerability.ql
[2025-06-04 12:52:10] Calling plumbing command: codeql resolve ram --dataset=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript --format=json
[2025-06-04 12:52:10] [PROGRESS] resolve ram> Stringpool size measured as 5013930
[2025-06-04 12:52:10] Plumbing command codeql resolve ram completed:
                      [
                        "-J-Xmx1346M"
                      ]
[2025-06-04 12:52:10] Spawning plumbing command: execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\results -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript queries/custom-vulnerability.ql
[2025-06-04 12:52:12] [ERROR] Spawned process exited abnormally (code 2; tried to run: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\java\bin\java.exe, -Xmx1346M, "-Djava.library.path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\java\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\MongoDB\Server\7.0\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\Git\cmd;C:\Program Files\MongoDB\Tools\100\bin;C:\ngrok;C:\Program Files\Docker\Docker\resources\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\mongosh;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\users\<USER>\.local\bin;C:\users\<USER>\appdata\roaming\python\python313\scripts;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl;.", -cp, C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\codeql.jar, com.semmle.cli2.CodeQL, env_argv, execute, queries, -J-Xmx1346M, _, _, _, _, _, _, --no-rerun, _, --, _, queries/custom-vulnerability.ql])
[2025-06-04 12:52:12] Plumbing command codeql execute queries terminated with status 2.
[2025-06-04 12:52:12] Plumbing command codeql database run-queries completed with status 2.
[2025-06-04 12:52:12] Exiting with code 2
