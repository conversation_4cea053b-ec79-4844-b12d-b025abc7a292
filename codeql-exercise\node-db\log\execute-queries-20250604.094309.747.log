[2025-06-04 09:43:09] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\8\codeql-exercise\node-db\results -- E:\advance_javascript\codeQL\8\codeql-exercise\node-db\db-javascript queries/command-injection.ql
[2025-06-04 09:43:09] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- queries/command-injection.ql
[2025-06-04 09:43:10] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:43:10] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\command-injection.ql"
                      ]
[2025-06-04 09:43:10] Refusing fancy output: The terminal is not an xterm: 
[2025-06-04 09:43:10] Creating executor with 1 threads.
[2025-06-04 09:43:10] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations queries/command-injection.ql
[2025-06-04 09:43:10] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- queries/command-injection.ql
[2025-06-04 09:43:10] [PROGRESS] resolve queries> Recording pack reference security-queries at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:43:10] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise"
                      ]
[2025-06-04 09:43:10] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DECISION 1] security-queries: 1.0.0
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:10] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-04 09:43:11] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-04 09:43:11] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-04 09:43:11] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 09:43:11] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 09:43:11] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql --format=json
[2025-06-04 09:43:11] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 09:43:11] [DETAILS] resolve library-path> Found enclosing pack 'security-queries' at E:\advance_javascript\codeQL\8\codeql-exercise.
[2025-06-04 09:43:11] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 09:43:11] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\qlpack.yml.
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: not 1.0.0 {root: security-queries@1.0.0}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] security-queries: 1.0.0 {security-queries: not 1.0.0 {root: security-queries@1.0.0}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 1] security-queries: 1.0.0
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {security-queries: * [*], codeql/javascript-all: not * [*] {dependency: security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-04 09:43:11] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-04 09:43:11] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise resolved OK.
[2025-06-04 09:43:11] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-04 09:43:11] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "security-queries\\queries\\command-injection.ql",
                        "qlPackName" : "security-queries"
                      }
[2025-06-04 09:43:11] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 09:43:11] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 09:43:12] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql hashes to 9c798214f07f16464051c06e75f0ed41.
[2025-06-04 09:43:13] [DETAILS] execute queries> Compilation cache hit for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 09:43:13] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql
[2025-06-04 09:43:13] [PROGRESS] execute queries> [1/1] Found in cache: E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql.
[2025-06-04 09:43:13] Inferred that cached_Ssa::TWriteDefSource#e9fe1d3d/2@6f6ad5nu is empty, due to Ssa::TWriteDefSource#e9fe1d3d/2@8b751dlt.
[2025-06-04 09:43:13] Inferred that cached_DataFlowNode::TForbiddenRecursionGuard#50eecc95/1@752d12gs is empty, due to DataFlowNode::TForbiddenRecursionGuard#50eecc95/1@04fe93g3.
[2025-06-04 09:43:13] Inferred that cached_DataFlowImplCommon::Cached::getSourceModel/1#b549c0f5/2@9bde74p0 is empty, due to DataFlowImplCommon::Cached::getSourceModel/1#b549c0f5/2@174bd1gf.
[2025-06-04 09:43:13] Inferred that cached_VariableCapture::VariableCaptureOutput::Cached::TMallocNode#bbbdfa43/2@a82699ne is empty, due to VariableCapture::VariableCaptureOutput::Cached::TMallocNode#bbbdfa43/2@235405e1.
[2025-06-04 09:43:13] Inferred that cached_DataFlowImplCommon::Cached::UnreachableSet.toString/0#dispred#266771f6/2@d7ab430f is empty, due to DataFlowImplCommon::Cached::UnreachableSet.toString/0#dispred#266771f6/2@39df17f9.
[2025-06-04 09:43:13] Inferred that cached_DataFlowImplCommon::Cached::UnreachableSet.getEnclosingCallable/0#dispred#4d3fc3b5/2@10f86atr is empty, due to DataFlowImplCommon::Cached::UnreachableSet.getEnclosingCallable/0#dispred#4d3fc3b5/2@e373e3np.
[2025-06-04 09:43:13] Inferred that cached_DataFlowImplCommon::Cached::UnreachableSet#6cbf52f1/1@9b0aabah is empty, due to DataFlowImplCommon::Cached::UnreachableSet#6cbf52f1/1@065717fb.
[2025-06-04 09:43:13] Inferred that cached_PreCallGraphStep::PreCallGraphStep::loadStoreStep/4#77d6c9aa/4@636ce3ac is empty, due to PreCallGraphStep::PreCallGraphStep::loadStoreStep/4#77d6c9aa/4@b0489cfm.
[2025-06-04 09:43:13] Inferred that cached_PreCallGraphStep::PreCallGraphStep::loadStoreStep/3#8ae9b10a/3@b7112b5d is empty, due to PreCallGraphStep::PreCallGraphStep::loadStoreStep/3#8ae9b10a/3@7ede9ees.
[2025-06-04 09:43:13] Inferred that cached_VariableCapture::VariableCaptureOutput::SsaFlow::TSsaInputNode#0e229f96/3@0adbf96v is empty, due to VariableCapture::VariableCaptureOutput::SsaFlow::TSsaInputNode#0e229f96/3@504a1477.
[2025-06-04 09:43:13] Inferred that cached_AbstractValuesImpl::TCustomAbstractValueFromDefinition#49735a46/2@c2abb08j is empty, due to AbstractValuesImpl::TCustomAbstractValueFromDefinition#49735a46/2@3dd06euq.
[2025-06-04 09:43:13] Inferred that cached_AbstractValuesImpl::TCustomAbstractValue#0f206e96/2@2e2b860g is empty, due to AbstractValuesImpl::TCustomAbstractValue#0f206e96/2@d29d80bu.
[2025-06-04 09:43:13] [PROGRESS] execute queries> Starting evaluation of security-queries\queries\command-injection.ql.
[2025-06-04 09:43:13] Starting evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql
[2025-06-04 09:43:13] (0s) Start query execution
[2025-06-04 09:43:13] (0s) Beginning execution of E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TValueNode#239fba13/2@14b88ar0 with 62434 rows and digest 2070f50lnslkkmni6ui3g579469.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 with 34962 rows and digest fea75dtn6e02l4jtpj0fir9mba6.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me with 61500 rows and digest 1d4de0tjs28ikf4osuddegn1ppe.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 192887 rows and digest bf1ab4ujhkf0don2emb08cu4701.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::Cached::reachableBB/1#b7652edc/1@79167bkj with 4122 rows and digest 12e2cbtpl4rcp8m1495ue44uvte.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 237718 rows and digest e2e365km2cv53omf8vjemqdbtr1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::Cached::bbIndex/3#7b7737ea/3@f77a12p1 with 83974 rows and digest 512e7a7m3boufr08mm6e1blg3u2.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 with 3906 rows and digest 3444e3v02bl020ava9qii82e3c3.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre with 108660 rows and digest 15f8bc8s4t79u5lv8seojntm508.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 385 rows and digest 1999b8doaea4airlo4e9tr0a623.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 with 76021 rows and digest 978f95qfnc1amiq09gqe53hk4k3.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j with 55376 rows and digest 6f6f02aupa0djiruh6s5gbgfrt6.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::Cached::defAt/4#376d9864/4@4e6ba5jh with 5932 rows and digest 7f6c3621d85hqj2enlm0opbcllc.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::Cached::useAt/4#bb3437a7/4@ae2a62g4 with 10243 rows and digest 73e6b6enlcjl2njac1dkkvciate.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::Cached::bbLength/1#f7bba88f/2@f919c2p2 with 4122 rows and digest 23b09707b30ov1ghl6u9lfnp688.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::BasicBlock#31bdb16d/1@77c26arn with 4122 rows and digest 12e2cbtpl4rcp8m1495ue44uvte.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Constants::ConstantString#e55e40cc/1@43d594jb with 147 rows and digest 607b8ehoc6an67h29jl5uhfdfl7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_BasicBlockInternal::immediateDominator/1#dd3aef64/2@4d5997df with 103 rows and digest ae83283rm05fmat4bp9b9n1jct6.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Locations::CachedInDataFlowStage::synthLocationInfo/6#3dd0209e/6@7f50dda1 with 39 rows and digest b8da8eg7iqdhi9uiin3trd50nse.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s with 75908 rows and digest f21e59taour23ovre1f9u53nu74.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 with 147 rows and digest 9221c9o1tle1tvjggqsraki2ve9.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableOrThis::TThis#cecf4871/2@c7a678b8 with 4007 rows and digest 881fa5ldvhu59akc6im1ecfq7f4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableOrThis::TLocalVariable#9a859764/2@e69b12l5 with 9253 rows and digest eb09a9kq36s3g6pop5oea2rf863.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkNormalReturnKind#ba4deec9/1@fc2d4f4u with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::TParameterPosition#11b8dc0a/1@9e7f4ff2 with 42 rows and digest 2eed2d5ple0iioam0mlgli49j33.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkPositionalParameter#46f38a1e/2@c11fc0l6 with 19 rows and digest b38953na57p6n5hrca1hgbg8d5d.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkPositionalLowerBound#3170f1e3/2@ee1ba3os with 19 rows and digest f49744332s46445824nd60qbi16.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkFunctionSelfReferenceParameter#5f98c7d6/1@4287bahe with 1 rows and digest a28a4c33kuncpc8g6b5si5p502b.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkThisParameter#4a32bfbd/1@061cb7om with 1 rows and digest 10952drg6dialitmaeap0ujtdf7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::TReturnKind#7362f3ee/1@00b1966i with 2 rows and digest 8077edibunvleb7g75bfbjhteec.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkExceptionalReturnKind#03b97e99/1@d1d5a6d3 with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableOrThis::TLocalVariableOrThis#b4ecbb8d/1@7ee4e2gl with 13260 rows and digest af39dbv8pvfgddls05hd7ilih3a.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TPhiReadNode#5553b65f/3@0b064flc with 11 rows and digest a21c2eg3apvhbfov3hf50eb5go7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TWriteDef#4944b5c4/4@e12414dl with 215 rows and digest 9a421c7cjalsrjgu71q87foc35a.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Ssa::TPhiNode#d314e4bb/3@3d8a63re with empty relation.
[2025-06-04 09:43:13] (0s) Inferred that Ssa::TPhiNode#d314e4bb_2#join_rhs/1@41d359ro is empty, due to cached_Ssa::TPhiNode#d314e4bb/3@3d8a63re.
[2025-06-04 09:43:13] (0s) Inferred that DataFlowPrivate::samePhi/2#8b775182/2@19de7bog is empty, due to Ssa::TPhiNode#d314e4bb_2#join_rhs/1@41d359ro.
[2025-06-04 09:43:13] (0s) Inferred that DataFlowPrivate::samePhi/2#8b775182_10#join_rhs/2@609431cg is empty, due to DataFlowPrivate::samePhi/2#8b775182/2@19de7bog.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::SsaDataflowInput::getARead/1#3806be7f/2@031b53se with 3832 rows and digest eccedfo83n9jpoiu24rh9cp5f79.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::getAPhiInputDef/2#837168e4/3@3dafb3qt with 27 rows and digest 4be689ae10lqk158njsogsopee9.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TSsaInputNode#9e892e0c/3@319437it with 8 rows and digest 1797b9230j6qb17vr9jj0mv2798.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TDefinitionExt#da9c1f5d/1@ed6a2bhr with 226 rows and digest 19b515d17clqlf1hvest1gtc004.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TSsaDefinitionNode#adf45c43/2@8d8fdcbf with 217 rows and digest b088d1tsrj5j70a5kuvkftlhm37.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::Node.getLocation/0#dispred#a5baba9c/2@765cf5ks with 110453 rows and digest 684f63dnr0curmls066up3u2td1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkDynamicArgumentArray#eea95af7/1@bbef90rh with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkStaticArgumentArray#9f092aab/1@598169p3 with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::Node.toString/0#dispred#1dcf46a4/2@a212a4lj with 111386 rows and digest f45fb3qhjnqm8ga82e1c22c7gff.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TNode#9d438dfb/1@1b032650 with 111386 rows and digest 30f91baou4lpudtdctlc78sg454.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TApplyCallTaintNode#9d0b5e19/2@97baa1op with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TCapturedVariableNode#cea8e175/2@3bd2abql with 13 rows and digest 83d502rim0lm622pt9cgjrfmmo4.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TDestructuredModuleImportNode#634ad679/2@8135017g with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TDynamicArgumentArrayNode#d5343497/2@d886d7t6 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TDynamicArgumentStoreNode#0a197168/3@5aa34dds with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::TContent#7ab9d95e/1@672710na with 5803 rows and digest 89bdbfffjq3m1f04e6ce7ngq406.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkArrayElementUnknown#57e3fc1b/1@63444045 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkCapturedContent#b400f6fc/2@2982795h with 13 rows and digest 8e8aabu1tkn4lq3vgk008504ht8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkIteratorElement#83ae3c39/1@f49609k4 with 1 rows and digest 14dae325fn1rrk4g27omfsv8kma.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkIteratorError#7a430eb2/1@0f8f3deh with 1 rows and digest 10952drg6dialitmaeap0ujtdf7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkMapKey#00c04cb6/1@909511d3 with 1 rows and digest b42aeb6f5dhvr1ffes9kr95b663.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Contents::Private::MkMapValueWithKnownKey#27b8b3e2/2@fa2912db with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_ApiGraphModels::ModelOutput::relevantSummaryModel/6#b0481946/6@701b90vh with 4 rows and digest 5a52c8l28j1vqvotlfgdencrbf5.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_ApiGraphModels::ModelOutput::getASinkNode/2#b01c13f8/3@690349bq with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_ApiGraphModels::ModelOutput::getASourceNode/2#a62bcfef/3@7018f5ld with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlow::DataFlow::localFlowStep/2#efc035fe/2@fb0ec8k6 with 8145 rows and digest 31eb1dp5cn8gqrsuvcm1lmpakn7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::TSsaDefinition#0d127dc0/1@f40a99ib with 255 rows and digest 06431e016s0jk2ppvgfnm80clve.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::Read#b609b123/1@f16ddf74 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::RefKind#b2258c35/1@881ecfll with 2 rows and digest 8077edibunvleb7g75bfbjhteec.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::Write#e282fa30/1@d2689d98 with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::TCapture#4f713c20/4@8e2f73lv with 16 rows and digest 2cda9bn8jbuejinnedpjg18n478.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::TExplicitDef#22bfc9ce/5@2c730eeq with 169 rows and digest f7f5b6f512odut9kk5ioko8umv8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::TImplicitInit#6b2949bc/3@e30a14qr with 69 rows and digest 54de67b2djosc0up910lngqbgv4.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_SSA::Internal::TPhi#1df17c1c/3@26fff81m with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::TRefinement#02956a01/5@33c63a7d with 1 rows and digest ec9f5a7067eroj3gdm9q6ihjag4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::getDefReachingEndOf/2#1566eeab/3@9ecfa5ef with 425 rows and digest 214296e204uvrdvn28hshlvlhh4.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_AMD::AmdModule#b6e913fc/1@bc3f2fu3 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::SourceNode::Range#94bc5973/1@e7a6374g with 42114 rows and digest 2f87e0sam1bo7eef4iv04n0qom5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TSsaDefNode#c5110f94/2@35e4a7g0 with 255 rows and digest 972e311486ho3cmf71k6s3qfhf8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TPropNode#0c5b058b/2@26913bi5 with 132 rows and digest 8a6bad247sko71klgprvhrgma15.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TRestPatternNode#bc615c0a/3@b301ed6b with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TElementPatternNode#91902348/3@8fc3cc28 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TElementNode#deadfee0/3@2df1d7g2 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TReflectiveCallNode#21d0f6a5/3@cc9eb6lu with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TThisNode#fdc0ca37/2@04d53ctt with 4007 rows and digest 32b113s6dj6hc3d61258uf8leh8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TFunctionSelfReferenceNode#9ea28b31/2@b7e3d3f1 with 3906 rows and digest 5ac512tbpnhji4vd0knet2jag88.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::THtmlAttributeNode#f3a8d75b/2@f55717je with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TFunctionReturnNode#5239d249/2@f2b02doi with 3906 rows and digest 3b2ef714vuq5jhfgf2dgjvvn271.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TExceptionalFunctionReturnNode#bbf7cfaa/2@a614c5ul with 3906 rows and digest 6cd24b1f2eub73bl2chrm259qn8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TExceptionalInvocationReturnNode#83d93ddf/2@ed677fqg with 142 rows and digest 691e72fci33jatmj08jrn8mksh0.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TGlobalAccessPathRoot#8d2907c7/1@3a67e5s5 with 1 rows and digest b109f9qcsmniirb7flsb0ao7p75.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TTemplatePlaceholderTag#bb560e82/2@1398ace3 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TReflectiveParametersNode#57e6be05/2@2c065c4g with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TExprPostUpdateNode#1a835942/2@6becd3l7 with 23841 rows and digest e3acf72s6nara8cipr1ca4jrvde.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TNewCallThisArgument#b7cd2ecf/2@ab4f12kc with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TStaticArgumentArrayNode#f8ebc42b/2@be026fhm with 142 rows and digest 251f2aopen5ad5eo2ve23tda2o9.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TStaticParameterArrayNode#917437bb/2@7f904dlt with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TDynamicParameterArrayNode#e25059ed/2@748968ch with 3906 rows and digest b577cbtfq4g3i7hs4esum599nnb.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TImplicitThisUse#f50d0d30/3@71dfc2rp with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_NodeJS::isRequire/1#104e166f/1@70a197ap with 31 rows and digest e98f3bf58v9q3nckj765e2vq0ic.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::Cached::namedPropRef/3#81807ac5/3@60fa34nj with 18811 rows and digest 596659pipm8pc7hllqumnguap22.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::Cached::dynamicPropRef/2#74462b41/2@8667b4ep with 21 rows and digest 237a83qvjvpc9266osra9hvp0aa.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::Cached::hasLocalSource/2#6a818bc8/2@0a58c3n9 with 50181 rows and digest 12e35di44mj0g3eabsivcdsubhf.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::Cached::invocation/2#7bdc5fad/2@1d33f2uo with 111 rows and digest e4386asl2p64jrbp5dgia5ahje2.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Sources::Cached::isSyntacticMethodCall/1#48f62b54/1@aea117lp with 104 rows and digest bfc553hnuff23b4ublk0b6vd9b1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlow::DataFlow::PropRef.getBase/0#dispred#ec4de665/2@ddb499p1 with 18686 rows and digest b89b08qs10pcn4c5smmag0k0i08.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Expr::getCatchParameterFromStmt/1#ec492e4b/2@ddc434q0 with 26 rows and digest 77f103l6cvu2n3frc1eeb071fk5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TSynthCaptureNode#664dd831/2@861b7799 with 21 rows and digest 9b02adb70f4e5f3rvfb1cla78ca.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TClosureNode#05f2fa4f/1@f4f72bp2 with 48 rows and digest f75d3a4j9bpjir0p1s9ng4e8u85.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TExprNode#d2c21f1c/3@da4a39u9 with 18 rows and digest 363c26et2g8lv7r8hh6qt74ufd4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 with 5 rows and digest b418cdppe5vhu6uicbdbvhi2d71.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Ssa::TExprNode#563340ff/3@0b5e2dia with 7664 rows and digest 3f1605uemdou9vfb8be2c0ki4s7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::callContextAffectsDispatch/2#626f352c/2@5a0ceaq5 with 11 rows and digest f0e54fbmeallkkf9ds0dtieuof5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::getSpecificCallContextCall/2#d0a64fa2/3@6d635fc3 with 11 rows and digest 978af8i373cv7h4kjpb82q0sra8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_num#DataFlowImplCommon::Cached::TMkCallEdge#388082fe/3@650caagf with 49 rows and digest f305ealgks464acck0bv7r2m0k8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableCallableCached/1#2743ea02/2@aaecdd50 with 38 rows and digest 65917auhaff7ppiuno4d967i6ia.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::TDataFlowCallable#97acb46b/1@fbe3f6lo with 4221 rows and digest 74ead5vqi8817omi4o5a9hg0j6d.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkFileCallable#e9e4da7f/2@52011339 with 115 rows and digest 3ea825hat9g9rqp94d7vbt4en76.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkLibraryCallable#8ed09f42/2@bfa067vj with 87 rows and digest 80be3330s7duqg8jtj5b5ut8bn5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkSourceCallable#81050c20/2@c2ec67pu with 4019 rows and digest a27a2dmkulq8o94g6om65q2g8a3.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::TDataFlowCall#afb8174f/1@8b5ebbpv with 21809 rows and digest 68c64ahndu36tlu4k6kddorjmt7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkAccessorCall#b04c97e1/2@81f7fe3p with 18561 rows and digest 88dbb7p08ugeb7o18vqtqhql70a.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowPrivate::MkBoundCall#7df13f3b/3@93f7a050 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_FlowSteps::callsBoundInternal/4#1a8c12a5#fffb#cpe#123/3@8de242dl with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_FlowSteps::argumentPassing/4#8326a036#origOrder/4@06455d7m with 30 rows and digest 314007g1ldnbqmfjmsj2bvckd2e.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_FlowSteps::basicLoadStep/3#b3ac1e09/3@a2a5c8qo with 14515 rows and digest 008201lkhci1rjfgalq5sqqpla3.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::PreCallGraphStep::loadStep/3#0efd9330/3@2c0ac0j8 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::PreCallGraphStep::classInstanceSource/2#0e2ec27a/2@3fb3a9k6 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range#a891b4e4/1@658795qh with 643 rows and digest 8bf4d383kperolnv4v83b2gd6qf.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.describe/0#dispred#30d1566e/2@358350ht with 643 rows and digest 913a0f0v613pc2ksbpiem34ohr6.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractValue#c55f3e33/1@ee72ddj7 with 7986 rows and digest 2b8aabm434tkmbsqa3dfg90koa3.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_AbstractValuesImpl::TAbstractArguments#264153ca/2@9bc183d4 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractBoolean#ca67d884/2@73f7fe73 with 2 rows and digest 52933dniah355t2csbmc919oq8f.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_AbstractValuesImpl::TAbstractClass#65cfce2c/2@db52ea3s with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractDate#f27cb20d/1@b56ded80 with 1 rows and digest 8b7341h8eig9flknaabp5i3r9d1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractEmpty#9219630d/1@ae74e99f with 1 rows and digest 67208fn9rqv4lh3qu0fr99g9hv6.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractExportsObject#db94ca24/2@fa5423gg with 39 rows and digest df7c69o8obl9hceu0qbluv0apj4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractFunction#bdf8852b/2@a14fc5d7 with 3906 rows and digest 6e9f20jaqn2ilgcfa5lsged4qq9.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractGlobalObject#43ef0f22/1@3a25e6f3 with 1 rows and digest 771840rdpi325iaart1ollksj51.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractInstance#6f1bc1ea/2@ec2bcd7o with 3894 rows and digest 038bdfbk5b5rsijt7384j8m0921.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractModuleObject#8d2ab6f0/2@d28537am with 39 rows and digest 7ef79e427snpipg0oj81cf8a8la.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractNonZero#3f9e0b49/1@bd394d7l with 1 rows and digest fc57542m7ll1duav8e3av4id8c0.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractNull#9ee7725d/1@9b142d3n with 1 rows and digest 5ba0bbpvr8bu7g2hs1ctvm7hvj7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractNumString#25c27bba/1@8485e8ai with 1 rows and digest 7b9ba37mgtj0bbg95qp3oc0q8i4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractObjectLiteral#631e88fa/2@0561acud with 71 rows and digest c316aas9q7sldsl7cvucl2p4fbe.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractOtherObject#1eda4535/1@d7b540jp with 1 rows and digest 9934baj6a2g9f7ck3tq3rjebmm8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractOtherString#2955f1de/1@42dd315o with 1 rows and digest cbc6c0ri91edbn6dj9be9f7rcm1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractRegExp#54ff9e08/1@4a9408lg with 1 rows and digest bd482fvmakpmvlava3v78pi5ns2.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractUndefined#5a480212/1@6f5906la with 1 rows and digest 4a816cpp8p39hne0g0vmd6tmlk0.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TAbstractZero#8811fb25/1@90c39bdl with 1 rows and digest 812ba3ddnungbdsgqro7otvpjje.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TIndefiniteAbstractValue#54aa96bd/2@7fa741js with 8 rows and digest 7319466c480q6r2ashc6e93l1a7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TIndefiniteFunctionOrClass#6fd62023/2@28c6a9e8 with 8 rows and digest 01653ek63r6jqbtqt5ak2dq221a.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_AbstractValuesImpl::TIndefiniteObject#dd961f79/2@b1f8024n with 8 rows and digest 67e7b5hvei7js4bo3ksbko3p3uf.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_TypeInference::AnalyzedNode.getAValue/0#dispred#c8b3cee7/2@717238fe with 83146 rows and digest e898e3sdoiojefg1notp02mijrc.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Modules::Import.getImportedModule/0#dispred#8fbef8f6/2@999874m5 with 27 rows and digest dbcbc6j067jd99287sqq9ukcmh3.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::moduleImport/1#34aa15d2/2@d432c7ao with 36 rows and digest 181a30j43se7qjosvhkmn9ejjtc.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_ES2015Modules::ReExportDeclaration.getReExportedModule/0#dispred#7af331b4/2@9c2f73g7 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::Internal::getDefinition/3#49110aad/4@645968dv with 3858 rows and digest 5ffb84uj7tfdd6q7t720bs7u10a.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_SSA::SsaPseudoDefinition.getAnInput/0#dispred#0121cb58/2@45ff8a0a with 1 rows and digest 585e72kiiuefqhdrk4erggoq628.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_SSA::SsaPhiNode.getInputFromBlock/1#dispred#7a14d866/3@9163dbdp with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Modules::Module.getABulkExportedNode/0#dispred#167f25d9/2@285c996u with 10 rows and digest 5131cevil5i6mo3t28ihdmaji79.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::postUpdatePair/2#1a2827e6/2@5a446f35 with 23970 rows and digest 0883dbcdb6hagtikhgcdjs6rh20.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TFlowSummaryNode#cef675d8/2@e7d328f8 with 757 rows and digest c26ae6bnresgiqes4mdpr2cgfib.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkArrayElementLowerBound#28e6defe/2@eca1bf11 with 11 rows and digest 55a43csg2r3n0u385nm7gmmaijb.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkAnyCapturedContent#79ed9e26/1@7cc30422 with 1 rows and digest bc99c6ish3hu38qa64k3m3c5708.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkAnyProperty#dfc6293e/1@4fa4b325 with 1 rows and digest 9500385oeo8kvsb1vcagqfe0jfb.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkAnyPropertyDeep#eed6ed49/1@717253i8 with 1 rows and digest 82b31599df4leink1p4lfjenrlf.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkArrayElementDeep#c6011eb9/1@aca575rl with 1 rows and digest 09910erpgkairlmvqorl30685v1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkArrayElementKnown#c1108fbe/2@015defg5 with 10 rows and digest 5f50d4s283l7l4odl5cl4nhkoo5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkAwaited#bd67f530/1@4acf1f8i with 1 rows and digest eb36fbuq2irtc2ke7ue7334vj4a.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkIteratorFilter#d07c1aa2/1@7382405e with 1 rows and digest e494326fdfooto1m9be2d96otce.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkMapValueAll#9a4f0ba1/1@39e9f2ij with 1 rows and digest 6092fb1b5pr1c4qubhb50dlp2b8.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Contents::Private::MkMapValueKnown#6002b77c/2@cfbaf6bo with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkOptionalBarrier#924af967/2@ab8afb4i with 1 rows and digest 1da078lq822d0tbfro3e9oa69ff.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkOptionalStep#8dbe082d/2@49ab3bdo with 2 rows and digest 725279166ph080u3g8fk458t8d4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkPromiseFilter#53e3ff10/1@92504fei with 1 rows and digest 81c2c0i3ebeuourgi7jf8isa326.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkSingletonContent#15f8a600/2@80cc5750 with 5803 rows and digest c51d18654kdsaalkvvnfgc0sh86.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::TContentSet#f82bd9d6/1@1b80abe1 with 5835 rows and digest 8dc6ablvlfhh09mg2kk465ndr68.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkPropertyContent#5db4c66a/2@fe294ai2 with 5782 rows and digest a40701hps03i45i29qhpfi2mdme.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkSetElement#408b4dc8/1@c632364k with 1 rows and digest f764b1d21a1fgcg2kpnbqhd65k7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkPromiseValue#8013ebef/1@2ebfe5fh with 1 rows and digest a28a4c33kuncpc8g6b5si5p502b.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkPromiseError#c5f51cac/1@156b7c77 with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Public::Content.toString/0#dispred#3acf41d3/2@eff5262f with 5803 rows and digest 8e08ddktb4fghbon2v27h5sibnd.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Contents::Private::MkMapValueWithUnknownKey#34fe48f5/1@f1fb6frl with 1 rows and digest e1863b3n66jq4ukb5nukddibf32.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_FlowSummaryPrivate::Stage::backref/0#1ff9cb84/0@062482pi with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::optionalStep/3#c661d033/3@85ecde2o with 2 rows and digest 155d09d2tth7kug8mg4jgfdiqhe.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::optionalBarrier/2#c3d981a2/2@2e15eb13 with 1 rows and digest 3211c4usm42dausiu868ft901na.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TParamNode#35ce5dcc/2@2891e1b1 with 2 rows and digest 7c1444bepd5tceha5b1verun05f.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TThisParamNode#80ab3196/2@d6cfa1v1 with 3 rows and digest b67ee6vmkr5jovrgqng225teh07.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TVariableWriteSourceNode#6e5a279b/2@7df0c88s with 4 rows and digest 865aa184sg5ntjlb7rl9t9jlthe.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TSynthRead#63ee732d/5@4b5165q6 with 10 rows and digest b03daafq4ad9insm5nkeql4k454.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TSynthThisQualifier#5945e624/4@60226d6p with 10 rows and digest 7dbb2bbhkmuro8m6ioa9m0pai74.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TGenericSynthesizedNode#a2dffc13/4@999338ai with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TRestParameterStoreNode#82360cfa/3@310e47ee with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TSsaSynthReadNode#7dc0a8f7/2@7ab8f0k2 with 10 rows and digest b503d4hfes1onnpubg80lbigkq5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowNode::TSsaUseNode#6bd082bb/2@625deeko with 3832 rows and digest 079bd4rgverq4v37r52u73cgfhb.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::CaptureNode.getContainer/0#dispred#16eff21a/2@2758603n with 21 rows and digest 5fdfb4ohcasl1pjs00qjshq8i07.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::Cached::TSynthSsa#d5f40611/2@b47eaav7 with 1 rows and digest 29d96drv9pkqol5h5oep7rgnvla.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::SsaFlow::TNode#a3a8238f/1@72b4008e with 25 rows and digest 7c03e1einouvlg6cq36e1n0vble.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::SsaFlow::TExprNode#658708ef/3@42d9ab5m with 18 rows and digest 56e9f4ijh2n3gf99tkqelcbth45.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::SsaFlow::TSsaDefinitionNode#f3a93a70/2@961902mf with 1 rows and digest 467179h9kf3ff1410ap71ss7vd5.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::SsaFlow::getAPhiInputDef/2#28c84c83/3@76d9efur with 3 rows and digest 6af200pj5776o7i2iuh4d9vilr7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::CaptureSsa::TDefinitionExt#cc7cd88b/1@44cc86af with 7 rows and digest 2681fe98brgn6oaonf7raenuo1a.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_VariableCapture::VariableCaptureOutput::CaptureSsa::TPhiNode#bbcff565/3@835c862m with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::CaptureSsa::TWriteDef#6ac1a1f1/4@bf1b68ul with 6 rows and digest 4dda60lk58p3k0le9of5f19pk75.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::CaptureSsa::TPhiReadNode#17535541/3@8f707fdn with 1 rows and digest c9eaf46jdd91sb9t0ce90nbid8f.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::SsaFlow::TWriteDefSource#5342ee9d/2@5f684cqq with 6 rows and digest 021924dabbet48t7aogia37a0ub.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaSynthReadNode.getContainer/0#dispred#51e247a3/2@7f5904bm with 10 rows and digest e111e3oede37pklvi2d7tans7v1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaUseNode.getContainer/0#dispred#fd3175b2/2@2ded8cvv with 3832 rows and digest ccd8854argig8bodsg1krjqca58.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_GlobalAccessPaths::AccessPath::fromReference/2#677a5358/3@c664ee5b with 49967 rows and digest df3793ps7t6f4qcklr6p4jkc1qf.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_FlowSteps::identityFunctionStep/2#1d69b701/2@986648do with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::InvokeNode.getArgument/1#dispred#8d37387c/3@96e7e39i with 172 rows and digest 27befe5dd16f7a7q4up55gpph5c.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_DataFlowPrivate::Node.getImmediatePredecessor/0#dispred#9e796d89/2@f2c6057b with 8069 rows and digest 397bf365mf0njsc1d208cevhoba.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_GlobalAccessPaths::AccessPath::fromRhs/2#0be6b5c1/3@dac735rp with 9177 rows and digest f5685fu666t3ba0i94b9b28lad1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_TypeInference::AnalyzedNode.getALocalValue/0#dispred#c8de7f52/2@488cfffc with 81251 rows and digest 166d35m227anpv82nkp9mqla2rb.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_VariableTypeInference::clobberedProp/2#0eb15960/2@ecac0fs3 with 89 rows and digest 2029e1d09jnhlce1j6ijt6gu660.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::InvokeNode.getAnArgument/0#dispred#f59012e8/2@3bedffio with 172 rows and digest 66896dtf3uqs8bod35mi6dn5u8e.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_DataFlowNode::TXmlAttributeNode#ff07b4ed/2@becc9b9h with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Nodes::ClassNode::Range.getADecorator/0#dispred#9f4055b7/2@77f1b44b with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getAStaticMember/1#dispred#2d3660f4/3@edb8ec4a with 11 rows and digest 674b6brdotn1925lcdm486rfun7.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Nodes::ClassNode::Range.getASuperClassNode/0#dispred#29db6d6e/2@1ddf31ve with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getAnInstanceMember/1#dispred#23250de1/3@b23392ik with 2298 rows and digest e1b3f5lm8g3aiue4ciceghfj1qf.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getConstructor/0#dispred#53a1e5e8/2@ce1604af with 643 rows and digest 9aff3dgm7n9qbrsrlbrshd07hqd.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_Nodes::ClassNode::Range.getFieldTypeAnnotation/1#dispred#9051d949/3@01486427 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getInstanceMember/2#dispred#bdefb17e/4@637e39k9 with 2292 rows and digest 0c5e43ftcs49mhi136u4a7ovc74.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getName/0#dispred#f2929725/2@cef2530k with 643 rows and digest f19fbe3fsl12lrudp9jbuvki911.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Nodes::ClassNode::Range.getStaticMember/2#dispred#b9a5b98e/4@a6fb300v with 11 rows and digest 4d77c87uoq7gfdrrodko7q081q8.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_PreCallGraphStep::PreCallGraphStep::step/2#bf932b79/2@7281382q with 25 rows and digest ad678bko71evta9qbggoj4vqb27.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_Modules::Module.getAnExportedValue/1#dispred#ea17c3e9/3@309c87db with 1166 rows and digest 8aeebbahifpf2ppqobubr96b8h7.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_TypeInference::AnalyzedNode.getAType/0#dispred#2d36561f/2@9d9d35c6 with 390272 rows and digest fe5e93hcj2cf3l5ro8nbcva9pa1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_GlobalAccessPaths::AccessPath::DominatingPaths::hasDominatingWrite/1#085234a7/1@462ceeu3 with 1590 rows and digest c2852a38gdv90a0u0l9h4v8a9ae.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::PropertyName#945650a3/1@41e16ehc with 8877 rows and digest 2f8e25emjotf828dbs7ltdi441e.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::CallStep#e5af4dc7/1@28495e9q with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::CopyStep#f0aae27e/2@3a9b0e61 with 8877 rows and digest 41331ffrbdp2k5sgtpc6fg567r2.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::LevelStep#2f57267f/1@dcc129ne with 1 rows and digest a28a4c33kuncpc8g6b5si5p502b.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::LoadStep#210f9696/2@07bbe8qf with 8877 rows and digest 9a323ba1e3dp59nat405lm5kud4.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_StepSummary::LoadStoreStep#2ebd7b63/3@3458bdqb with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::LegacyPreCallGraphStep::loadStoreStep/4#e1c62530/4@2cfa02v3 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_PreCallGraphStep::LegacyPreCallGraphStep::loadStep/3#ec1011e4/3@7b0ddcii with 6 rows and digest 357066n6h9rvdtkehkal3q9ugfb.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::LegacyPreCallGraphStep::loadStoreStep/3#378992a3/3@597b619p with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::LegacyPreCallGraphStep::step/2#ef7a635f/2@e126cert with empty relation.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_PreCallGraphStep::LegacyPreCallGraphStep::storeStep/3#9f754cc4/3@d68329b4 with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::ReturnStep#d384a3ef/1@1e7ec313 with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::StoreStep#980a429e/2@7bb12ags with 8877 rows and digest 6a96dearifu6lmj1sr11o92g3q0.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::TStepSummary#b001526a/1@75b1f2ih with 26634 rows and digest 99af2e2dreskobosp47fb24i3e7.
[2025-06-04 09:43:13] (0s)  >>> First-level cache hit cached_StepSummary::WithoutPropStep#75b55953/2@4aaf13pv with empty relation.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_StepSummary::Cached::smallstep/3#f38b11a6/3@d2120foj with 39497 rows and digest 3ae8677bd5uqelc272b54rs28g1.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_FlowSteps::propertyFlowStep/2#4a808f29/2@93f71a14 with 28 rows and digest a8ceb6dtr0bjra74tf5jqufufn4.
[2025-06-04 09:43:13] (0s)  >>> Full cache hit cached_FlowSteps::globalFlowStep/2#dfab237b/2@50852fih with 5043 rows and digest bc0eb04i6973emta7tgljjbtlke.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::callStep/2#9fcb5078/2@a2b454kj with 30 rows and digest 83ae87kq5l8fogmigoqphsjfj34.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::returnStep/2#694e0aac/2@369b4fit with 32 rows and digest 8d5440uhpajmasv03ms1si1n71b.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::calls/2#951e713b/2@d7bc4b63 with 16 rows and digest 936e0dcbvdv8idspth1i0ue7rdd.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::getACallee/2#2146d20d/3@cbcb8c0f with 26 rows and digest 7fc6e27nm0s7lhj0eobvpfovlo7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::callgraphStep/2#99e0f09d/3@0b55bb6i with 8198 rows and digest 7fc8e5ml0be4n27hjgpmb0jqev6.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_CallGraphs::CallGraph::getABoundFunctionReference/3#4e705e74/4@e3841002 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Nodes::InvokeNode.getLastArgument/0#dispred#fbbd379c/2@77b9614o with 134 rows and digest 602d4b4kqci31i5i3u2g8mjfeq1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::getAFunctionValue/1#77fcbe89/2@526760i5 with 15694 rows and digest 76405bbkq7scnrvit181noh680c.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::isIndefiniteGlobal/1#c59c4395/1@a3cf82q1 with 18483 rows and digest 21309d4h93bf369etk5jl81v1vb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_StepSummary::Cached::step/3#7f46d31c/3@fcf4de8r with 35498 rows and digest a3e0158i2mjcdmkds71bqheca3c.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_TypeTracking::TypeTracker.append/1#dispred#aeeab7fd/3@8af9a2cq with 97647 rows and digest 4273edbpri8h9ubme8eopeegpha.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::getAFunctionReference/2#e2061ef0/3@2cffb6um with 28565 rows and digest e78c67h0eoat5h9u9uj3kas50ce.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_CallGraphs::CallGraph::getAnAccessorCallee/1#3179c4b6/2@faa265ja with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_JSDoc::JSDocNamedTypeExpr.resolvedName/0#dispred#75c63b1b/2@f276d353 with 153 rows and digest 1e70abtlfi2pbst55nv2ekfl3d9.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Nodes::ClassNode.getAClassReference/0#dispred#3f6695e4/2@be5e40ki with 8178 rows and digest 03cb9baqq5p9vf3rs8un41mu7tc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Nodes::ClassNode.getAnInstanceReference/0#dispred#4b6ace77/2@3f2c37hj with 9040 rows and digest ba3faed4v33m4a9gp5blfoo820f.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::getAnAllocationSiteRef/1#975c4720#origOrder/2@fec7c904 with 3 rows and digest 851707cf2fhobd8b82ffdn6jbv5.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CallGraphs::CallGraph::impliedReceiverStep/2#24d4eaac/2@e47fb1fq with 4071 rows and digest 6966f5qona86ka9qlr8trh9i7n3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::basicStoreStep/3#bd494023/3@7a48828f with 4483 rows and digest 4668act8k2qj1d1fkiif2sdrb2e.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_PreCallGraphStep::PreCallGraphStep::storeStep/3#02d310bb/3@12b55940 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_FlowSteps::getACallbackSource/1#c9728cae/2@d068f8gc with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_TypeTracking::TypeBackTracker.prepend/1#dispred#8e7d1ae9/3@196ec3ia with 97647 rows and digest 8f59807q7jc8ksvcttiiqpnrc78.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_FunctionWrapperSteps::functionForwardingStep/2#243cee08/2@b0ada6fl with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FunctionWrapperSteps::functionOneWayForwardingStep/2#86856b76/2@1cf0dc1u with 9 rows and digest 516cddk98ln7r994dta2egel1ud.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::Node.hasUnderlyingType/1#dispred#c9576148/2@016355h9 with 4174 rows and digest 2250156113s5m09useilnqlsmlf.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_DataFlowPrivate::Node.hasUnderlyingType/2#dispred#4bcf6ba1/3@3bd269ah with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::captures/3#a41e8e2c/3@b174b633 with 16 rows and digest 25ee7a4c995c946qtspkv0j90r0.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_FlowSteps::exploratoryBoundInvokeStep/2#e84a51a7/2@043c79h1 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_FlowSteps::exploratoryCallbackStep/2#266c0bc7/2@8382cdhq with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::getCapturedVariableDepth/1#839882a8/2@7e512c53 with 13 rows and digest e9d703gqmaqpu9rh7ke9ov1v0ge.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::getContainerDepth/1#1db7284a/2@3ab085hf with 4019 rows and digest 78c50e6ljtb99lq7n7l7veqsdn5.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_FlowSteps::receiverPropWrite/3#f8a57a86/3@e848f479 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_FlowSteps::returnedPropWrite/4#03dcac62/4@a601c2pg with 3 rows and digest 09b21eggg1pu4ug4vbhhl0ucsk4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkImpliedLambdaCall#6b25f156/2@0a5fcbsr with 3074 rows and digest 03fe64nftepedrqkc79v5odbjrf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkOrdinaryCall#7d71c2b5/2@683407u0 with 142 rows and digest decafeleucia9f2ppsectq524de.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_DataFlowPrivate::MkPartialCall#2d28b292/3@697c635e with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::MkSummaryCall#539ba1f2/3@5c9884bt with 32 rows and digest 1c12aektct8fqahra1k6diltsqe.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkRoot#8009436b/1@9ee972fh with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Impl::MkClassInstance#26cbf169/2@1a0c9b1v with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkDef#0ba1e57a/2@49fb8f86 with 219 rows and digest 10627as35q8r9j15qvmaeimnb46.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::rhs/3#7303814d/3@b03481bh with 219 rows and digest 668a9f4eku9lroctc3duq68q6n0.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkModuleExport#84d8d8a3/2@54251e19 with 1 rows and digest d3c450539iqlgjj4bnv8b39ffi2.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkModuleImport#a48e0022/2@9f4543vi with 8 rows and digest 8f50d76kferhcho476nss08ntof.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Impl::MkTypeUse#18c0fdfc/3@0b3f28ev with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::rhs/2#23712a61#origOrder/2@45e66812 with 219 rows and digest 92dd87tolsf4f6hsi1c69l3nmt0.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::TApiNode#564cbb7f/1@512d3d7g with 510 rows and digest e0d7f1tvro8mstloa9370b1f2t8.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkModuleDef#213fd289/2@6c0e35fv with 1 rows and digest 91f1b6i141borr1i3v2rb2m55c4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkModuleUse#f0824180/2@d4f70fjf with 8 rows and digest ef81a7oaf5h4r9unqe4ejam9s54.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::MkUse#a4362b36/2@25daebbl with 272 rows and digest b959a4fjvj5glggh1rds5ito3qf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::use/3#adbfd2c1/3@956166sg with 276 rows and digest e8e69eefv52fav0kc4k9176bji7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::use/2#446d92fe/2@c4546f42 with 290 rows and digest 7d1ca622eco2m52koiejglar8c3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::trackUseNode/1#d1d34ef4/2@361e61c0 with 292 rows and digest 58caa590sugv6eqh2105n6h0sib.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Impl::MkSyntheticCallbackArg#09fb16dd#origOrder/4@e0aff912 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::trackDefNode/1#1317f5b7/2@56b189fa with 620 rows and digest 25b4addp2s0i3gp7ano77rfn4qb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::distanceFromRoot/1#b5788886/2@1771aaen with 510 rows and digest 7c3c2f1ti7re96lfjm07ucplafa.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Impl::edge/3#87719dc4/3@de5317hu with 513 rows and digest 71be065ftt2c8pcfp7c1ejdnvrd.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getMember/1#dispred#855c0504/3@8b36fck6 with 137 rows and digest 7dd5756hip34nphr4tpptgitag2.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphModelsSpecific::ModelOutputSpecific::getATypeNode/2#8c9f2f03/3@d96e7c0e with 2 rows and digest 3a39005od2270i41lrd4n8r05bc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphModels::getNodeFromType/1#414ab8df/2@d651bfg2 with 2 rows and digest 7821e8sihsiqjjv8kpee678av6e.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getInstance/0#dispred#0ba5f1b4/2@e4ed5e3c with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getArrayElement/0#dispred#16d9404a/2@d4333cg3 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getContentRaw/1#dispred#046261d9/3@725e861a with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getAMember/0#dispred#b056b81e/2@e41c36o7 with 137 rows and digest 9a5a49s9l4vahvhq1okkrq27q4c.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getUnknownArrayElement/0#dispred#d6a38ef9/2@f66f11a4 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getAParameter/0#dispred#cdeb3fd3/2@1718e76b with 137 rows and digest 7033e06bevtfpqn4nq2tbpgag49.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getParameter/1#dispred#04e368b7/3@38f4f597 with 137 rows and digest 4bef2d5lvh2n0qr2flvatrgh4s1.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getPromised/0#dispred#6b84cc1b/2@b9c177td with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getReturn/0#dispred#6eca0e97/2@de95738j with 88 rows and digest 7f80edq91tmdvv5hf78p5js4kf7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_ApiGraphs::API::Node.getReceiver/0#dispred#ecce8bfd/2@4bdc4dau with 86 rows and digest 275c5fbpd3q2nv1nh71av7tgg18.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getForwardingFunction/0#dispred#363cddc5/2@b5ba32h2 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getPromisedError/0#dispred#7e8287fb/2@142e0emo with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getADecoratedClass/0#dispred#dbf9b523/2@38e513vo with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getADecoratedMember/0#dispred#a12ed4df/2@b190336q with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Node.getADecoratedParameter/0#dispred#13ed201c/2@42cb01a8 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphs::API::Impl::getAPromisifiedInvocation/3#f2820de7/4@c4900c0b with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphModels::ModelOutput::resolvedSummaryBase/3#e80c03e5/3@82a41463 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableCallableLambda/2#c90fc9aa/3@e7ae9cqe with 11 rows and digest 9a9352hnjje94g2ipmdmpm6t7nb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::getNodeType/1#e0dc6966/2@981f87ds with 111386 rows and digest 8f75dd3jc1pjuq7d6t7ceejuere.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TCallNone#e4dbf1af/1@4dad29vv with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_AccessPaths::AccessPath.getAnInstanceIn/1#dispred#9d0a4c06/3@63ae5a8b with 10165 rows and digest 6b3c2306mtl2t9b83ifil5sm5va.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_fastTC@BarrierGuards::ConditionGuardDominators::immediateDom/2#6da7dd69#2/2@be17f8l1 with 36 rows and digest 3c6dbcr2a9ollgu7mlb3t45fp6d.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_BarrierGuards::ConditionGuardDominators::getAnAccessPathUseUnderCondition/2#b3b9ab71/3@1f274b98 with 48 rows and digest 7354b6ndd0vevh30flsucachftb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::getNodeFromSsa2/1#15fcdba5/2@eb0b42ki with 7623 rows and digest 91c14540idhg5nvg1p46rvsichc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowNode::TFlowSummaryDynamicParameterArrayNode#7ec21756/2@6894a8su with 87 rows and digest cf8a7eubjoje04dk091tavej8uc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowNode::TFlowSummaryIntermediateAwaitStoreNode#d384bd8e/2@804e2627 with 11 rows and digest 83320cnh24h15m080n4q5occ6td.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowNode::TFlowSummaryDefaultExceptionalReturn#1d49fee2/2@86023dok with 77 rows and digest 80298dea0b33oq0ikovbhvkrkvc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_VariableCapture::VariableCaptureOutput::localFlowStep/2#2ab5b072/2@11d96dmt with 15 rows and digest 29cfa3o9pk9b80cfsuofb889ni9.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_AdditionalFlowSteps::AdditionalFlowStep::step/2#1d85d944/2@29d1e9jp with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_AdditionalFlowSteps::AdditionalFlowStep::jumpStep/2#0b24a421/2@816880j2 with 25 rows and digest ad678bko71evta9qbggoj4vqb27.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Nodes::RegExpCreationNode.getAReference/0#dispred#c8257679/2@c540e3vl with 2 rows and digest 1df1c24oae33p9fi1s1ujmhhhmc.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_AdditionalFlowSteps::AdditionalFlowStep::readStep/3#2ff68899/3@1054f2u5 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_AdditionalFlowSteps::AdditionalFlowStep::storeStep/3#465bd7fc/3@09f30dve with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::nodeGetEnclosingCallable/1#39b5e716/2@c56ad6re with 111385 rows and digest 52401827r23cek2fnqauebm5rq7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::jumpStepCached/2#cd8c06db/2@e7a915hd with 54 rows and digest 9dd967lahb6qe4i5ma83flfqh8c.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::parameterNode/3#205a0af8/3@99a91fsr with 16872 rows and digest 1e6d11dhst5nvlcbi1plmlq8289.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TCallOption#bcf686d6/1@ec2556m5 with 21810 rows and digest 43480dnhdvnte62babk1i3lubgf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TCallSome#128a64e8/2@5ba1af2r with 21809 rows and digest e10adf85oareq2vr9uf7otm7j6d.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::argumentNode/3#a5784af2/3@659bdai9 with 30370 rows and digest 38a0d2lkbh5doountcpgvqhnkab.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TValueReturn#9385c94a/2@23f0c0fk with 2 rows and digest 46c5d4jdf01o03ef4bb4mg1cjtc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnKindExt#9c5fac0c/1@fb6df61j with 25 rows and digest 7c03e1einouvlg6cq36e1n0vble.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TParamUpdate#1fc6206f/2@59da73ud with 23 rows and digest facea6u4ma7vnukvjrg106pa5b3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::outNodeExt/1#435f1887/1@d4fb80t1 with 37953 rows and digest eefd7cja0b4v9gjlnfm8j82n97b.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::compatibleTypesCached/2#83ada9fd/2@bc2a600m with 13648 rows and digest ca65e1pdnvti0ue3olllhte7f7c.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::mayBenefitFromCallContextExt/2#35d37af2/2@c6ac62d2 with 1 rows and digest 8a7930t6mdiqvas1g6k93nin1kb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::DataFlowCall.getEnclosingCallable/0#dispred#13ee8f92/2@aebeffkb with 21809 rows and digest 19014db4oofkiseta7ljqjfmh24.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableImplInCallContextExt/2#d46281a9/3@eee07frm with 11 rows and digest eb5a6d89pvslvio79vp5l21acee.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TCallEdge#fa156fc9/1@8fa1d11c with 49 rows and digest fe67e14org6t9e38l82obsbtop9.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::TReturn#ad7ff56d/3@1888aald with 11 rows and digest 01aa3a1td64l3vpaof79nghs629.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::CachedCallContextSensitivity::instanceofCc/1#1642c538/1@405801dh with 24 rows and digest 796e567qscjfjnlm06ioofq0clf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::CachedCallContextSensitivity::instanceofCcCall/1#aed10e5f/1@e68d347h with 12 rows and digest 614d1ele6b5p2i8mp7pt92o2kf0.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::CallContextNoCall#10d2b172/1@3f7a832h with 12 rows and digest cf63fffh5o8tpt55v9kik05dpp3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::reducedViableImplInCallContext/3#d52638af/3@e4ec15po with 11 rows and digest ad4d41o4jtroe9gi7nm1178n7l8.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::reducedViableImplInReturn/2#c1e4f79b/2@c481f4b8 with 11 rows and digest 598728qv0717hb11totl39be215.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::PrunedViableImpl<Cached::CachedCallContextSensitivity::PrunedViableImplInput>::viableImplCallContextReduced/2#923df4f4/3@016674qi with 11 rows and digest e4dfb57tgn6ettc2dm04dqa1u4d.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::CallContextSensitivity<Cached::CachedCallContextSensitivity::CallContextSensitivityInput>::PrunedViableImpl<Cached::CachedCallContextSensitivity::PrunedViableImplInput>::viableImplCallContextReducedReverse/2#2888eed6/3@5e4c71jt with 11 rows and digest cbcecdvk6sem6rrls17qpcr776a.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TAccessPathFront#ad16228c/1@daf1aa3j with 5804 rows and digest a34717f6f0qhake5151ntn3h0s9.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TFrontNil#422809d5/1@047defi2 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TFrontHead#7e04d60f/2@46a0c8cl with 5803 rows and digest 695d1bv13c3j2vl6h7fahn8kih1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TAccessPathFrontNone#facd32c4/1@83adb18v with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TAccessPathFrontOption#4dddcd04/1@e32c64m4 with 5805 rows and digest 9734cbhk2h6gc4thu9053qdm7m1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TAccessPathFrontSome#87158b8d/2@c28bab4q with 5804 rows and digest 252a5dpgpb454sc7kuaa2cnf841.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TAnyLocalCall#3a61c44f/1@ca447405 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxAccessPathFront#27fccda1/1@788740jj with 10 rows and digest 991fa7gbago3r4vsetf2agtmig3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxFrontNil#11e6c994/1@7082909a with 1 rows and digest 6af319hvvals2v7v45ga25pits7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxFrontHead#3a1850ab/2@5eabe78l with 9 rows and digest 453dc1bim06e9g6bhd2fhood8rb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxAccessPathFrontNone#79890dfe/1@68d3c11e with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxAccessPathFrontOption#d4960d9a/1@f58071u0 with 11 rows and digest 341bd2euvibi7c1mjsmd46eb4a3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TApproxAccessPathFrontSome#f5fe03b2/2@0dbfa19f with 10 rows and digest 1743aa4btbo2b0edj06cheqcl6a.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TBooleanNone#43245c59/1@71ba4adt with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TBooleanOption#73358799/1@28631bkg with 3 rows and digest 1e437594k4lrub4j1rp8ks3g8h1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TBooleanSome#5c2ee45c/2@35c9dd51 with 2 rows and digest cad59d1f424i34evjnuchesu99c.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TNodeEx#7b69bad0/1@412da2nc with 222869 rows and digest b739a4u8qnfv3ibfkfsig3fmq0e.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TNodeNormal#694ed724/2@5bbef9hq with 111386 rows and digest 44f9b24q9pjon05cs2qjd3m2um2.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TNodeImplicitRead#fa83cad5/2@81a807gn with 111386 rows and digest 9d2f21fnnp26n9v69b7943ahhkf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TParamReturnNode#3b2aa9ea/3@ccd6ebvo with 97 rows and digest 4ebc6flgpsd3oq0jnpjm7ojmsne.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::readSet/3#21a7459b/3@ba6907hi with 19737 rows and digest 0919cbomfep591od1so7n0cs5u1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableParamArg/3#ebd29e04/3@e0896a3v with 81 rows and digest 66c91c7bkla3qdr2d4k6gtv4a02.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::expectsContent/2#414e28dc/2@8e0fd46k with 3932 rows and digest 71d801f8h7hui4ie53ijq99hl48.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnCtx#b99fb8a6/1@6878a42t with 8064 rows and digest 435d7a5bmnnp8fgd6md9ggemgtf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnCtxNone#fc34d2a0/1@d4e6016j with 1 rows and digest 14d190jcpgdl411cck5pgml3dof.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnCtxNoFlowThrough#a615ca0e/1@dc26a808 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnCtxMaybeFlowThrough#00178386/2@dfa60896 with 8062 rows and digest 141b6dh5ei12etrlg4108slo7s7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_num#DataFlowImplCommon::Cached::TReturnPosition0#4ac40474/3@f3ad73sv with 8062 rows and digest 7e80d2ufn279uas0mubfulva6a2.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::TReturnPosition#f01d93ec/1@9447fb6q with 8062 rows and digest 3f08825n9ph26rfd2vgvuf6gap3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::allowParameterReturnInSelfEx/1#c9a65cae/1@d6e6b95j with 30 rows and digest 718d5a5dven5t55c7beo9l12vg9.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::castingNodeEx/1#59e00497/1@c93f8f41 with 55094 rows and digest 6ff6e12fut9775viheqbu7os1i4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::clearsContentSet/2#482a602f/2@1e8f9e7h with 50 rows and digest 0f0e43mmjdsh78peud0ji2ldm57.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::expectsContentSet/2#5a3fa81d/2@615a86m2 with 3932 rows and digest 86efe9bb26nnaqnb070uh689tuf.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::flowCheckNode/1#3158e062/1@9c8edc4k with 20608 rows and digest a9284bk9ts7bd6vqe32tvqm18ha.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getAnOutNodeEx/2#5c5ead59/3@ada1532t with 48798 rows and digest 0b58a6fdefmsm91cco8fkjocbia.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getContentApproxCached/1#c42e8f55/2@ef44a2ov with 5803 rows and digest 8f2b8avur54j2hnps5a8laub4jb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getParamReturnPosition/2#8d239723/3@bb7963cd with 127 rows and digest 8d3a65cm0kvacvpqm67i0bcavn6.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getReturnPositionEx/1#fc2763ad/2@ef4475qb with 8062 rows and digest bf6b79gerv6oblsh77pbtq4p979.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getValueReturnPosition/1#7441dcb2/2@06d49f38 with 7965 rows and digest f5fe8202ti4il023f1kqjcf9v21.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::getSecondLevelScopeEx/1#db7c1834/2@3e8786tp with 111386 rows and digest 5e9a2f9grfktd5r0ln12b839c8d.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::hiddenNode/1#8d668334/1@2f27ed6o with 143178 rows and digest 62390fb377q7iqf04tsk98hudk7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::isTopType/1#523d880d/1@b30c6do7 with 1 rows and digest 19f2cevarof1fbaqkctt3t43if4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::localFlowStepExImpl/3#359f276d/3@28d718aj with 17664 rows and digest 0cf6133thh4s8a99s50436lbhk4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::simpleLocalFlowStepExt/3#fe4dcec4/3@569da51n with 17537 rows and digest 903dc0ecuh4ftamrqvlhs9lsf64.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::FlowThrough::Final::argumentValueFlowsThrough/4#3e21ca15/4@83ab7eij with 4 rows and digest 7c635ds0ekjg16kf646ij9jujqc.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::paramMustFlow/2#d1c7d66b/2@2a18d3f6 with 33 rows and digest f622a8uc5pp7ild2sjq9tfr1pj4.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::readEx/3#e1ec868c/3@8284d75o with 19737 rows and digest 3a85e02mhrjmlm6f25hk3e6qtb3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::storeEx/5#80ba630f/5@3f831ehe with 13927 rows and digest 3677bbrhfnohne3n55pjkklloe3.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::storeSet/5#a9c87f01/5@831c07su with 13927 rows and digest 7528c8itf1ne75o86671achv177.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::typeStrongerThanCached/2#25161972/2@e13001s3 with 4549 rows and digest 1f1d274cqjvnq7oq6ncsd9cl1f1.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableParamArgEx/3#27355a3e/3@65f4f4js with 81 rows and digest 8bbd61u8r8ai0pfeonl5s0fub34.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableReturnPosOut/3#9b39ad82/3@78a5510h with 69 rows and digest dec2abdj608aveamnpbmlprihf0.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowImplCommon::Cached::viableReturnPosOutEx/3#4b4dd8f8/3@2c4b047u with 69 rows and digest 3c424fvt9ruc7pml0uhsbd2c1te.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::CaptureNode.getLocation/0#dispred#f73cd561/2@bc4d69oq with 21 rows and digest ba37f240vnsqdp1esfdk5jsg8i9.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaSynthReadNode.getLocation/0#dispred#53ddb37c/2@55d130e3 with 10 rows and digest 0276d3rf86footiuaaiql5o85oe.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaUseNode.getLocation/0#dispred#181610e2/2@8088ad5l with 3832 rows and digest 1d3bc7sknc7akdufn3t5egm8ab7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::CaptureNode.toStringInternal/0#dispred#4f611994/2@ca9a4fqj with 21 rows and digest e878dddvkmkm7mg32mbbtr702ke.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::FlowSummaryDefaultExceptionalReturn.toString/0#dispred#5c80071c/2@9a9eb655 with 77 rows and digest f0e6b2bsleqtl9mcavnp7fek5sb.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::FlowSummaryDynamicParameterArrayNode.toString/0#dispred#1476f62d/2@492203uv with 87 rows and digest 3f6a3e4dagdj13c3fr5m4bdskb5.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::FlowSummaryNode.toString/0#dispred#f309373d/2@2ec537h2 with 757 rows and digest 3e34eacr38ln462ioml41spnrv8.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaSynthReadNode.toString/0#dispred#1c2d943e/2@8b1c0eg7 with 10 rows and digest e35acdok7kus7rei3h4o3vt7bk7.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_DataFlowPrivate::SsaUseNode.toString/0#dispred#24796ed8/2@c065b0on with 3832 rows and digest 540ed91qsfnd0b3rsa3k1roboe6.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_ApiGraphModels::ModelOutput::resolvedSummaryRefBase/3#c83a875a/3@762aa791 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Ssa::TNode#209a3fc4/1@2603939o with 7889 rows and digest 7aa0adkfd7mnhig6jhkq1281nhd.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 with 447 rows and digest 43325c7h18ff672hcvcd7qm0tkd.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn with empty relation.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go with 289 rows and digest 681059jinboal2khcnn5qgtb7p2.
[2025-06-04 09:43:14] (0s)  >>> First-level cache hit cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t with empty relation.
[2025-06-04 09:43:14] (0s)  >>> Full cache hit cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb with 434 rows and digest 63d347vhs7sd94hfp5jnslkhod8.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b_201#join_rhs/3@117d3emi
[2025-06-04 09:43:14] (0s)  >>> Created relation Files::Container.splitAbsolutePath/2#dispred#43b82b7b_201#join_rhs/3@117d3emi with 385 rows and digest bdede4thbg0r75pd90huje66445.
[2025-06-04 09:43:14] (0s)  >>> Created relation files/2@ec93749g with 115 rows and digest 1da27am6grhuehngssp5m5htnd9.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Locations::CachedInDataFlowStage::synthLocationInfo/6#3dd0209e_10#join_rhs/2@0ea04fsk
[2025-06-04 09:43:14] (0s)  >>> Created relation Locations::CachedInDataFlowStage::synthLocationInfo/6#3dd0209e_10#join_rhs/2@0ea04fsk with 39 rows and digest 637f47k17dpjgmms1qm9vap2l51.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Locations::LocationImpl.getFile/0#dispred#9c1beba9/2@ece4e9at
[2025-06-04 09:43:14] (0s)  >>> Created relation Locations::LocationImpl.getFile/0#dispred#9c1beba9/2@ece4e9at with 192926 rows and digest 239cb3h042coq0qiqkru4r0ve53.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Locations::LocationImpl.getFile/0#dispred#9c1beba9_10#join_rhs/2@fd96ddh2
[2025-06-04 09:43:14] (0s)  >>> Created relation Locations::LocationImpl.getFile/0#dispred#9c1beba9_10#join_rhs/2@fd96ddh2 with 192926 rows and digest b78537u871sr9ma9ukqhnag3ka4.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Locations::LocationImpl.getStartLine/0#dispred#24e14924/2@262fa0u0
[2025-06-04 09:43:14] (0s)  >>> Created relation Locations::LocationImpl.getStartLine/0#dispred#24e14924/2@262fa0u0 with 192926 rows and digest 76c0faa77ea6tci2v546cr3mci7.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Locations::LocationImpl.toString/0#dispred#ee961913/2@24a2245o
[2025-06-04 09:43:14] (0s)  >>> Created relation Locations::LocationImpl.toString/0#dispred#ee961913/2@24a2245o with 192926 rows and digest 4eb8c5n53kp1uvue5f53e33se3e.
[2025-06-04 09:43:14] (0s)  >>> Created relation exprs/5@a1831agj with 61500 rows and digest f83d74t85c4ldl9dpg3028d06p5.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate exprs_10#join_rhs/2@27dbbd1m
[2025-06-04 09:43:14] (0s)  >>> Created relation exprs_10#join_rhs/2@27dbbd1m with 61500 rows and digest 6da27335elt5b4ik9drf3iig65c.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Expr::InvokeExpr#b783e907/1@f74db5bt
[2025-06-04 09:43:14] (0s)  >>> Created relation Expr::InvokeExpr#b783e907/1@f74db5bt with 142 rows and digest 26c26b2pg3feckem0aot5smps2c.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate exprs_230#join_rhs/3@dfee24vq
[2025-06-04 09:43:14] (0s)  >>> Created relation exprs_230#join_rhs/3@dfee24vq with 61500 rows and digest 121fe0ao19alrk2qhpb5jmfd474.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Expr::InvokeExpr.getArgument/1#dispred#77b465c9#fbf#cpe#13/2@017e0ftj
[2025-06-04 09:43:14] (0s)  >>> Created relation Expr::InvokeExpr.getArgument/1#dispred#77b465c9#fbf#cpe#13/2@017e0ftj with 134 rows and digest 96caceo1iqegdumvh0kr0d3iffc.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Expr::InvokeExpr.getCallee/0#dispred#6d51f52c/2@eafdf4j2
[2025-06-04 09:43:14] (0s)  >>> Created relation Expr::InvokeExpr.getCallee/0#dispred#6d51f52c/2@eafdf4j2 with 142 rows and digest c31d60m99d5cqkmh7ke8rldqkg3.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate DataFlowPrivate::Node.asExpr/0#dispred#d0c15559/2@69b579i3
[2025-06-04 09:43:14] (0s)  >>> Created relation DataFlowPrivate::Node.asExpr/0#dispred#d0c15559/2@69b579i3 with 61500 rows and digest 0d30a67p8mlb43nis3jh68te72b.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate DataFlowPrivate::Node.asExpr/0#dispred#d0c15559_10#join_rhs/2@88caa6al
[2025-06-04 09:43:14] (0s)  >>> Created relation DataFlowPrivate::Node.asExpr/0#dispred#d0c15559_10#join_rhs/2@88caa6al with 61500 rows and digest 65576ej152ffmuu8v434tgttfb6.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate m#Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb/1@ec7c11kg
[2025-06-04 09:43:14] (0s)  >>> Created relation m#Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb/1@ec7c11kg with 4 rows and digest 367aaemdshsu4mbv1t4cql2o8fe.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate m#Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/1@369f8eoc
[2025-06-04 09:43:14] (0s)  >>> Created relation m#Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/1@369f8eoc with 7 rows and digest b0c026cu2gpoj2lkdnkut242sm8.
[2025-06-04 09:43:14] (0s)  >>> Created relation typeexprs/5@5ef91d9v with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:43:14] (0s) Inferred that typeexprs_0#antijoin_rhs/1@e1d4c0cm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that typeexprs_10#join_rhs/2@af1f3114 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that typeexprs_230#join_rhs/3@fff219je is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that Classes::ClassDefinition.getASuperInterface/0#dispred#3b18c2c7/2@4e2d66km is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that typeexprs_032#join_rhs/3@53fc72rm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::NamespaceAccess#5f01a2ab#b/1@e03375b6 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that typeexprs_302#join_rhs/3@6531debh is empty, due to typeexprs/5@5ef91d9v.
[2025-06-04 09:43:14] (0s) Inferred that @function_typeexpr/1@4c1f62fv is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _const_1_typeexprs_10#join_rhs#shared/1@2b25da08 is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _#ApiGraphs::API::Node.getAMember/0#dispred#b056b81ePlus#sourceBound#2#5_typeexprs_10#join_rhs#antijoin_rhs/1@55e653qe is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalVarTypeAccess.getVariable/0#dispred#464ef61d/2@80d93fgq is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeAccess#39c9c5f2/1@ba4a59t1 is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::UnionOrIntersectionTypeExpr#76fc2ca7/1@3f7b54ur is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _typeexprs_10#join_rhs#antijoin_rhs/1@d13369g3 is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _typeexprs_10#join_rhs#antijoin_rhs#1/1@c2bf5b34 is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _typeexprs_10#join_rhs#antijoin_rhs#2/1@a04918r4 is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::VarTypeAccess#fedc5813#b/1@3bbfc7tk is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that _typeexprs_10#join_rhs_typeexprs_230#join_rhs#join_rhs/2@8f19afrm is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeAccess.hasQualifiedName/1#dispred#2888336b/2@b2edd7ab is empty, due to typeexprs_10#join_rhs/2@af1f3114.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::InterfaceDeclaration.getIdentifier/0#dispred#d076a4ed/2@399419p1 is empty, due to typeexprs_230#join_rhs/3@fff219je.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::GenericTypeExpr.getTypeAccess/0#dispred#6e3cc862/2@a1894e5p is empty, due to typeexprs_230#join_rhs/3@fff219je.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeAliasDeclaration.getIdentifier/0#dispred#795485b7/2@c6b971ke is empty, due to typeexprs_230#join_rhs/3@fff219je.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeofTypeExpr.getExpressionName/0#dispred#3568d8f6/2@bfb2daea is empty, due to typeexprs_230#join_rhs/3@fff219je.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalNamespaceName.getAMemberAccess/1#dispred#9f2bc614#bff/3@6c600av5 is empty, due to TypeScript::NamespaceAccess#5f01a2ab#b/1@e03375b6.
[2025-06-04 09:43:14] (0s) Inferred that Nest::NestJS::CustomPipeClass#2c916a38/1@74cefat3 is empty, due to Classes::ClassDefinition.getASuperInterface/0#dispred#3b18c2c7/2@4e2d66km.
[2025-06-04 09:43:14] (0s) Inferred that Functions::Function.inferNameFromFunctionType/0#dispred#6ee5e668#cpe#1/1@7b1cb7ao is empty, due to @function_typeexpr/1@4c1f62fv.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getLocalTypeName/0#dispred#f3e558c5/2@59cdaaql is empty, due to _#ApiGraphs::API::Node.getAMember/0#dispred#b056b81ePlus#sourceBound#2#5_typeexprs_10#join_rhs#antijoin_rhs/1@55e653qe.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalVarTypeAccess.getVariable/0#dispred#464ef61d_10#join_rhs/2@16a7f19p is empty, due to TypeScript::LocalVarTypeAccess.getVariable/0#dispred#464ef61d/2@80d93fgq.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeAccess.getTypeName/0#dispred#fe216587/2@4128b3lc is empty, due to TypeScript::TypeAccess#39c9c5f2/1@ba4a59t1.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeAccess.hasQualifiedName/2#dispred#c4ad3c53/3@5ebc5blb is empty, due to TypeScript::TypeAccess#39c9c5f2/1@ba4a59t1.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getAlias/0#dispred#fc9e5b0e/2@10f06cso is empty, due to TypeScript::TypeAliasDeclaration.getIdentifier/0#dispred#795485b7/2@c6b971ke.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::TypeofTypeExpr.getExpressionName/0#dispred#3568d8f6_10#join_rhs/2@0f621c5u is empty, due to TypeScript::TypeofTypeExpr.getExpressionName/0#dispred#3568d8f6/2@bfb2daea.
[2025-06-04 09:43:14] (0s) Inferred that TypeAnnotations::TypeAnnotation.hasQualifiedName/2#dispred#c588170b/3@65fac5i4 is empty, due to TypeScript::TypeAccess.hasQualifiedName/2#dispred#c4ad3c53/3@5ebc5blb.
[2025-06-04 09:43:14] (0s) Inferred that Nest::NestJS::CustomPipeClass.getAnAffectedParameter/0#dispred#ef824373/2@dd27d81e is empty, due to Nest::NestJS::CustomPipeClass#2c916a38/1@74cefat3.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getLocalTypeName/0#dispred#f3e558c5_10#join_rhs/2@c16eb79f is empty, due to TypeScript::LocalTypeAccess.getLocalTypeName/0#dispred#f3e558c5/2@59cdaaql.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getLocalTypeName/0#dispred#f3e558c5_0#antijoin_rhs/1@53f1a5bm is empty, due to TypeScript::LocalTypeAccess.getLocalTypeName/0#dispred#f3e558c5/2@59cdaaql.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getAlias/0#dispred#fc9e5b0e_0#antijoin_rhs/1@a8ad34i7 is empty, due to TypeScript::LocalTypeAccess.getAlias/0#dispred#fc9e5b0e/2@10f06cso.
[2025-06-04 09:43:14] (0s) Inferred that TypeScript::LocalTypeAccess.getAlias/0#dispred#fc9e5b0e_10#join_rhs/2@b2bf6ck4 is empty, due to TypeScript::LocalTypeAccess.getAlias/0#dispred#fc9e5b0e/2@10f06cso.
[2025-06-04 09:43:14] (0s) Inferred that project#TypeAnnotations::TypeAnnotation.hasQualifiedName/2#dispred#c588170b/1@50cceas7 is empty, due to TypeAnnotations::TypeAnnotation.hasQualifiedName/2#dispred#c588170b/3@65fac5i4.
[2025-06-04 09:43:14] (0s) Inferred that project#TypeAnnotations::TypeAnnotation.hasQualifiedName/2#dispred#c588170b#2/2@800191op is empty, due to TypeAnnotations::TypeAnnotation.hasQualifiedName/2#dispred#c588170b/3@65fac5i4.
[2025-06-04 09:43:14] (0s) Starting to evaluate predicate Expr::Identifier#299f46d8#b/1@8f40f552
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::Identifier#299f46d8#b/1@8f40f552 with 18532 rows and digest 2342f9etk0o0ojr9p9nis2o8ve1.
[2025-06-04 09:43:14] (1s)  >>> Created relation literals/3@f05366h0 with 35271 rows and digest abee41aghqnk1iifnu6cd40i64c.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate @literal#b/1@81201fg9
[2025-06-04 09:43:14] (1s)  >>> Created relation @literal#b/1@81201fg9 with 296 rows and digest 8c8b4e1u6vkn0co1nnlbqs4p75d.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::Literal.getValue/0#dispred#c75e715e#bf/2@19797dqp
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::Literal.getValue/0#dispred#c75e715e#bf/2@19797dqp with 296 rows and digest 6a513al0b7tpveio3enc8kfubp5.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/2@5a4c24m0
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::PropAccess.getPropertyName/0#dispred#80dcc485#fb/2@5a4c24m0 with 66 rows and digest 0979ccrp0oh631rn47te6o0ji37.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb/2@d979e7r3
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::InvokeExpr.getCalleeName/0#dispred#ddb25677#fb/2@d979e7r3 with 2 rows and digest 90f52b5lqecn1ck79sm32vqrn61.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate commandinjection::isCommandSink/1#e9fbac3d/1@716557ac
[2025-06-04 09:43:14] (1s)  >>> Created relation commandinjection::isCommandSink/1#e9fbac3d/1@716557ac with 2 rows and digest b533ab9cdb11imame9lh2411r45.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::PropAccess#c63e67c9/1@ebfb7bse
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::PropAccess#c63e67c9/1@ebfb7bse with 18553 rows and digest 9a88d4st57gdhtues73ne15n814.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::PropAccess.getBase/0#dispred#51cb3016/2@1fa3c18i
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::PropAccess.getBase/0#dispred#51cb3016/2@1fa3c18i with 18553 rows and digest ccf24br8o5klnas0uq6d2oq3903.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate commandinjection::isUserInputSource/1#835549a3/1@6fbaaeus
[2025-06-04 09:43:14] (1s)  >>> Created relation commandinjection::isUserInputSource/1#835549a3/1@6fbaaeus with 1 rows and digest ab17ceorbg2mks80a9m62jvbhl5.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate exprs_20#count_range/2@b5deeecs
[2025-06-04 09:43:14] (1s)  >>> Created relation exprs_20#count_range/2@b5deeecs with 61500 rows and digest 967da6c0h8uohfp8b93k21t4l61.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate literals_201#join_rhs/3@d5f6d8jq
[2025-06-04 09:43:14] (1s)  >>> Created relation literals_201#join_rhs/3@d5f6d8jq with 35271 rows and digest 28389cv2seup3qhqnlpu54rb63b.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037/2@de20e2ff
[2025-06-04 09:43:14] (1s)  >>> Created relation Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037/2@de20e2ff with 22 rows and digest 6f57ec5hf9q3in59mjcvdir7p58.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037_10#join_rhs/2@8bea99ju
[2025-06-04 09:43:14] (1s)  >>> Created relation Templates::TemplateLiteral.getAnElement/0#dispred#1ce9e037_10#join_rhs/2@8bea99ju with 22 rows and digest 9b08756cfnl8e2frsb3ls58tht6.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate m#Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb/1@8edc40g0
[2025-06-04 09:43:14] (1s)  >>> Created relation m#Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb/1@8edc40g0 with 135 rows and digest bb19348ib59rkumm9eu249h8bd5.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::BinaryExpr#18c21d7d/1@49e1f1kg
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::BinaryExpr#18c21d7d/1@49e1f1kg with 15 rows and digest 61f520cp7oe44f82ordskbbromc.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb/2@26fc21ec
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb/2@26fc21ec with 6 rows and digest a680d0a7umdh27csg62f5f8sam2.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb_10#join_rhs/2@1d3270r0
[2025-06-04 09:43:14] (1s)  >>> Created relation Expr::BinaryExpr.getAnOperand/0#dispred#15e26c28#fb_10#join_rhs/2@1d3270r0 with 6 rows and digest e85ee0kiubrlqlglqkdivusje07.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate commandinjection::isCommandConstruction/2#4c01a459#fb/2@a090d47q
[2025-06-04 09:43:14] (1s)  >>> Created relation commandinjection::isCommandConstruction/2#4c01a459#fb/2@a090d47q with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:43:14] (1s) Starting to evaluate predicate #select/2@ac596e74
[2025-06-04 09:43:14] (1s)  >>> Created relation #select/2@ac596e74 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-04 09:43:14] (1s) Inferred that project##select/1@68d113il is empty, due to #select/2@ac596e74.
[2025-06-04 09:43:14] (1s) Inferred that #select#query/8@20ca5aqj is empty, due to #select/2@ac596e74.
[2025-06-04 09:43:14] (1s) Inferred that DataFlowPrivate::Node.hasLocationInfo/5#dispred#f392a0be#bfffff/6@ab7b5brh is empty, due to project##select/1@68d113il.
[2025-06-04 09:43:14] (1s) Inferred that project#DataFlowPrivate::Node.hasLocationInfo/5#dispred#f392a0be#bfffff/1@bb268776 is empty, due to DataFlowPrivate::Node.hasLocationInfo/5#dispred#f392a0be#bfffff/6@ab7b5brh.
[2025-06-04 09:43:14] (1s) Query done
[2025-06-04 09:43:14] (1s) Sequence stamp origin is -6041943567257396668
[2025-06-04 09:43:14] (1s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-04 09:43:14] (1s) Unpausing evaluation
[2025-06-04 09:43:14] Evaluation of E:\advance_javascript\codeQL\8\codeql-exercise\queries\command-injection.ql produced BQRS results.
[2025-06-04 09:43:14] [PROGRESS] execute queries> [1/1 eval 1.3s] Evaluation done; writing results to security-queries\queries\command-injection.bqrs.
[2025-06-04 09:43:14] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-04 09:43:14] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-04 09:43:14] The disk cache is freshly trimmed; leave it be.
[2025-06-04 09:43:14] Unpausing evaluation
[2025-06-04 09:43:14] Exiting with code 0
