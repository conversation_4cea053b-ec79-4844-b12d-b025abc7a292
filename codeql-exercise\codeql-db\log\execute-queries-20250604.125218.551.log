[2025-06-04 12:52:18] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\results -- E:\advance_javascript\codeQL\8\codeql-exercise\codeql-db\db-javascript queries/sql-injection.ql
[2025-06-04 12:52:18] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --format=json -- queries/sql-injection.ql
[2025-06-04 12:52:18] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:18] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries\\sql-injection.ql"
                      ]
[2025-06-04 12:52:18] Refusing fancy output: Cannot detect terminal size on this platform
[2025-06-04 12:52:18] Creating executor with 1 threads.
[2025-06-04 12:52:19] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations queries/sql-injection.ql
[2025-06-04 12:52:19] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --allow-library-packs --format startingpacks -- queries/sql-injection.ql
[2025-06-04 12:52:19] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql) at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:19] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                      ]
[2025-06-04 12:52:19] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\8\codeql-exercise\queries
[2025-06-04 12:52:19] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 12:52:19] [SPAMMY] resolve extensions-by-pack> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 12:52:19] [SPAMMY] resolve extensions-by-pack> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 12:52:19] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 12:52:19] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-04 12:52:19] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\8\codeql-exercise\qlconfig.yml --query=E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql --format=json
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Found no pack; trying after symlink resolution with E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Found enclosing pack 'queries' at E:\advance_javascript\codeQL\8\codeql-exercise\queries.
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] [SPAMMY] resolve library-path> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-04 12:52:19] [SPAMMY] resolve library-path> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-04 12:52:19] [SPAMMY] resolve library-path> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-04 12:52:19] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\8\codeql-exercise\queries resolved OK.
[2025-06-04 12:52:19] [DETAILS] resolve library-path> Found no dbscheme through dependencies.
[2025-06-04 12:52:19] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries"
                        ],
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "queries\\sql-injection.ql",
                        "possibleAdvice" : "There should probably be a qlpack.yml file declaring dependencies in E:\\advance_javascript\\codeQL\\8\\codeql-exercise\\queries or an enclosing directory.",
                        "qlPackName" : "queries"
                      }
[2025-06-04 12:52:19] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] Resolved file set for E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql hashes to 0ef9c3042e40af22b13b51b5459e1200.
[2025-06-04 12:52:19] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql.
[2025-06-04 12:52:19] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-04 12:52:19] ExternalModuleBindingPass ...
[2025-06-04 12:52:19] ExternalModuleBindingPass time: 00:00.001
[2025-06-04 12:52:19] CollectInstantiationsPass ...
[2025-06-04 12:52:19] CollectInstantiationsPass time: 00:00.010
[2025-06-04 12:52:19] Ql checks ...
[2025-06-04 12:52:19] Ql checks time: 00:00.067
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module javascript (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:13,8-18)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:18,21-29)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type AddExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:20,10-17)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:21,12-25)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type TemplateLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:28,10-25)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type TemplateElement (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:29,12-27)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:40,29-37)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:42,10-20)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:43,21-31)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:47,10-20)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:48,21-31)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:52,10-20)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type PropAccess (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:53,21-31)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:61,29-37)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:61,50-58)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type AddExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:62,10-17)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type StringLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:65,12-25)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type TemplateLiteral (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:71,10-25)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type TemplateElement (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:74,12-27)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:82,6-14)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:82,29-37)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve module DataFlow (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:94,12-20)
[2025-06-04 12:52:20] [ERROR] execute queries> ERROR: could not resolve type AddExpr (E:\advance_javascript\codeQL\8\codeql-exercise\queries\sql-injection.ql:105,12-19)
[2025-06-04 12:52:20] Sequence stamp origin is -6041894824499024697
[2025-06-04 12:52:20] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-04 12:52:20] The disk cache is freshly trimmed; leave it be.
[2025-06-04 12:52:20] Unpausing evaluation
[2025-06-04 12:52:20] Exiting with code 2
