/**
 * @name Command Injection Detection
 * @description Detects potential command injection vulnerabilities
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/command-injection-custom
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript

/**
 * Predicate to identify command execution sinks
 */
predicate isCommandSink(DataFlow::Node sink) {
  exists(CallExpr call |
    (
      // Direct command execution calls
      call.getCalleeName() = "exec" or
      call.getCalleeName() = "execSync" or
      call.getCalleeName() = "spawn" or
      call.getCalleeName() = "spawnSync"
    ) and
    sink.asExpr() = call.getArgument(0)
  ) or
  exists(CallExpr call |
    // child_process module calls
    exists(PropAccess prop |
      prop.getPropertyName() = "exec" or
      prop.getPropertyName() = "execSync" or
      prop.getPropertyName() = "spawn" or
      prop.getPropertyName() = "spawnSync"
    |
      call.getCallee() = prop and
      sink.asExpr() = call.getArgument(0)
    )
  )
}

/**
 * Predicate to identify user input sources
 */
predicate isUserInputSource(DataFlow::Node source) {
  // HTTP request body parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "body" and
    source.asExpr() = prop
  ) or
  // HTTP request URL parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "params" and
    source.asExpr() = prop
  ) or
  // HTTP request query parameters
  exists(PropAccess prop |
    prop.getBase().(PropAccess).getPropertyName() = "query" and
    source.asExpr() = prop
  )
}

/**
 * Predicate to identify string concatenation that builds commands
 */
predicate isCommandConstruction(DataFlow::Node node, DataFlow::Node userInput) {
  exists(AddExpr add |
    // One operand is user input, another contains command keywords
    add.getAnOperand() = userInput.asExpr() and
    exists(StringLiteral str |
      str = add.getAnOperand() and
      str.getValue().regexpMatch(".*(tar|cp|mv|rm|cat|ls|ps|kill|wget|curl|ping).*")
    ) and
    node.asExpr() = add
  ) or
  exists(TemplateLiteral template |
    // Template literal with user input and command keywords
    template.getAnElement() = userInput.asExpr() and
    exists(TemplateElement elem |
      elem = template.getAnElement() and
      elem.getValue().regexpMatch(".*(tar|cp|mv|rm|cat|ls|ps|kill|wget|curl|ping).*")
    ) and
    node.asExpr() = template
  )
}

from DataFlow::Node source, DataFlow::Node sink, string message
where
  (
    // Direct flow from user input to command execution
    isUserInputSource(source) and
    isCommandSink(sink) and
    source = sink and
    message = "Command injection: user input flows directly to command execution"
  ) or
  (
    // Flow through command construction
    isUserInputSource(source) and
    exists(DataFlow::Node construction |
      isCommandConstruction(construction, source) and
      isCommandSink(sink) and
      construction = sink
    ) and
    message = "Command injection: user input used in command construction"
  ) or
  (
    // Simple case: user input in command string
    isUserInputSource(source) and
    isCommandSink(sink) and
    exists(AddExpr add |
      add.getAnOperand() = source.asExpr() and
      sink.asExpr() = add
    ) and
    message = "Command injection: user input concatenated into command"
  )
select sink, message + " at " + sink.getLocation().toString()