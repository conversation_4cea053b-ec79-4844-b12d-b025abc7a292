/**
 * @name Command Injection Detection
 * @description Detects potential command injection vulnerabilities
 * @kind path-problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/command-injection-custom
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript
import DataFlow::PathGraph
import semmle.javascript.security.dataflow.CommandInjection

class CommandInjectionConfiguration extends CommandInjection::Configuration {
  CommandInjectionConfiguration() { this = "CommandInjectionConfiguration" }

  override predicate isSource(DataFlow::Node source) {
    source instanceof RemoteFlowSource
  }

  override predicate isSink(DataFlow::Node sink) {
    // Using the command execution sinks from the standard library
    sink instanceof CommandInjection::Sink
  }

  override predicate isSanitizer(DataFlow::Node node) {
    exists(CallExpr call |
      call.getCalleeName().regexpMatch("(?i).*(escape|sanitize|quote).*") and
      node.asExpr() = call
    )
  }
}

from CommandInjectionConfiguration config, DataFlow::PathNode source, DataFlow::PathNode sink
where config.hasFlowPath(source, sink)
select sink.getNode(), source, sink, "Command injection vulnerability: user input flows to command execution."