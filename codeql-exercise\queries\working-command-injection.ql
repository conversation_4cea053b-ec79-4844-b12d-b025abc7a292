/**
 * @name Working Command Injection Detection
 * @description Properly detects command injection with intermediate variables
 * @kind problem
 * @problem.severity error
 * @security-severity 9.0
 * @precision high
 * @id js/working-command-injection
 * @tags security
 *       external/cwe/cwe-78
 */

import javascript

/**
 * Check if a variable comes from user input
 */
predicate isUserInputVariable(Variable var) {
  exists(VariableDeclarator decl, DestructuringPattern pattern, PropAccess prop |
    decl.getBindingPattern() = pattern and
    pattern.getAVariable() = var and
    decl.getInit() = prop and
    (prop.getBase().(PropAccess).getPropertyName() = "body" or
     prop.getBase().(PropAccess).getPropertyName() = "params" or
     prop.getBase().(PropAccess).getPropertyName() = "query")
  )
}

/**
 * Check if an expression contains user input
 */
predicate containsUserInput(Expr expr) {
  // Direct variable access from user input
  exists(VarAccess var |
    var = expr and
    isUserInputVariable(var.getVariable())
  ) or
  // Template literal with user input variable
  exists(TemplateLiteral template |
    template = expr and
    exists(VarAccess var |
      var = template.getAnElement() and
      isUserInputVariable(var.getVariable())
    )
  ) or
  // String concatenation with user input
  exists(AddExpr add |
    add = expr and
    (containsUserInput(add.getLeftOperand()) or containsUserInput(add.getRightOperand()))
  )
}

from CallExpr call, Expr commandArg, string message
where
  // Find command execution calls
  (call.getCalleeName() = "exec" or
   exists(PropAccess prop |
     prop.getPropertyName() = "exec" and
     call.getCallee() = prop
   )) and
  commandArg = call.getArgument(0) and
  containsUserInput(commandArg) and
  message = "Command injection: user input flows to command execution"
select call, message + " - Command: " + commandArg.toString() + " (File: " + call.getFile().getBaseName() + ", Line: " + call.getLocation().getStartLine() + ")"
