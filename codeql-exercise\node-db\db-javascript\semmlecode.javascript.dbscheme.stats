<dbstats>
<typesizes>

<e>
<k>@location_default</k>
<v>15664049</v>
</e>
<e>
<k>@file</k>
<v>6457</v>
</e>
<e>
<k>@folder</k>
<v>1590</v>
</e>
<e>
<k>@externalDataElement</k>
<v>950</v>
</e>
<e>
<k>@toplevel</k>
<v>5320</v>
</e>
<e>
<k>@script</k>
<v>5200</v>
</e>
<e>
<k>@inline_script</k>
<v>86</v>
</e>
<e>
<k>@event_handler</k>
<v>31</v>
</e>
<e>
<k>@javascript_url</k>
<v>3</v>
</e>
<e>
<k>@template_toplevel</k>
<v>100</v>
</e>
<e>
<k>@stmt</k>
<v>1096691</v>
</e>
<e>
<k>@empty_stmt</k>
<v>1136</v>
</e>
<e>
<k>@block_stmt</k>
<v>204994</v>
</e>
<e>
<k>@expr_stmt</k>
<v>610340</v>
</e>
<e>
<k>@if_stmt</k>
<v>68214</v>
</e>
<e>
<k>@labeled_stmt</k>
<v>1378</v>
</e>
<e>
<k>@break_stmt</k>
<v>10149</v>
</e>
<e>
<k>@continue_stmt</k>
<v>1642</v>
</e>
<e>
<k>@with_stmt</k>
<v>4</v>
</e>
<e>
<k>@switch_stmt</k>
<v>1569</v>
</e>
<e>
<k>@return_stmt</k>
<v>48209</v>
</e>
<e>
<k>@throw_stmt</k>
<v>2305</v>
</e>
<e>
<k>@try_stmt</k>
<v>1316</v>
</e>
<e>
<k>@while_stmt</k>
<v>3120</v>
</e>
<e>
<k>@do_while_stmt</k>
<v>1471</v>
</e>
<e>
<k>@for_stmt</k>
<v>5385</v>
</e>
<e>
<k>@for_in_stmt</k>
<v>1315</v>
</e>
<e>
<k>@debugger_stmt</k>
<v>3</v>
</e>
<e>
<k>@function_decl_stmt</k>
<v>16771</v>
</e>
<e>
<k>@var_decl_stmt</k>
<v>105606</v>
</e>
<e>
<k>@case</k>
<v>8674</v>
</e>
<e>
<k>@catch_clause</k>
<v>1272</v>
</e>
<e>
<k>@for_of_stmt</k>
<v>61</v>
</e>
<e>
<k>@const_decl_stmt</k>
<v>1118</v>
</e>
<e>
<k>@let_stmt</k>
<v>551</v>
</e>
<e>
<k>@legacy_let_stmt</k>
<v>1</v>
</e>
<e>
<k>@for_each_stmt</k>
<v>1</v>
</e>
<e>
<k>@class_decl_stmt</k>
<v>41</v>
</e>
<e>
<k>@import_declaration</k>
<v>8</v>
</e>
<e>
<k>@export_all_declaration</k>
<v>1</v>
</e>
<e>
<k>@export_as_namespace_declaration</k>
<v>5</v>
</e>
<e>
<k>@global_augmentation_declaration</k>
<v>5</v>
</e>
<e>
<k>@using_decl_stmt</k>
<v>5</v>
</e>
<e>
<k>@export_default_declaration</k>
<v>5</v>
</e>
<e>
<k>@export_named_declaration</k>
<v>31</v>
</e>
<e>
<k>@expr</k>
<v>5495305</v>
</e>
<e>
<k>@label</k>
<v>722373</v>
</e>
<e>
<k>@null_literal</k>
<v>15525</v>
</e>
<e>
<k>@boolean_literal</k>
<v>31652</v>
</e>
<e>
<k>@number_literal</k>
<v>557620</v>
</e>
<e>
<k>@string_literal</k>
<v>268843</v>
</e>
<e>
<k>@regexp_literal</k>
<v>2773</v>
</e>
<e>
<k>@this_expr</k>
<v>128651</v>
</e>
<e>
<k>@array_expr</k>
<v>28131</v>
</e>
<e>
<k>@obj_expr</k>
<v>50958</v>
</e>
<e>
<k>@function_expr</k>
<v>95744</v>
</e>
<e>
<k>@seq_expr</k>
<v>2457</v>
</e>
<e>
<k>@conditional_expr</k>
<v>8111</v>
</e>
<e>
<k>@new_expr</k>
<v>19023</v>
</e>
<e>
<k>@call_expr</k>
<v>487075</v>
</e>
<e>
<k>@dot_expr</k>
<v>602582</v>
</e>
<e>
<k>@index_expr</k>
<v>105192</v>
</e>
<e>
<k>@neg_expr</k>
<v>11993</v>
</e>
<e>
<k>@plus_expr</k>
<v>731</v>
</e>
<e>
<k>@log_not_expr</k>
<v>19385</v>
</e>
<e>
<k>@bit_not_expr</k>
<v>403</v>
</e>
<e>
<k>@typeof_expr</k>
<v>4540</v>
</e>
<e>
<k>@void_expr</k>
<v>51</v>
</e>
<e>
<k>@delete_expr</k>
<v>1310</v>
</e>
<e>
<k>@eq_expr</k>
<v>13468</v>
</e>
<e>
<k>@neq_expr</k>
<v>5338</v>
</e>
<e>
<k>@eqq_expr</k>
<v>17758</v>
</e>
<e>
<k>@neqq_expr</k>
<v>5818</v>
</e>
<e>
<k>@lt_expr</k>
<v>10254</v>
</e>
<e>
<k>@le_expr</k>
<v>1503</v>
</e>
<e>
<k>@gt_expr</k>
<v>5438</v>
</e>
<e>
<k>@ge_expr</k>
<v>2527</v>
</e>
<e>
<k>@lshift_expr</k>
<v>5655</v>
</e>
<e>
<k>@rshift_expr</k>
<v>27749</v>
</e>
<e>
<k>@urshift_expr</k>
<v>4331</v>
</e>
<e>
<k>@add_expr</k>
<v>88032</v>
</e>
<e>
<k>@sub_expr</k>
<v>10789</v>
</e>
<e>
<k>@mul_expr</k>
<v>14075</v>
</e>
<e>
<k>@div_expr</k>
<v>2496</v>
</e>
<e>
<k>@mod_expr</k>
<v>655</v>
</e>
<e>
<k>@bitor_expr</k>
<v>42853</v>
</e>
<e>
<k>@xor_expr</k>
<v>503</v>
</e>
<e>
<k>@bitand_expr</k>
<v>8538</v>
</e>
<e>
<k>@in_expr</k>
<v>1135</v>
</e>
<e>
<k>@instanceof_expr</k>
<v>1184</v>
</e>
<e>
<k>@logand_expr</k>
<v>15892</v>
</e>
<e>
<k>@logor_expr</k>
<v>12711</v>
</e>
<e>
<k>@assign_expr</k>
<v>245084</v>
</e>
<e>
<k>@assign_add_expr</k>
<v>6231</v>
</e>
<e>
<k>@assign_sub_expr</k>
<v>823</v>
</e>
<e>
<k>@assign_mul_expr</k>
<v>143</v>
</e>
<e>
<k>@assign_div_expr</k>
<v>44</v>
</e>
<e>
<k>@assign_mod_expr</k>
<v>17</v>
</e>
<e>
<k>@assign_lshift_expr</k>
<v>57</v>
</e>
<e>
<k>@assign_rshift_expr</k>
<v>86</v>
</e>
<e>
<k>@assign_urshift_expr</k>
<v>96</v>
</e>
<e>
<k>@assign_or_expr</k>
<v>586</v>
</e>
<e>
<k>@assign_xor_expr</k>
<v>108</v>
</e>
<e>
<k>@assign_and_expr</k>
<v>222</v>
</e>
<e>
<k>@assignlogandexpr</k>
<v>1</v>
</e>
<e>
<k>@assignlogorexpr</k>
<v>1</v>
</e>
<e>
<k>@assignnullishcoalescingexpr</k>
<v>1</v>
</e>
<e>
<k>@template_placeholder_tag</k>
<v>100</v>
</e>
<e>
<k>@template_pipe_ref</k>
<v>100</v>
</e>
<e>
<k>@generated_code_expr</k>
<v>100</v>
</e>
<e>
<k>@satisfies_expr</k>
<v>100</v>
</e>
<e>
<k>@preinc_expr</k>
<v>1792</v>
</e>
<e>
<k>@postinc_expr</k>
<v>7103</v>
</e>
<e>
<k>@predec_expr</k>
<v>457</v>
</e>
<e>
<k>@postdec_expr</k>
<v>774</v>
</e>
<e>
<k>@par_expr</k>
<v>86199</v>
</e>
<e>
<k>@var_declarator</k>
<v>130843</v>
</e>
<e>
<k>@arrow_function_expr</k>
<v>3730</v>
</e>
<e>
<k>@spread_element</k>
<v>50</v>
</e>
<e>
<k>@array_pattern</k>
<v>57</v>
</e>
<e>
<k>@object_pattern</k>
<v>122</v>
</e>
<e>
<k>@yield_expr</k>
<v>81</v>
</e>
<e>
<k>@tagged_template_expr</k>
<v>27</v>
</e>
<e>
<k>@template_literal</k>
<v>408</v>
</e>
<e>
<k>@template_literal_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@template_element</k>
<v>639</v>
</e>
<e>
<k>@array_comprehension_expr</k>
<v>3</v>
</e>
<e>
<k>@generator_expr</k>
<v>1</v>
</e>
<e>
<k>@for_in_comprehension_block</k>
<v>1</v>
</e>
<e>
<k>@for_of_comprehension_block</k>
<v>3</v>
</e>
<e>
<k>@legacy_letexpr</k>
<v>1</v>
</e>
<e>
<k>@var_decl</k>
<v>250257</v>
</e>
<e>
<k>@proper_varaccess</k>
<v>1295408</v>
</e>
<e>
<k>@super_expr</k>
<v>11</v>
</e>
<e>
<k>@newtarget_expr</k>
<v>1</v>
</e>
<e>
<k>@import_meta_expr</k>
<v>1</v>
</e>
<e>
<k>@named_import_specifier</k>
<v>4</v>
</e>
<e>
<k>@import_default_specifier</k>
<v>4</v>
</e>
<e>
<k>@import_namespace_specifier</k>
<v>2</v>
</e>
<e>
<k>@named_export_specifier</k>
<v>5</v>
</e>
<e>
<k>@export_default_specifier</k>
<v>5</v>
</e>
<e>
<k>@export_namespace_specifier</k>
<v>5</v>
</e>
<e>
<k>@export_assign_declaration</k>
<v>5</v>
</e>
<e>
<k>@interface_declaration</k>
<v>5</v>
</e>
<e>
<k>@type_alias_declaration</k>
<v>120</v>
</e>
<e>
<k>@enum_declaration</k>
<v>252</v>
</e>
<e>
<k>@external_module_declaration</k>
<v>100</v>
</e>
<e>
<k>@external_module_reference</k>
<v>5</v>
</e>
<e>
<k>@expression_with_type_arguments</k>
<v>45</v>
</e>
<e>
<k>@prefix_type_assertion</k>
<v>1721</v>
</e>
<e>
<k>@as_type_assertion</k>
<v>368</v>
</e>
<e>
<k>@export_varaccess</k>
<v>15</v>
</e>
<e>
<k>@decorator_list</k>
<v>2575</v>
</e>
<e>
<k>@non_null_assertion</k>
<v>2159</v>
</e>
<e>
<k>@dynamic_import</k>
<v>5</v>
</e>
<e>
<k>@import_equals_declaration</k>
<v>5</v>
</e>
<e>
<k>@namespace_declaration</k>
<v>5</v>
</e>
<e>
<k>@namespace_scope</k>
<v>5</v>
</e>
<e>
<k>@exp_expr</k>
<v>14075</v>
</e>
<e>
<k>@assign_exp_expr</k>
<v>143</v>
</e>
<e>
<k>@class_expr</k>
<v>41</v>
</e>
<e>
<k>@scope</k>
<v>118172</v>
</e>
<e>
<k>@global_scope</k>
<v>1</v>
</e>
<e>
<k>@function_scope</k>
<v>116245</v>
</e>
<e>
<k>@catch_scope</k>
<v>1272</v>
</e>
<e>
<k>@module_scope</k>
<v>21</v>
</e>
<e>
<k>@block_scope</k>
<v>584</v>
</e>
<e>
<k>@for_scope</k>
<v>17</v>
</e>
<e>
<k>@for_in_scope</k>
<v>28</v>
</e>
<e>
<k>@comprehension_block_scope</k>
<v>4</v>
</e>
<e>
<k>@class_expr_scope</k>
<v>41</v>
</e>
<e>
<k>@class_decl_scope</k>
<v>2693</v>
</e>
<e>
<k>@interface_scope</k>
<v>200</v>
</e>
<e>
<k>@type_alias_scope</k>
<v>11</v>
</e>
<e>
<k>@enum_scope</k>
<v>252</v>
</e>
<e>
<k>@external_module_scope</k>
<v>100</v>
</e>
<e>
<k>@mapped_type_scope</k>
<v>10</v>
</e>
<e>
<k>@conditional_type_scope</k>
<v>100</v>
</e>
<e>
<k>@variable</k>
<v>364388</v>
</e>
<e>
<k>@local_type_name</k>
<v>23565</v>
</e>
<e>
<k>@local_namespace_name</k>
<v>20832</v>
</e>
<e>
<k>@property</k>
<v>142723</v>
</e>
<e>
<k>@value_property</k>
<v>140856</v>
</e>
<e>
<k>@property_getter</k>
<v>1529</v>
</e>
<e>
<k>@property_setter</k>
<v>338</v>
</e>
<e>
<k>@jsx_attribute</k>
<v>100</v>
</e>
<e>
<k>@function_call_signature</k>
<v>2458</v>
</e>
<e>
<k>@constructor_call_signature</k>
<v>37</v>
</e>
<e>
<k>@index_signature</k>
<v>504</v>
</e>
<e>
<k>@enum_member</k>
<v>2026</v>
</e>
<e>
<k>@proper_field</k>
<v>16934</v>
</e>
<e>
<k>@parameter_field</k>
<v>2693</v>
</e>
<e>
<k>@static_initializer</k>
<v>100</v>
</e>
<e>
<k>@local_type_access</k>
<v>25491</v>
</e>
<e>
<k>@type_decl</k>
<v>2513</v>
</e>
<e>
<k>@keyword_typeexpr</k>
<v>25306</v>
</e>
<e>
<k>@string_literal_typeexpr</k>
<v>733</v>
</e>
<e>
<k>@number_literal_typeexpr</k>
<v>3</v>
</e>
<e>
<k>@boolean_literal_typeexpr</k>
<v>4</v>
</e>
<e>
<k>@array_typeexpr</k>
<v>4579</v>
</e>
<e>
<k>@union_typeexpr</k>
<v>852</v>
</e>
<e>
<k>@intersection_typeexpr</k>
<v>27</v>
</e>
<e>
<k>@parenthesized_typeexpr</k>
<v>62</v>
</e>
<e>
<k>@tuple_typeexpr</k>
<v>98</v>
</e>
<e>
<k>@keyof_typeexpr</k>
<v>3</v>
</e>
<e>
<k>@indexed_access_typeexpr</k>
<v>3</v>
</e>
<e>
<k>@qualified_type_access</k>
<v>3559</v>
</e>
<e>
<k>@import_namespace_access</k>
<v>100</v>
</e>
<e>
<k>@import_type_access</k>
<v>100</v>
</e>
<e>
<k>@import_var_type_access</k>
<v>100</v>
</e>
<e>
<k>@optional_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@rest_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@readonly_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@bigint_literal_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@generic_typeexpr</k>
<v>5220</v>
</e>
<e>
<k>@type_label</k>
<v>3559</v>
</e>
<e>
<k>@typeof_typeexpr</k>
<v>24</v>
</e>
<e>
<k>@local_var_type_access</k>
<v>24</v>
</e>
<e>
<k>@qualified_var_type_access</k>
<v>15</v>
</e>
<e>
<k>@this_var_type_access</k>
<v>20</v>
</e>
<e>
<k>@predicate_typeexpr</k>
<v>86</v>
</e>
<e>
<k>@interface_typeexpr</k>
<v>1038</v>
</e>
<e>
<k>@type_parameter</k>
<v>3463</v>
</e>
<e>
<k>@plain_function_typeexpr</k>
<v>1674</v>
</e>
<e>
<k>@local_namespace_access</k>
<v>4671</v>
</e>
<e>
<k>@qualified_namespace_access</k>
<v>20</v>
</e>
<e>
<k>@constructor_typeexpr</k>
<v>20</v>
</e>
<e>
<k>@mapped_typeexpr</k>
<v>20</v>
</e>
<e>
<k>@conditional_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@infer_typeexpr</k>
<v>100</v>
</e>
<e>
<k>@comment</k>
<v>104947</v>
</e>
<e>
<k>@any_type</k>
<v>1</v>
</e>
<e>
<k>@string_type</k>
<v>1</v>
</e>
<e>
<k>@number_type</k>
<v>1</v>
</e>
<e>
<k>@union_type</k>
<v>1802</v>
</e>
<e>
<k>@true_type</k>
<v>1</v>
</e>
<e>
<k>@false_type</k>
<v>1</v>
</e>
<e>
<k>@type_reference</k>
<v>12383</v>
</e>
<e>
<k>@object_type</k>
<v>159099</v>
</e>
<e>
<k>@canonical_type_variable_type</k>
<v>650</v>
</e>
<e>
<k>@typeof_type</k>
<v>2903</v>
</e>
<e>
<k>@void_type</k>
<v>1</v>
</e>
<e>
<k>@undefined_type</k>
<v>1</v>
</e>
<e>
<k>@null_type</k>
<v>1</v>
</e>
<e>
<k>@never_type</k>
<v>1</v>
</e>
<e>
<k>@plain_symbol_type</k>
<v>1</v>
</e>
<e>
<k>@objectkeyword_type</k>
<v>1</v>
</e>
<e>
<k>@intersection_type</k>
<v>369</v>
</e>
<e>
<k>@tuple_type</k>
<v>307</v>
</e>
<e>
<k>@lexical_type_variable_type</k>
<v>50</v>
</e>
<e>
<k>@this_type</k>
<v>2731</v>
</e>
<e>
<k>@number_literal_type</k>
<v>1244</v>
</e>
<e>
<k>@string_literal_type</k>
<v>30638</v>
</e>
<e>
<k>@unknown_type</k>
<v>100</v>
</e>
<e>
<k>@bigint_type</k>
<v>100</v>
</e>
<e>
<k>@bigint_literal_type</k>
<v>100</v>
</e>
<e>
<k>@unique_symbol_type</k>
<v>100</v>
</e>
<e>
<k>@root_symbol</k>
<v>2385</v>
</e>
<e>
<k>@member_symbol</k>
<v>7223</v>
</e>
<e>
<k>@other_symbol</k>
<v>584</v>
</e>
<e>
<k>@function_signature_type</k>
<v>34698</v>
</e>
<e>
<k>@constructor_signature_type</k>
<v>2646</v>
</e>
<e>
<k>@slashslash_comment</k>
<v>76841</v>
</e>
<e>
<k>@slashstar_comment</k>
<v>8834</v>
</e>
<e>
<k>@doc_comment</k>
<v>19270</v>
</e>
<e>
<k>@html_comment_start</k>
<v>1</v>
</e>
<e>
<k>@htmlcommentend</k>
<v>1</v>
</e>
<e>
<k>@line</k>
<v>1622184</v>
</e>
<e>
<k>@js_parse_error</k>
<v>8</v>
</e>
<e>
<k>@regexpterm</k>
<v>33197</v>
</e>
<e>
<k>@regexp_alt</k>
<v>641</v>
</e>
<e>
<k>@regexp_seq</k>
<v>3371</v>
</e>
<e>
<k>@regexp_caret</k>
<v>826</v>
</e>
<e>
<k>@regexp_dollar</k>
<v>637</v>
</e>
<e>
<k>@regexp_wordboundary</k>
<v>99</v>
</e>
<e>
<k>@regexp_nonwordboundary</k>
<v>3</v>
</e>
<e>
<k>@regexp_positive_lookahead</k>
<v>15</v>
</e>
<e>
<k>@regexp_negative_lookahead</k>
<v>12</v>
</e>
<e>
<k>@regexp_star</k>
<v>1057</v>
</e>
<e>
<k>@regexp_plus</k>
<v>1067</v>
</e>
<e>
<k>@regexp_opt</k>
<v>478</v>
</e>
<e>
<k>@regexp_range</k>
<v>146</v>
</e>
<e>
<k>@regexp_dot</k>
<v>445</v>
</e>
<e>
<k>@regexp_group</k>
<v>1692</v>
</e>
<e>
<k>@regexp_normal_constant</k>
<v>15489</v>
</e>
<e>
<k>@regexp_hex_escape</k>
<v>59</v>
</e>
<e>
<k>@regexp_unicode_escape</k>
<v>264</v>
</e>
<e>
<k>@regexp_dec_escape</k>
<v>7</v>
</e>
<e>
<k>@regexp_oct_escape</k>
<v>1</v>
</e>
<e>
<k>@regexp_ctrl_escape</k>
<v>599</v>
</e>
<e>
<k>@regexp_char_class_escape</k>
<v>1573</v>
</e>
<e>
<k>@regexp_id_escape</k>
<v>2613</v>
</e>
<e>
<k>@regexp_backref</k>
<v>11</v>
</e>
<e>
<k>@regexp_char_class</k>
<v>1473</v>
</e>
<e>
<k>@regexp_char_range</k>
<v>619</v>
</e>
<e>
<k>@regexp_positive_lookbehind</k>
<v>15</v>
</e>
<e>
<k>@regexp_negative_lookbehind</k>
<v>12</v>
</e>
<e>
<k>@regexp_unicode_property_escape</k>
<v>12</v>
</e>
<e>
<k>@regexp_quoted_string</k>
<v>12</v>
</e>
<e>
<k>@regexp_intersection</k>
<v>12</v>
</e>
<e>
<k>@regexp_subtraction</k>
<v>12</v>
</e>
<e>
<k>@regexp_parse_error</k>
<v>122</v>
</e>
<e>
<k>@token</k>
<v>8770869</v>
</e>
<e>
<k>@token_eof</k>
<v>5312</v>
</e>
<e>
<k>@token_null_literal</k>
<v>15526</v>
</e>
<e>
<k>@token_boolean_literal</k>
<v>31654</v>
</e>
<e>
<k>@token_numeric_literal</k>
<v>557620</v>
</e>
<e>
<k>@token_string_literal</k>
<v>269555</v>
</e>
<e>
<k>@token_regular_expression</k>
<v>2773</v>
</e>
<e>
<k>@token_identifier</k>
<v>2268328</v>
</e>
<e>
<k>@token_keyword</k>
<v>551767</v>
</e>
<e>
<k>@token_punctuator</k>
<v>5068334</v>
</e>
<e>
<k>@json_value</k>
<v>1643352</v>
</e>
<e>
<k>@json_null</k>
<v>24</v>
</e>
<e>
<k>@json_boolean</k>
<v>654</v>
</e>
<e>
<k>@json_number</k>
<v>273113</v>
</e>
<e>
<k>@json_string</k>
<v>752355</v>
</e>
<e>
<k>@json_array</k>
<v>175925</v>
</e>
<e>
<k>@json_object</k>
<v>441281</v>
</e>
<e>
<k>@json_parse_error</k>
<v>1</v>
</e>
<e>
<k>@entry_node</k>
<v>121542</v>
</e>
<e>
<k>@exit_node</k>
<v>121542</v>
</e>
<e>
<k>@guard_node</k>
<v>177785</v>
</e>
<e>
<k>@jsdoc</k>
<v>19270</v>
</e>
<e>
<k>@falsy_guard</k>
<v>86336</v>
</e>
<e>
<k>@truthy_guard</k>
<v>91449</v>
</e>
<e>
<k>@jsdoc_tag</k>
<v>29323</v>
</e>
<e>
<k>@jsdoc_type_expr</k>
<v>22481</v>
</e>
<e>
<k>@jsdoc_any_type_expr</k>
<v>292</v>
</e>
<e>
<k>@jsdoc_null_type_expr</k>
<v>35</v>
</e>
<e>
<k>@jsdoc_undefined_type_expr</k>
<v>287</v>
</e>
<e>
<k>@jsdoc_unknown_type_expr</k>
<v>27</v>
</e>
<e>
<k>@jsdoc_void_type_expr</k>
<v>8</v>
</e>
<e>
<k>@jsdoc_identifier_type_expr</k>
<v>18639</v>
</e>
<e>
<k>@jsdoc_qualified_type_expr</k>
<v>1000</v>
</e>
<e>
<k>@jsdoc_applied_type_expr</k>
<v>303</v>
</e>
<e>
<k>@jsdoc_nullable_type_expr</k>
<v>310</v>
</e>
<e>
<k>@jsdoc_non_nullable_type_expr</k>
<v>536</v>
</e>
<e>
<k>@jsdoc_record_type_expr</k>
<v>91</v>
</e>
<e>
<k>@jsdoc_array_type_expr</k>
<v>19</v>
</e>
<e>
<k>@jsdoc_union_type_expr</k>
<v>668</v>
</e>
<e>
<k>@jsdoc_function_type_expr</k>
<v>316</v>
</e>
<e>
<k>@jsdoc_optional_type_expr</k>
<v>895</v>
</e>
<e>
<k>@jsdoc_rest_type_expr</k>
<v>55</v>
</e>
<e>
<k>@jsdoc_error</k>
<v>1658</v>
</e>
<e>
<k>@yaml_node</k>
<v>885</v>
</e>
<e>
<k>@yaml_scalar_node</k>
<v>700</v>
</e>
<e>
<k>@yaml_mapping_node</k>
<v>149</v>
</e>
<e>
<k>@yaml_sequence_node</k>
<v>35</v>
</e>
<e>
<k>@yaml_alias_node</k>
<v>1</v>
</e>
<e>
<k>@yaml_error</k>
<v>1</v>
</e>
<e>
<k>@jsx_element</k>
<v>1090</v>
</e>
<e>
<k>@jsx_qualified_name</k>
<v>100</v>
</e>
<e>
<k>@jsx_empty_expr</k>
<v>100</v>
</e>
<e>
<k>@await_expr</k>
<v>100</v>
</e>
<e>
<k>@function_sent_expr</k>
<v>100</v>
</e>
<e>
<k>@decorator</k>
<v>100</v>
</e>
<e>
<k>@bind_expr</k>
<v>100</v>
</e>
<e>
<k>@bigint_literal</k>
<v>100</v>
</e>
<e>
<k>@nullishcoalescing_expr</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_anyname</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_static_attribute_selector</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_dynamic_attribute_selector</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_filter_expression</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_static_qualident</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_dynamic_qualident</k>
<v>100</v>
</e>
<e>
<k>@e4x_xml_dotdotexpr</k>
<v>100</v>
</e>
<e>
<k>@xmldtd</k>
<v>1</v>
</e>
<e>
<k>@xmlelement</k>
<v>1270313</v>
</e>
<e>
<k>@xmlattribute</k>
<v>1202020</v>
</e>
<e>
<k>@xmlnamespace</k>
<v>4185</v>
</e>
<e>
<k>@xmlcomment</k>
<v>26812</v>
</e>
<e>
<k>@xmlcharacters</k>
<v>439958</v>
</e>
<e>
<k>@optionalchainable</k>
<v>100</v>
</e>
<e>
<k>@nullishcoalescing_expr</k>
<v>100</v>
</e>
<e>
<k>@config</k>
<v>69795</v>
</e>
<e>
<k>@configName</k>
<v>69794</v>
</e>
<e>
<k>@configValue</k>
<v>69691</v>
</e>
</typesizes>
<stats>

<relation>
<name>locations_default</name>
<key>id</key>
<cardinality>15664049</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>15664049</v>
</e>
<e>
<k>file</k>
<v>6457</v>
</e>
<e>
<k>beginLine</k>
<v>277405</v>
</e>
<e>
<k>beginColumn</k>
<v>117878</v>
</e>
<e>
<k>endLine</k>
<v>277405</v>
</e>
<e>
<k>endColumn</k>
<v>117868</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15664049</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15664049</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15664049</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15664049</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15664049</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>674</v>
</b>
<b>
<a>2</a>
<b>28</b>
<v>501</v>
</b>
<b>
<a>28</a>
<b>105</b>
<v>488</v>
</b>
<b>
<a>105</a>
<b>211</b>
<v>488</v>
</b>
<b>
<a>211</a>
<b>335</b>
<v>490</v>
</b>
<b>
<a>335</a>
<b>477</b>
<v>485</v>
</b>
<b>
<a>477</a>
<b>637</b>
<v>488</v>
</b>
<b>
<a>637</a>
<b>856</b>
<v>486</v>
</b>
<b>
<a>856</a>
<b>1141</b>
<v>485</v>
</b>
<b>
<a>1141</a>
<b>1602</b>
<v>485</v>
</b>
<b>
<a>1604</a>
<b>2336</b>
<v>486</v>
</b>
<b>
<a>2336</a>
<b>4472</b>
<v>485</v>
</b>
<b>
<a>4472</a>
<b>2368854</b>
<v>416</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>674</v>
</b>
<b>
<a>2</a>
<b>13</b>
<v>509</v>
</b>
<b>
<a>13</a>
<b>23</b>
<v>513</v>
</b>
<b>
<a>23</a>
<b>35</b>
<v>516</v>
</b>
<b>
<a>35</a>
<b>50</b>
<v>504</v>
</b>
<b>
<a>50</a>
<b>69</b>
<v>506</v>
</b>
<b>
<a>69</a>
<b>92</b>
<v>489</v>
</b>
<b>
<a>92</a>
<b>124</b>
<v>504</v>
</b>
<b>
<a>124</a>
<b>165</b>
<v>487</v>
</b>
<b>
<a>165</a>
<b>230</b>
<v>490</v>
</b>
<b>
<a>230</a>
<b>357</b>
<v>491</v>
</b>
<b>
<a>357</a>
<b>737</b>
<v>485</v>
</b>
<b>
<a>737</a>
<b>277406</b>
<v>289</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>674</v>
</b>
<b>
<a>2</a>
<b>12</b>
<v>491</v>
</b>
<b>
<a>12</a>
<b>32</b>
<v>495</v>
</b>
<b>
<a>32</a>
<b>46</b>
<v>510</v>
</b>
<b>
<a>46</a>
<b>56</b>
<v>498</v>
</b>
<b>
<a>56</a>
<b>62</b>
<v>488</v>
</b>
<b>
<a>62</a>
<b>67</b>
<v>500</v>
</b>
<b>
<a>67</a>
<b>71</b>
<v>477</v>
</b>
<b>
<a>71</a>
<b>75</b>
<v>583</v>
</b>
<b>
<a>75</a>
<b>78</b>
<v>497</v>
</b>
<b>
<a>78</a>
<b>80</b>
<v>403</v>
</b>
<b>
<a>80</a>
<b>82</b>
<v>543</v>
</b>
<b>
<a>82</a>
<b>117856</b>
<v>298</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>674</v>
</b>
<b>
<a>2</a>
<b>13</b>
<v>509</v>
</b>
<b>
<a>13</a>
<b>23</b>
<v>509</v>
</b>
<b>
<a>23</a>
<b>35</b>
<v>520</v>
</b>
<b>
<a>35</a>
<b>50</b>
<v>504</v>
</b>
<b>
<a>50</a>
<b>69</b>
<v>506</v>
</b>
<b>
<a>69</a>
<b>92</b>
<v>489</v>
</b>
<b>
<a>92</a>
<b>124</b>
<v>504</v>
</b>
<b>
<a>124</a>
<b>165</b>
<v>487</v>
</b>
<b>
<a>165</a>
<b>230</b>
<v>490</v>
</b>
<b>
<a>230</a>
<b>357</b>
<v>491</v>
</b>
<b>
<a>357</a>
<b>737</b>
<v>485</v>
</b>
<b>
<a>737</a>
<b>277406</b>
<v>289</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>682</v>
</b>
<b>
<a>2</a>
<b>18</b>
<v>501</v>
</b>
<b>
<a>18</a>
<b>36</b>
<v>487</v>
</b>
<b>
<a>36</a>
<b>51</b>
<v>513</v>
</b>
<b>
<a>51</a>
<b>61</b>
<v>532</v>
</b>
<b>
<a>61</a>
<b>67</b>
<v>508</v>
</b>
<b>
<a>67</a>
<b>72</b>
<v>568</v>
</b>
<b>
<a>72</a>
<b>75</b>
<v>444</v>
</b>
<b>
<a>75</a>
<b>78</b>
<v>514</v>
</b>
<b>
<a>78</a>
<b>80</b>
<v>484</v>
</b>
<b>
<a>80</a>
<b>81</b>
<v>283</v>
</b>
<b>
<a>81</a>
<b>82</b>
<v>579</v>
</b>
<b>
<a>82</a>
<b>117837</b>
<v>362</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>666</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>116499</v>
</b>
<b>
<a>8</a>
<b>14</b>
<v>19181</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>29298</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>25329</v>
</b>
<b>
<a>19</a>
<b>24</b>
<v>17273</v>
</b>
<b>
<a>24</a>
<b>29</b>
<v>22410</v>
</b>
<b>
<a>29</a>
<b>56</b>
<v>21150</v>
</b>
<b>
<a>56</a>
<b>242</b>
<v>20830</v>
</b>
<b>
<a>242</a>
<b>134468</b>
<v>4769</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>117975</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>120803</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>21079</v>
</b>
<b>
<a>8</a>
<b>6458</b>
<v>17548</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>667</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>116499</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>19126</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>32612</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>18313</v>
</b>
<b>
<a>15</a>
<b>17</b>
<v>18964</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>21845</v>
</b>
<b>
<a>21</a>
<b>31</b>
<v>21197</v>
</b>
<b>
<a>31</a>
<b>64</b>
<v>20988</v>
</b>
<b>
<a>64</a>
<b>94454</b>
<v>7194</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>238980</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>22312</v>
</b>
<b>
<a>3</a>
<b>890</b>
<v>16113</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>667</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>116499</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>20939</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>28687</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>19707</v>
</b>
<b>
<a>16</a>
<b>18</b>
<v>20057</v>
</b>
<b>
<a>18</a>
<b>22</b>
<v>21035</v>
</b>
<b>
<a>22</a>
<b>33</b>
<v>21605</v>
</b>
<b>
<a>33</a>
<b>69</b>
<v>21089</v>
</b>
<b>
<a>69</a>
<b>94455</b>
<v>7120</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5117</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9246</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>13440</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>15857</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>13813</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>11696</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>8777</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>6887</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9723</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>10392</v>
</b>
<b>
<a>14</a>
<b>20</b>
<v>9364</v>
</b>
<b>
<a>20</a>
<b>2248970</b>
<v>3566</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>68610</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15842</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7965</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>9221</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>8014</v>
</b>
<b>
<a>6</a>
<b>6458</b>
<v>8226</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6868</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15317</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>24725</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>25386</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>10178</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>6239</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>10825</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9294</v>
</b>
<b>
<a>11</a>
<b>1255</b>
<v>8841</v>
</b>
<b>
<a>1258</a>
<b>277405</b>
<v>205</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6868</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15317</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>24725</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>25386</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>10175</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>6232</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>10827</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9299</v>
</b>
<b>
<a>11</a>
<b>1227</b>
<v>8842</v>
</b>
<b>
<a>1256</a>
<b>277405</b>
<v>207</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>24039</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>21662</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>22809</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17118</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>12038</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>7768</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>9297</v>
</b>
<b>
<a>10</a>
<b>1064</b>
<v>3147</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>666</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>116499</v>
</b>
<b>
<a>8</a>
<b>14</b>
<v>18715</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>30262</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>24946</v>
</b>
<b>
<a>19</a>
<b>24</b>
<v>17066</v>
</b>
<b>
<a>24</a>
<b>29</b>
<v>22451</v>
</b>
<b>
<a>29</a>
<b>56</b>
<v>21060</v>
</b>
<b>
<a>56</a>
<b>237</b>
<v>20821</v>
</b>
<b>
<a>237</a>
<b>134470</b>
<v>4919</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>117975</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>120803</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>21076</v>
</b>
<b>
<a>8</a>
<b>6458</b>
<v>17551</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>243883</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>23431</v>
</b>
<b>
<a>4</a>
<b>71</b>
<v>10091</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>667</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>116499</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>19057</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>32046</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>18779</v>
</b>
<b>
<a>15</a>
<b>17</b>
<v>18710</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>21785</v>
</b>
<b>
<a>21</a>
<b>31</b>
<v>21103</v>
</b>
<b>
<a>31</a>
<b>63</b>
<v>20930</v>
</b>
<b>
<a>63</a>
<b>94454</b>
<v>7829</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>667</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>116499</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>21177</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>28718</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>19585</v>
</b>
<b>
<a>16</a>
<b>18</b>
<v>21210</v>
</b>
<b>
<a>18</a>
<b>23</b>
<v>23344</v>
</b>
<b>
<a>23</a>
<b>35</b>
<v>21013</v>
</b>
<b>
<a>35</a>
<b>80</b>
<v>20938</v>
</b>
<b>
<a>80</a>
<b>94454</b>
<v>4254</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4439</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8489</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12884</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>16048</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>15554</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>12546</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>9231</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>6405</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9266</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>10367</v>
</b>
<b>
<a>14</a>
<b>20</b>
<v>9186</v>
</b>
<b>
<a>20</a>
<b>489713</b>
<v>3453</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>68569</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15919</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7876</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>9221</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>8062</v>
</b>
<b>
<a>6</a>
<b>6458</b>
<v>8221</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6848</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15273</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>24807</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>25343</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>10180</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>6269</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>10857</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9251</v>
</b>
<b>
<a>11</a>
<b>1768</b>
<v>8841</v>
</b>
<b>
<a>1780</a>
<b>212575</b>
<v>199</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15842</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>27460</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>26707</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>18639</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>11518</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>10766</v>
</b>
<b>
<a>8</a>
<b>265</b>
<v>6936</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6850</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15271</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>24807</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>25343</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>10180</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>6269</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>10858</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>9252</v>
</b>
<b>
<a>11</a>
<b>1789</b>
<v>8841</v>
</b>
<b>
<a>1795</a>
<b>212360</b>
<v>197</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>numlines</name>
<cardinality>122044</cardinality>
<columnsizes>
<e>
<k>element_id</k>
<v>122044</v>
</e>
<e>
<k>num_lines</k>
<v>1136</v>
</e>
<e>
<k>num_code</k>
<v>939</v>
</e>
<e>
<k>num_comment</k>
<v>418</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>element_id</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>122044</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>element_id</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>122044</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>element_id</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>122044</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>399</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>144</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>97</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>91</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>86</v>
</b>
<b>
<a>9</a>
<b>15</b>
<v>90</v>
</b>
<b>
<a>15</a>
<b>36</b>
<v>86</v>
</b>
<b>
<a>36</a>
<b>174</b>
<v>86</v>
</b>
<b>
<a>175</a>
<b>21589</b>
<v>57</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>444</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>140</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>95</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>87</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>85</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>88</v>
</b>
<b>
<a>14</a>
<b>24</b>
<v>90</v>
</b>
<b>
<a>24</a>
<b>33</b>
<v>89</v>
</b>
<b>
<a>33</a>
<b>38</b>
<v>18</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>444</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>140</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>94</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>92</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>90</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>90</v>
</b>
<b>
<a>14</a>
<b>20</b>
<v>89</v>
</b>
<b>
<a>20</a>
<b>27</b>
<v>89</v>
</b>
<b>
<a>27</a>
<b>30</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>317</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>125</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>67</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>61</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>67</v>
</b>
<b>
<a>8</a>
<b>12</b>
<v>73</v>
</b>
<b>
<a>12</a>
<b>26</b>
<v>72</v>
</b>
<b>
<a>26</a>
<b>69</b>
<v>71</v>
</b>
<b>
<a>69</a>
<b>1540</b>
<v>71</v>
</b>
<b>
<a>1747</a>
<b>22000</b>
<v>15</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>349</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>118</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>77</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>76</v>
</b>
<b>
<a>6</a>
<b>10</b>
<v>84</v>
</b>
<b>
<a>10</a>
<b>19</b>
<v>78</v>
</b>
<b>
<a>19</a>
<b>31</b>
<v>79</v>
</b>
<b>
<a>31</a>
<b>44</b>
<v>73</v>
</b>
<b>
<a>44</a>
<b>52</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>347</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>121</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>79</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>74</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>74</v>
</b>
<b>
<a>9</a>
<b>16</b>
<v>80</v>
</b>
<b>
<a>16</a>
<b>23</b>
<v>72</v>
</b>
<b>
<a>23</a>
<b>31</b>
<v>76</v>
</b>
<b>
<a>31</a>
<b>40</b>
<v>16</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>147</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>67</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>26</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>26</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>32</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>34</v>
</b>
<b>
<a>12</a>
<b>32</b>
<v>34</v>
</b>
<b>
<a>33</a>
<b>135</b>
<v>32</v>
</b>
<b>
<a>150</a>
<b>93795</b>
<v>20</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>171</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>57</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>32</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>24</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>33</v>
</b>
<b>
<a>8</a>
<b>18</b>
<v>35</v>
</b>
<b>
<a>19</a>
<b>47</b>
<v>32</v>
</b>
<b>
<a>52</a>
<b>253</b>
<v>33</v>
</b>
<b>
<a>362</a>
<b>363</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>174</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>54</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>33</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>22</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>33</v>
</b>
<b>
<a>8</a>
<b>18</b>
<v>36</v>
</b>
<b>
<a>19</a>
<b>47</b>
<v>32</v>
</b>
<b>
<a>51</a>
<b>230</b>
<v>32</v>
</b>
<b>
<a>232</a>
<b>346</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>files</name>
<key>id</key>
<cardinality>6457</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>6457</v>
</e>
<e>
<k>name</k>
<v>6457</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6457</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6457</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>folders</name>
<key>id</key>
<cardinality>1590</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1590</v>
</e>
<e>
<k>name</k>
<v>1590</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1590</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1590</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>containerparent</name>
<key>child</key>
<cardinality>8046</cardinality>
<columnsizes>
<e>
<k>parent</k>
<v>1590</v>
</e>
<e>
<k>child</k>
<v>8046</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>parent</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>525</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>326</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>207</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>128</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>138</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>132</v>
</b>
<b>
<a>11</a>
<b>53</b>
<v>120</v>
</b>
<b>
<a>60</a>
<b>335</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>child</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8046</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>externalData</name>
<cardinality>5684</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>950</v>
</e>
<e>
<k>path</k>
<v>3</v>
</e>
<e>
<k>column</k>
<v>6</v>
</e>
<e>
<k>value</k>
<v>790</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>950</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>946</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>6</b>
<v>8</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>942</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>72</a>
<b>73</b>
<v>1</v>
</b>
<b>
<a>874</a>
<b>875</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>86</a>
<b>87</b>
<v>1</v>
</b>
<b>
<a>722</a>
<b>723</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>946</a>
<b>947</b>
<v>4</v>
</b>
<b>
<a>950</a>
<b>951</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>31</a>
<b>32</b>
<v>1</v>
</b>
<b>
<a>93</a>
<b>94</b>
<v>1</v>
</b>
<b>
<a>117</a>
<b>118</b>
<v>1</v>
</b>
<b>
<a>620</a>
<b>621</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>478</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>132</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>69</v>
</b>
<b>
<a>5</a>
<b>16</b>
<v>61</v>
</b>
<b>
<a>16</a>
<b>928</b>
<v>50</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>764</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>26</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>711</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>79</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>sourceLocationPrefix</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>prefix</k>
<v>1</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>toplevels</name>
<key>id</key>
<cardinality>5320</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>5320</v>
</e>
<e>
<k>kind</k>
<v>4</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5320</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>31</a>
<b>32</b>
<v>1</v>
</b>
<b>
<a>86</a>
<b>87</b>
<v>1</v>
</b>
<b>
<a>5200</a>
<b>5201</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_externs</name>
<cardinality>44</cardinality>
<columnsizes>
<e>
<k>toplevel</k>
<v>44</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_instantiated</name>
<cardinality>5</cardinality>
<columnsizes>
<e>
<k>decl</k>
<v>5</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_declare_keyword</name>
<cardinality>66</cardinality>
<columnsizes>
<e>
<k>stmt</k>
<v>66</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_asserts_keyword</name>
<cardinality>66</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>66</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_abstract_member</name>
<cardinality>66</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>66</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_public_keyword</name>
<cardinality>9297</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>9297</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_private_keyword</name>
<cardinality>11391</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>11391</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_protected_keyword</name>
<cardinality>1048</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1048</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_readonly_keyword</name>
<cardinality>2338</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>2338</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_type_keyword</name>
<cardinality>1000</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1000</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_optional_member</name>
<cardinality>3668</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>3668</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_definite_assignment_assertion</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_optional_parameter_declaration</name>
<cardinality>3966</cardinality>
<columnsizes>
<e>
<k>parameter</k>
<v>3966</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>parameter_fields</name>
<cardinality>2693</cardinality>
<columnsizes>
<e>
<k>field</k>
<v>2693</v>
</e>
<e>
<k>constructor</k>
<v>1020</v>
</e>
<e>
<k>param_index</k>
<v>20</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>field</src>
<trg>constructor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2693</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>field</src>
<trg>param_index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2693</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>constructor</src>
<trg>field</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>233</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>118</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>78</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>83</v>
</b>
<b>
<a>7</a>
<b>21</b>
<v>69</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>constructor</src>
<trg>param_index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>233</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>118</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>78</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>83</v>
</b>
<b>
<a>7</a>
<b>21</b>
<v>69</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>param_index</src>
<trg>field</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>29</a>
<b>30</b>
<v>1</v>
</b>
<b>
<a>36</a>
<b>37</b>
<v>1</v>
</b>
<b>
<a>48</a>
<b>49</b>
<v>1</v>
</b>
<b>
<a>69</a>
<b>70</b>
<v>1</v>
</b>
<b>
<a>104</a>
<b>105</b>
<v>1</v>
</b>
<b>
<a>152</a>
<b>153</b>
<v>1</v>
</b>
<b>
<a>230</a>
<b>231</b>
<v>1</v>
</b>
<b>
<a>348</a>
<b>349</b>
<v>1</v>
</b>
<b>
<a>581</a>
<b>582</b>
<v>1</v>
</b>
<b>
<a>1020</a>
<b>1021</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>param_index</src>
<trg>constructor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>29</a>
<b>30</b>
<v>1</v>
</b>
<b>
<a>36</a>
<b>37</b>
<v>1</v>
</b>
<b>
<a>48</a>
<b>49</b>
<v>1</v>
</b>
<b>
<a>69</a>
<b>70</b>
<v>1</v>
</b>
<b>
<a>104</a>
<b>105</b>
<v>1</v>
</b>
<b>
<a>152</a>
<b>153</b>
<v>1</v>
</b>
<b>
<a>230</a>
<b>231</b>
<v>1</v>
</b>
<b>
<a>348</a>
<b>349</b>
<v>1</v>
</b>
<b>
<a>581</a>
<b>582</b>
<v>1</v>
</b>
<b>
<a>1020</a>
<b>1021</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_const_enum</name>
<cardinality>62</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>62</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_abstract_class</name>
<cardinality>116</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>116</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>typeexprs</name>
<cardinality>54050</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>54050</v>
</e>
<e>
<k>kind</k>
<v>6</v>
</e>
<e>
<k>parent</k>
<v>29264</v>
</e>
<e>
<k>idx</k>
<v>26</v>
</e>
<e>
<k>tostring</k>
<v>3278</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>54050</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>54050</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>54050</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>54050</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>733</a>
<b>734</b>
<v>1</v>
</b>
<b>
<a>2513</a>
<b>2514</b>
<v>1</v>
</b>
<b>
<a>25306</a>
<b>25307</b>
<v>1</v>
</b>
<b>
<a>25491</a>
<b>25492</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>733</a>
<b>734</b>
<v>1</v>
</b>
<b>
<a>2513</a>
<b>2514</b>
<v>1</v>
</b>
<b>
<a>16661</a>
<b>16662</b>
<v>1</v>
</b>
<b>
<a>17601</a>
<b>17602</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>242</a>
<b>243</b>
<v>1</v>
</b>
<b>
<a>2075</a>
<b>2076</b>
<v>1</v>
</b>
<b>
<a>2322</a>
<b>2323</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15321</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7887</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3725</v>
</b>
<b>
<a>4</a>
<b>9</b>
<v>2229</v>
</b>
<b>
<a>9</a>
<b>24</b>
<v>102</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21285</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7707</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>272</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15321</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7887</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3725</v>
</b>
<b>
<a>4</a>
<b>9</b>
<v>2229</v>
</b>
<b>
<a>9</a>
<b>24</b>
<v>102</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>16315</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8432</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3126</v>
</b>
<b>
<a>4</a>
<b>22</b>
<v>1391</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>13</a>
<b>22</b>
<v>2</v>
</b>
<b>
<a>27</a>
<b>38</b>
<v>2</v>
</b>
<b>
<a>54</a>
<b>61</b>
<v>2</v>
</b>
<b>
<a>101</a>
<b>212</b>
<v>2</v>
</b>
<b>
<a>356</a>
<b>530</b>
<v>2</v>
</b>
<b>
<a>859</a>
<b>1645</b>
<v>2</v>
</b>
<b>
<a>2513</a>
<b>2519</b>
<v>2</v>
</b>
<b>
<a>3330</a>
<b>7198</b>
<v>2</v>
</b>
<b>
<a>15305</a>
<b>19237</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>13</a>
<b>22</b>
<v>2</v>
</b>
<b>
<a>27</a>
<b>38</b>
<v>2</v>
</b>
<b>
<a>54</a>
<b>61</b>
<v>2</v>
</b>
<b>
<a>101</a>
<b>212</b>
<v>2</v>
</b>
<b>
<a>356</a>
<b>530</b>
<v>2</v>
</b>
<b>
<a>859</a>
<b>1645</b>
<v>2</v>
</b>
<b>
<a>2513</a>
<b>2519</b>
<v>2</v>
</b>
<b>
<a>3330</a>
<b>7198</b>
<v>2</v>
</b>
<b>
<a>15305</a>
<b>19237</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>17</b>
<v>2</v>
</b>
<b>
<a>18</a>
<b>26</b>
<v>2</v>
</b>
<b>
<a>28</a>
<b>31</b>
<v>2</v>
</b>
<b>
<a>37</a>
<b>44</b>
<v>2</v>
</b>
<b>
<a>60</a>
<b>71</b>
<v>2</v>
</b>
<b>
<a>108</a>
<b>196</b>
<v>2</v>
</b>
<b>
<a>395</a>
<b>667</b>
<v>2</v>
</b>
<b>
<a>746</a>
<b>978</b>
<v>2</v>
</b>
<b>
<a>1522</a>
<b>2076</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1085</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>627</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>344</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>322</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>292</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>260</v>
</b>
<b>
<a>12</a>
<b>45</b>
<v>247</v>
</b>
<b>
<a>45</a>
<b>7788</b>
<v>101</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1903</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1375</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1097</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>631</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>341</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>327</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>292</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>253</v>
</b>
<b>
<a>12</a>
<b>48</b>
<v>246</v>
</b>
<b>
<a>48</a>
<b>6190</b>
<v>91</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1450</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>939</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>481</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>289</v>
</b>
<b>
<a>6</a>
<b>19</b>
<v>119</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_for_await_of</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>forof</k>
<v>1</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_module</name>
<cardinality>21</cardinality>
<columnsizes>
<e>
<k>tl</k>
<v>21</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_es2015_module</name>
<cardinality>21</cardinality>
<columnsizes>
<e>
<k>tl</k>
<v>21</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_closure_module</name>
<cardinality>21</cardinality>
<columnsizes>
<e>
<k>tl</k>
<v>21</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>toplevel_parent_xml_node</name>
<cardinality>43</cardinality>
<columnsizes>
<e>
<k>toplevel</k>
<v>43</v>
</e>
<e>
<k>xmlnode</k>
<v>43</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>toplevel</src>
<trg>xmlnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>43</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>xmlnode</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>43</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xml_element_parent_expression</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>xmlnode</k>
<v>1</v>
</e>
<e>
<k>expression</k>
<v>1</v>
</e>
<e>
<k>index</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>xmlnode</src>
<trg>expression</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>xmlnode</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expression</src>
<trg>xmlnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expression</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>xmlnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>expression</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_nodejs</name>
<cardinality>12</cardinality>
<columnsizes>
<e>
<k>tl</k>
<v>12</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>stmts</name>
<key>id</key>
<cardinality>1096691</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1096691</v>
</e>
<e>
<k>kind</k>
<v>31</v>
</e>
<e>
<k>parent</k>
<v>412140</v>
</e>
<e>
<k>idx</k>
<v>152947</v>
</e>
<e>
<k>tostring</k>
<v>284956</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1096691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1096691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1096691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1096691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>31</a>
<b>42</b>
<v>2</v>
</b>
<b>
<a>61</a>
<b>552</b>
<v>2</v>
</b>
<b>
<a>1118</a>
<b>1137</b>
<v>2</v>
</b>
<b>
<a>1272</a>
<b>1316</b>
<v>2</v>
</b>
<b>
<a>1316</a>
<b>1379</b>
<v>2</v>
</b>
<b>
<a>1471</a>
<b>1570</b>
<v>2</v>
</b>
<b>
<a>1642</a>
<b>2306</b>
<v>2</v>
</b>
<b>
<a>3120</a>
<b>5386</b>
<v>2</v>
</b>
<b>
<a>8674</a>
<b>10150</b>
<v>2</v>
</b>
<b>
<a>16771</a>
<b>48210</b>
<v>2</v>
</b>
<b>
<a>68214</a>
<b>105607</b>
<v>2</v>
</b>
<b>
<a>204994</a>
<b>610341</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>35</a>
<b>59</b>
<v>2</v>
</b>
<b>
<a>298</a>
<b>424</b>
<v>2</v>
</b>
<b>
<a>738</a>
<b>1157</b>
<v>2</v>
</b>
<b>
<a>1253</a>
<b>1263</b>
<v>2</v>
</b>
<b>
<a>1271</a>
<b>1321</b>
<v>2</v>
</b>
<b>
<a>1495</a>
<b>1568</b>
<v>2</v>
</b>
<b>
<a>1642</a>
<b>2306</b>
<v>2</v>
</b>
<b>
<a>2999</a>
<b>4416</b>
<v>2</v>
</b>
<b>
<a>4734</a>
<b>10123</b>
<v>2</v>
</b>
<b>
<a>48139</a>
<b>48347</b>
<v>2</v>
</b>
<b>
<a>50857</a>
<b>162082</b>
<v>2</v>
</b>
<b>
<a>191077</a>
<b>191078</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>16</a>
<b>22</b>
<v>2</v>
</b>
<b>
<a>28</a>
<b>32</b>
<v>2</v>
</b>
<b>
<a>36</a>
<b>37</b>
<v>2</v>
</b>
<b>
<a>39</a>
<b>51</b>
<v>2</v>
</b>
<b>
<a>54</a>
<b>63</b>
<v>2</v>
</b>
<b>
<a>65</a>
<b>67</b>
<v>2</v>
</b>
<b>
<a>116</a>
<b>118</b>
<v>2</v>
</b>
<b>
<a>122</a>
<b>138</b>
<v>2</v>
</b>
<b>
<a>251</a>
<b>1564</b>
<v>2</v>
</b>
<b>
<a>1967</a>
<b>152946</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>17</b>
<v>2</v>
</b>
<b>
<a>88</a>
<b>104</b>
<v>2</v>
</b>
<b>
<a>147</a>
<b>168</b>
<v>2</v>
</b>
<b>
<a>239</a>
<b>296</b>
<v>2</v>
</b>
<b>
<a>356</a>
<b>428</b>
<v>2</v>
</b>
<b>
<a>591</a>
<b>705</b>
<v>2</v>
</b>
<b>
<a>811</a>
<b>829</b>
<v>2</v>
</b>
<b>
<a>1092</a>
<b>2254</b>
<v>2</v>
</b>
<b>
<a>2665</a>
<b>10292</b>
<v>2</v>
</b>
<b>
<a>18023</a>
<b>21916</b>
<v>2</v>
</b>
<b>
<a>43911</a>
<b>180066</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>265890</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>69435</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>25109</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>34966</v>
</b>
<b>
<a>8</a>
<b>152946</b>
<v>16740</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>319546</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>67918</v>
</b>
<b>
<a>3</a>
<b>23</b>
<v>24676</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>265890</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>69435</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>25109</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>34966</v>
</b>
<b>
<a>8</a>
<b>152946</b>
<v>16740</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>275359</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>62818</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>25781</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>34293</v>
</b>
<b>
<a>8</a>
<b>19511</b>
<v>13889</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>149939</v>
</b>
<b>
<a>2</a>
<b>220361</b>
<v>3008</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>149940</v>
</b>
<b>
<a>2</a>
<b>28</b>
<v>3007</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>149939</v>
</b>
<b>
<a>2</a>
<b>220361</b>
<v>3008</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>149939</v>
</b>
<b>
<a>2</a>
<b>88922</b>
<v>3008</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>186537</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>48494</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>24651</v>
</b>
<b>
<a>5</a>
<b>37</b>
<v>21526</v>
</b>
<b>
<a>37</a>
<b>72175</b>
<v>3748</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>284895</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>61</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>195596</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>45562</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>23127</v>
</b>
<b>
<a>5</a>
<b>66340</b>
<v>20671</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>225945</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>33948</v>
</b>
<b>
<a>3</a>
<b>13</b>
<v>21496</v>
</b>
<b>
<a>13</a>
<b>903</b>
<v>3567</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>stmt_containers</name>
<cardinality>1096691</cardinality>
<columnsizes>
<e>
<k>stmt</k>
<v>1096691</v>
</e>
<e>
<k>container</k>
<v>120740</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>stmt</src>
<trg>container</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1096691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>container</src>
<trg>stmt</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6778</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>35010</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>16178</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>12184</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>9476</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>7569</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>10084</v>
</b>
<b>
<a>9</a>
<b>13</b>
<v>10057</v>
</b>
<b>
<a>13</a>
<b>27</b>
<v>9196</v>
</b>
<b>
<a>27</a>
<b>152947</b>
<v>4208</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jump_targets</name>
<cardinality>11791</cardinality>
<columnsizes>
<e>
<k>jump</k>
<v>11791</v>
</e>
<e>
<k>target</k>
<v>4873</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>jump</src>
<trg>target</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11791</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>target</src>
<trg>jump</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2542</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1106</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>505</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>410</v>
</b>
<b>
<a>6</a>
<b>260</b>
<v>310</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>exprs</name>
<key>id</key>
<cardinality>5495305</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>5495305</v>
</e>
<e>
<k>kind</k>
<v>85</v>
</e>
<e>
<k>parent</k>
<v>3130204</v>
</e>
<e>
<k>idx</k>
<v>17698</v>
</e>
<e>
<k>tostring</k>
<v>834491</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5495305</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5495305</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5495305</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5495305</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>7</v>
</b>
<b>
<a>4</a>
<b>45</b>
<v>7</v>
</b>
<b>
<a>50</a>
<b>97</b>
<v>7</v>
</b>
<b>
<a>108</a>
<b>458</b>
<v>7</v>
</b>
<b>
<a>503</a>
<b>824</b>
<v>7</v>
</b>
<b>
<a>1135</a>
<b>2497</b>
<v>7</v>
</b>
<b>
<a>2527</a>
<b>5439</b>
<v>7</v>
</b>
<b>
<a>5655</a>
<b>10255</b>
<v>7</v>
</b>
<b>
<a>10789</a>
<b>15893</b>
<v>7</v>
</b>
<b>
<a>17758</a>
<b>42854</b>
<v>7</v>
</b>
<b>
<a>50958</a>
<b>130844</b>
<v>7</v>
</b>
<b>
<a>245084</a>
<b>722374</b>
<v>7</v>
</b>
<b>
<a>1295408</a>
<b>1295409</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>7</v>
</b>
<b>
<a>3</a>
<b>45</b>
<v>7</v>
</b>
<b>
<a>47</a>
<b>93</b>
<v>7</v>
</b>
<b>
<a>106</a>
<b>407</b>
<v>7</v>
</b>
<b>
<a>457</a>
<b>809</b>
<v>7</v>
</b>
<b>
<a>1108</a>
<b>2420</b>
<v>7</v>
</b>
<b>
<a>2502</a>
<b>5349</b>
<v>7</v>
</b>
<b>
<a>5453</a>
<b>10133</b>
<v>7</v>
</b>
<b>
<a>10658</a>
<b>15697</b>
<v>7</v>
</b>
<b>
<a>16273</a>
<b>36888</b>
<v>7</v>
</b>
<b>
<a>41849</a>
<b>128642</b>
<v>7</v>
</b>
<b>
<a>199566</a>
<b>722374</b>
<v>7</v>
</b>
<b>
<a>1171898</a>
<b>1171899</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>11</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>7</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>7</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>6</v>
</b>
<b>
<a>12</a>
<b>18</b>
<v>7</v>
</b>
<b>
<a>20</a>
<b>64</b>
<v>7</v>
</b>
<b>
<a>82</a>
<b>395</b>
<v>7</v>
</b>
<b>
<a>431</a>
<b>13375</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>6</b>
<v>7</v>
</b>
<b>
<a>8</a>
<b>37</b>
<v>7</v>
</b>
<b>
<a>38</a>
<b>126</b>
<v>7</v>
</b>
<b>
<a>142</a>
<b>304</b>
<v>7</v>
</b>
<b>
<a>358</a>
<b>721</b>
<v>7</v>
</b>
<b>
<a>811</a>
<b>1485</b>
<v>7</v>
</b>
<b>
<a>1523</a>
<b>2918</b>
<v>7</v>
</b>
<b>
<a>3305</a>
<b>5078</b>
<v>7</v>
</b>
<b>
<a>5422</a>
<b>9940</b>
<v>7</v>
</b>
<b>
<a>10536</a>
<b>40606</b>
<v>7</v>
</b>
<b>
<a>46227</a>
<b>123090</b>
<v>7</v>
</b>
<b>
<a>128754</a>
<b>128755</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1100280</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1876078</v>
</b>
<b>
<a>3</a>
<b>17692</b>
<v>153846</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1300246</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1747609</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>82349</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1100280</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1876078</v>
</b>
<b>
<a>3</a>
<b>17692</b>
<v>153846</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1108803</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1870864</v>
</b>
<b>
<a>3</a>
<b>17526</b>
<v>150537</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4092</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1365</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1995</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>283</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1681</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>5909</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>1344</v>
</b>
<b>
<a>10</a>
<b>3049605</b>
<v>1029</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10648</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6398</v>
</b>
<b>
<a>3</a>
<b>83</b>
<v>652</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4092</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1365</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1995</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>283</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1681</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>5909</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>1344</v>
</b>
<b>
<a>10</a>
<b>3049605</b>
<v>1029</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4093</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1365</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2014</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1147</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1529</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>5401</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>1499</v>
</b>
<b>
<a>10</a>
<b>573348</b>
<v>650</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>466570</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>157949</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>55443</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>61411</v>
</b>
<b>
<a>6</a>
<b>17</b>
<v>63412</v>
</b>
<b>
<a>17</a>
<b>128652</b>
<v>29706</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>772624</v>
</b>
<b>
<a>2</a>
<b>24</b>
<v>61867</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>467110</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>158201</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>55446</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>61061</v>
</b>
<b>
<a>6</a>
<b>17</b>
<v>63168</v>
</b>
<b>
<a>17</a>
<b>128642</b>
<v>29505</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>724438</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>86524</v>
</b>
<b>
<a>3</a>
<b>7765</b>
<v>23529</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>literals</name>
<key>expr</key>
<cardinality>3145090</cardinality>
<columnsizes>
<e>
<k>value</k>
<v>216517</v>
</e>
<e>
<k>raw</k>
<v>234110</v>
</e>
<e>
<k>expr</k>
<v>3145090</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>value</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>201221</v>
</b>
<b>
<a>2</a>
<b>25</b>
<v>15296</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>95821</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>41222</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19627</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>16097</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>18825</v>
</b>
<b>
<a>9</a>
<b>31</b>
<v>16474</v>
</b>
<b>
<a>31</a>
<b>122435</b>
<v>8451</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>234110</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104635</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>47230</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>20082</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>16835</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>19610</v>
</b>
<b>
<a>9</a>
<b>34</b>
<v>17695</v>
</b>
<b>
<a>34</a>
<b>120241</b>
<v>8023</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expr</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3145090</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expr</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3145090</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>enclosing_stmt</name>
<cardinality>5372899</cardinality>
<columnsizes>
<e>
<k>expr</k>
<v>5372899</v>
</e>
<e>
<k>stmt</k>
<v>854574</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>expr</src>
<trg>stmt</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5372899</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>stmt</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>74578</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>254844</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>57228</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>136234</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>44557</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>79401</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>55420</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>63155</v>
</b>
<b>
<a>11</a>
<b>17</b>
<v>65146</v>
</b>
<b>
<a>17</a>
<b>88321</b>
<v>24011</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>expr_containers</name>
<cardinality>5495305</cardinality>
<columnsizes>
<e>
<k>expr</k>
<v>5495305</v>
</e>
<e>
<k>container</k>
<v>118511</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>expr</src>
<trg>container</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5495305</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>container</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>7197</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>9110</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>9222</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>8424</v>
</b>
<b>
<a>10</a>
<b>13</b>
<v>10651</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>8706</v>
</b>
<b>
<a>16</a>
<b>20</b>
<v>9358</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>9955</v>
</b>
<b>
<a>25</a>
<b>31</b>
<v>8893</v>
</b>
<b>
<a>31</a>
<b>40</b>
<v>9356</v>
</b>
<b>
<a>40</a>
<b>54</b>
<v>9017</v>
</b>
<b>
<a>54</a>
<b>85</b>
<v>8935</v>
</b>
<b>
<a>85</a>
<b>484</b>
<v>8890</v>
</b>
<b>
<a>484</a>
<b>459128</b>
<v>797</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>array_size</name>
<cardinality>28188</cardinality>
<columnsizes>
<e>
<k>ae</k>
<v>28188</v>
</e>
<e>
<k>sz</k>
<v>118</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>ae</src>
<trg>sz</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>28188</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sz</src>
<trg>ae</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>52</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>21</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>9</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>9</v>
</b>
<b>
<a>9</a>
<b>20</b>
<v>9</v>
</b>
<b>
<a>22</a>
<b>181</b>
<v>9</v>
</b>
<b>
<a>231</a>
<b>12345</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_delegating</name>
<cardinality>4</cardinality>
<columnsizes>
<e>
<k>yield</k>
<v>4</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>expr_contains_template_tag_location</name>
<cardinality>31</cardinality>
<columnsizes>
<e>
<k>expr</k>
<v>31</v>
</e>
<e>
<k>location</k>
<v>31</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>expr</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>31</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>31</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>template_placeholder_tag_info</name>
<cardinality>283</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>283</v>
</e>
<e>
<k>parentNode</k>
<v>92</v>
</e>
<e>
<k>raw</k>
<v>24</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>parentNode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>283</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>node</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>283</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentNode</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>49</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>9</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>4</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>13</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentNode</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>49</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>11</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>13</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>26</b>
<v>2</v>
</b>
<b>
<a>34</a>
<b>45</b>
<v>2</v>
</b>
<b>
<a>82</a>
<b>83</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>parentNode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>26</b>
<v>2</v>
</b>
<b>
<a>34</a>
<b>41</b>
<v>2</v>
</b>
<b>
<a>44</a>
<b>45</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>scopes</name>
<key>id</key>
<cardinality>118172</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>118172</v>
</e>
<e>
<k>kind</k>
<v>8</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>118172</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
<b>
<a>28</a>
<b>29</b>
<v>1</v>
</b>
<b>
<a>584</a>
<b>585</b>
<v>1</v>
</b>
<b>
<a>1272</a>
<b>1273</b>
<v>1</v>
</b>
<b>
<a>116245</a>
<b>116246</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>scopenodes</name>
<cardinality>118171</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>118171</v>
</e>
<e>
<k>scope</k>
<v>118171</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>118171</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>118171</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>scopenesting</name>
<cardinality>118171</cardinality>
<columnsizes>
<e>
<k>inner</k>
<v>118171</v>
</e>
<e>
<k>outer</k>
<v>33143</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>inner</src>
<trg>outer</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>118171</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>outer</src>
<trg>inner</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>17868</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6196</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2666</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2791</v>
</b>
<b>
<a>6</a>
<b>13</b>
<v>2584</v>
</b>
<b>
<a>13</a>
<b>17277</b>
<v>1038</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_generator</name>
<cardinality>62</cardinality>
<columnsizes>
<e>
<k>fun</k>
<v>62</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>has_rest_parameter</name>
<cardinality>33</cardinality>
<columnsizes>
<e>
<k>fun</k>
<v>33</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_async</name>
<cardinality>50</cardinality>
<columnsizes>
<e>
<k>fun</k>
<v>50</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>variables</name>
<key>id</key>
<cardinality>364388</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>364388</v>
</e>
<e>
<k>name</k>
<v>56559</v>
</e>
<e>
<k>scope</k>
<v>118168</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>364388</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>364388</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38013</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9547</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>4518</v>
</b>
<b>
<a>5</a>
<b>115</b>
<v>4242</v>
</b>
<b>
<a>115</a>
<b>116259</b>
<v>239</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38013</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9547</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>4518</v>
</b>
<b>
<a>5</a>
<b>115</b>
<v>4242</v>
</b>
<b>
<a>115</a>
<b>116259</b>
<v>239</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>39907</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>32053</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>18882</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>9814</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>10909</v>
</b>
<b>
<a>8</a>
<b>8779</b>
<v>6603</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>39907</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>32053</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>18882</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>9814</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>10909</v>
</b>
<b>
<a>8</a>
<b>8779</b>
<v>6603</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>local_type_names</name>
<cardinality>23565</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>23565</v>
</e>
<e>
<k>name</k>
<v>6080</v>
</e>
<e>
<k>scope</k>
<v>1614</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23565</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23565</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2821</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1362</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>641</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>508</v>
</b>
<b>
<a>6</a>
<b>13</b>
<v>485</v>
</b>
<b>
<a>13</a>
<b>533</b>
<v>263</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2821</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1362</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>641</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>508</v>
</b>
<b>
<a>6</a>
<b>13</b>
<v>485</v>
</b>
<b>
<a>13</a>
<b>533</b>
<v>263</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>138</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>109</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>116</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>108</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>140</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>89</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>131</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>112</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>144</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>134</v>
</b>
<b>
<a>19</a>
<b>25</b>
<v>132</v>
</b>
<b>
<a>25</a>
<b>37</b>
<v>122</v>
</b>
<b>
<a>37</a>
<b>87</b>
<v>122</v>
</b>
<b>
<a>87</a>
<b>221</b>
<v>17</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>138</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>109</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>116</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>108</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>140</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>89</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>131</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>112</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>144</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>134</v>
</b>
<b>
<a>19</a>
<b>25</b>
<v>132</v>
</b>
<b>
<a>25</a>
<b>37</b>
<v>122</v>
</b>
<b>
<a>37</a>
<b>87</b>
<v>122</v>
</b>
<b>
<a>87</a>
<b>221</b>
<v>17</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>local_namespace_names</name>
<cardinality>20832</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>20832</v>
</e>
<e>
<k>name</k>
<v>4078</v>
</e>
<e>
<k>scope</k>
<v>1543</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20832</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20832</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1787</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>859</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>378</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>216</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>364</v>
</b>
<b>
<a>8</a>
<b>20</b>
<v>310</v>
</b>
<b>
<a>20</a>
<b>533</b>
<v>164</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1787</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>859</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>378</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>216</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>364</v>
</b>
<b>
<a>8</a>
<b>20</b>
<v>310</v>
</b>
<b>
<a>20</a>
<b>533</b>
<v>164</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>88</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>123</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>120</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>104</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>107</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>70</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>87</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>137</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>122</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>122</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>124</v>
</b>
<b>
<a>19</a>
<b>26</b>
<v>120</v>
</b>
<b>
<a>26</a>
<b>39</b>
<v>117</v>
</b>
<b>
<a>39</a>
<b>136</b>
<v>102</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>88</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>123</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>120</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>104</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>107</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>70</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>87</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>137</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>122</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>122</v>
</b>
<b>
<a>15</a>
<b>19</b>
<v>124</v>
</b>
<b>
<a>19</a>
<b>26</b>
<v>120</v>
</b>
<b>
<a>26</a>
<b>39</b>
<v>117</v>
</b>
<b>
<a>39</a>
<b>136</b>
<v>102</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_arguments_object</name>
<cardinality>116243</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>116243</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>bind</name>
<cardinality>1295408</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1295408</v>
</e>
<e>
<k>decl</k>
<v>224900</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1295408</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>81789</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>50824</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>29919</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17755</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>16901</v>
</b>
<b>
<a>7</a>
<b>14</b>
<v>17790</v>
</b>
<b>
<a>14</a>
<b>98305</b>
<v>9922</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>decl</name>
<cardinality>250257</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>250257</v>
</e>
<e>
<k>decl</k>
<v>246998</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>250257</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>245772</v>
</b>
<b>
<a>2</a>
<b>283</b>
<v>1226</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>typebind</name>
<cardinality>36216</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>36216</v>
</e>
<e>
<k>decl</k>
<v>12650</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>36216</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6781</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2435</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1133</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>1127</v>
</b>
<b>
<a>6</a>
<b>17</b>
<v>954</v>
</b>
<b>
<a>17</a>
<b>524</b>
<v>220</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>typedecl</name>
<cardinality>23573</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>23573</v>
</e>
<e>
<k>decl</k>
<v>23565</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23573</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23558</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>namespacedecl</name>
<cardinality>20839</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>20839</v>
</e>
<e>
<k>decl</k>
<v>20832</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20839</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20828</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>namespacebind</name>
<cardinality>4300</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>4300</v>
</e>
<e>
<k>decl</k>
<v>485</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>decl</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4300</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>decl</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>133</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>46</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>56</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>30</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>37</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>44</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>41</v>
</b>
<b>
<a>12</a>
<b>17</b>
<v>38</v>
</b>
<b>
<a>17</a>
<b>31</b>
<v>37</v>
</b>
<b>
<a>32</a>
<b>287</b>
<v>23</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>properties</name>
<key>id</key>
<cardinality>142723</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>142723</v>
</e>
<e>
<k>parent</k>
<v>45129</v>
</e>
<e>
<k>index</k>
<v>4204</v>
</e>
<e>
<k>kind</k>
<v>3</v>
</e>
<e>
<k>tostring</k>
<v>67703</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>142723</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>142723</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>142723</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>142723</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15702</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17715</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4729</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>3778</v>
</b>
<b>
<a>6</a>
<b>4205</b>
<v>3205</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15702</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17715</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4729</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>3778</v>
</b>
<b>
<a>6</a>
<b>4205</b>
<v>3205</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>44603</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>526</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>15770</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17763</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4692</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>3759</v>
</b>
<b>
<a>6</a>
<b>4173</b>
<v>3145</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2827</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>364</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>358</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>337</v>
</b>
<b>
<a>8</a>
<b>11713</b>
<v>316</v>
</b>
<b>
<a>29427</a>
<b>45130</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2827</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>364</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>358</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>337</v>
</b>
<b>
<a>8</a>
<b>11713</b>
<v>316</v>
</b>
<b>
<a>29427</a>
<b>45130</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4149</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>55</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2827</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>364</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>358</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>337</v>
</b>
<b>
<a>7</a>
<b>6233</b>
<v>316</v>
</b>
<b>
<a>16744</a>
<b>16747</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>338</a>
<b>339</b>
<v>1</v>
</b>
<b>
<a>1529</a>
<b>1530</b>
<v>1</v>
</b>
<b>
<a>140856</a>
<b>140857</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>204</a>
<b>205</b>
<v>1</v>
</b>
<b>
<a>523</a>
<b>524</b>
<v>1</v>
</b>
<b>
<a>45034</a>
<b>45035</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>36</a>
<b>37</b>
<v>1</v>
</b>
<b>
<a>55</a>
<b>56</b>
<v>1</v>
</b>
<b>
<a>4204</a>
<b>4205</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>174</a>
<b>175</b>
<v>1</v>
</b>
<b>
<a>880</a>
<b>881</b>
<v>1</v>
</b>
<b>
<a>66649</a>
<b>66650</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46301</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>13295</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>5112</v>
</b>
<b>
<a>6</a>
<b>2975</b>
<v>2995</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46926</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>13013</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>5466</v>
</b>
<b>
<a>7</a>
<b>2975</b>
<v>2298</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>61480</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>5275</v>
</b>
<b>
<a>4</a>
<b>43</b>
<v>948</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>67703</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_computed</name>
<cardinality>27</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>27</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_method</name>
<cardinality>392</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>392</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>is_static</name>
<cardinality>36</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>36</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>type_alias</name>
<cardinality>1386</cardinality>
<columnsizes>
<e>
<k>aliasType</k>
<v>1386</v>
</e>
<e>
<k>underlyingType</k>
<v>1361</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>underlyingType</src>
<trg>aliasType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>aliasType</src>
<trg>underlyingType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>type_literal_value</name>
<cardinality>31882</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>31882</v>
</e>
<e>
<k>value</k>
<v>31828</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>31882</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>31774</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>54</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>signature_types</name>
<cardinality>46921</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>46921</v>
</e>
<e>
<k>kind</k>
<v>2</v>
</e>
<e>
<k>tostring</k>
<v>27460</v>
</e>
<e>
<k>type_parameters</k>
<v>11</v>
</e>
<e>
<k>required_params</k>
<v>22</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46921</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46921</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>type_parameters</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46921</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>required_params</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>46921</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2639</a>
<b>2640</b>
<v>1</v>
</b>
<b>
<a>44282</a>
<b>44283</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2200</a>
<b>2201</b>
<v>1</v>
</b>
<b>
<a>25260</a>
<b>25261</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>type_parameters</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>required_params</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22069</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3061</v>
</b>
<b>
<a>3</a>
<b>13</b>
<v>2112</v>
</b>
<b>
<a>13</a>
<b>277</b>
<v>218</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>27460</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>type_parameters</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>27459</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>required_params</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>27134</v>
</b>
<b>
<a>2</a>
<b>10</b>
<v>326</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>type_parameters</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
<b>
<a>34</a>
<b>35</b>
<v>1</v>
</b>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
<b>
<a>51</a>
<b>52</b>
<v>1</v>
</b>
<b>
<a>74</a>
<b>75</b>
<v>1</v>
</b>
<b>
<a>139</a>
<b>140</b>
<v>1</v>
</b>
<b>
<a>274</a>
<b>275</b>
<v>1</v>
</b>
<b>
<a>5367</a>
<b>5368</b>
<v>1</v>
</b>
<b>
<a>40901</a>
<b>40902</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>type_parameters</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>type_parameters</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>158</a>
<b>159</b>
<v>1</v>
</b>
<b>
<a>1805</a>
<b>1806</b>
<v>1</v>
</b>
<b>
<a>25429</a>
<b>25430</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>type_parameters</src>
<trg>required_params</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>required_params</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>44</a>
<b>131</b>
<v>2</v>
</b>
<b>
<a>197</a>
<b>373</b>
<v>2</v>
</b>
<b>
<a>645</a>
<b>2439</b>
<v>2</v>
</b>
<b>
<a>2783</a>
<b>6853</b>
<v>2</v>
</b>
<b>
<a>16407</a>
<b>17002</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>required_params</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>required_params</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>39</a>
<b>62</b>
<v>2</v>
</b>
<b>
<a>112</a>
<b>205</b>
<v>2</v>
</b>
<b>
<a>432</a>
<b>1404</b>
<v>2</v>
</b>
<b>
<a>1813</a>
<b>3662</b>
<v>2</v>
</b>
<b>
<a>8431</a>
<b>11659</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>required_params</src>
<trg>type_parameters</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>12</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_abstract_signature</name>
<cardinality>12</cardinality>
<columnsizes>
<e>
<k>sig</k>
<v>12</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>signature_rest_parameter</name>
<cardinality>19521</cardinality>
<columnsizes>
<e>
<k>sig</k>
<v>19521</v>
</e>
<e>
<k>rest_param_arra_type</k>
<v>14259</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>rest_param_arra_type</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>rest_param_arra_type</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>type_contains_signature</name>
<cardinality>87640</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>68964</v>
</e>
<e>
<k>kind</k>
<v>2</v>
</e>
<e>
<k>index</k>
<v>247</v>
</e>
<e>
<k>sig</k>
<v>37344</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>68938</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>26</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typ</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>59150</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5394</v>
</b>
<b>
<a>3</a>
<b>248</b>
<v>4420</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typ</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>60034</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4557</v>
</b>
<b>
<a>3</a>
<b>248</b>
<v>4373</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2582</a>
<b>2583</b>
<v>1</v>
</b>
<b>
<a>66408</a>
<b>66409</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>247</a>
<b>248</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2646</a>
<b>2647</b>
<v>1</v>
</b>
<b>
<a>34698</a>
<b>34699</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>198</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>21</v>
</b>
<b>
<a>3</a>
<b>265</b>
<v>19</v>
</b>
<b>
<a>449</a>
<b>42171</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>241</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>198</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>24</v>
</b>
<b>
<a>3</a>
<b>90</b>
<v>19</v>
</b>
<b>
<a>309</a>
<b>31688</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>35114</v>
</b>
<b>
<a>2</a>
<b>896</b>
<v>2230</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37344</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>36489</v>
</b>
<b>
<a>2</a>
<b>9</b>
<v>855</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>signature_contains_type</name>
<cardinality>107012</cardinality>
<columnsizes>
<e>
<k>child</k>
<v>26824</v>
</e>
<e>
<k>parent</k>
<v>37344</v>
</e>
<e>
<k>index</k>
<v>21</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>child</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19848</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3736</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>2017</v>
</b>
<b>
<a>7</a>
<b>10275</b>
<v>1223</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>child</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22572</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3289</v>
</b>
<b>
<a>3</a>
<b>22</b>
<v>963</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3594</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>18463</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>10057</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3906</v>
</b>
<b>
<a>5</a>
<b>11</b>
<v>1324</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2649</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14810</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12007</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4294</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>3055</v>
</b>
<b>
<a>8</a>
<b>22</b>
<v>529</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>106</a>
<b>107</b>
<v>1</v>
</b>
<b>
<a>313</a>
<b>314</b>
<v>1</v>
</b>
<b>
<a>455</a>
<b>456</b>
<v>1</v>
</b>
<b>
<a>643</a>
<b>644</b>
<v>1</v>
</b>
<b>
<a>1088</a>
<b>1089</b>
<v>1</v>
</b>
<b>
<a>2051</a>
<b>2052</b>
<v>1</v>
</b>
<b>
<a>6862</a>
<b>6863</b>
<v>1</v>
</b>
<b>
<a>8789</a>
<b>8790</b>
<v>1</v>
</b>
<b>
<a>12289</a>
<b>12290</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>26</a>
<b>27</b>
<v>1</v>
</b>
<b>
<a>37</a>
<b>38</b>
<v>1</v>
</b>
<b>
<a>45</a>
<b>46</b>
<v>1</v>
</b>
<b>
<a>91</a>
<b>92</b>
<v>1</v>
</b>
<b>
<a>219</a>
<b>220</b>
<v>1</v>
</b>
<b>
<a>529</a>
<b>530</b>
<v>1</v>
</b>
<b>
<a>1042</a>
<b>1043</b>
<v>1</v>
</b>
<b>
<a>1574</a>
<b>1575</b>
<v>1</v>
</b>
<b>
<a>3584</a>
<b>3585</b>
<v>1</v>
</b>
<b>
<a>7878</a>
<b>7879</b>
<v>1</v>
</b>
<b>
<a>19885</a>
<b>19886</b>
<v>1</v>
</b>
<b>
<a>34695</a>
<b>34696</b>
<v>1</v>
</b>
<b>
<a>37344</a>
<b>37345</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>signature_parameter_name</name>
<cardinality>69668</cardinality>
<columnsizes>
<e>
<k>sig</k>
<v>34695</v>
</e>
<e>
<k>index</k>
<v>20</v>
</e>
<e>
<k>name</k>
<v>4071</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>sig</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14810</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12007</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4294</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>3055</v>
</b>
<b>
<a>7</a>
<b>21</b>
<v>529</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14810</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12007</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4294</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>3055</v>
</b>
<b>
<a>7</a>
<b>21</b>
<v>529</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>26</a>
<b>27</b>
<v>1</v>
</b>
<b>
<a>37</a>
<b>38</b>
<v>1</v>
</b>
<b>
<a>45</a>
<b>46</b>
<v>1</v>
</b>
<b>
<a>91</a>
<b>92</b>
<v>1</v>
</b>
<b>
<a>219</a>
<b>220</b>
<v>1</v>
</b>
<b>
<a>529</a>
<b>530</b>
<v>1</v>
</b>
<b>
<a>1042</a>
<b>1043</b>
<v>1</v>
</b>
<b>
<a>1574</a>
<b>1575</b>
<v>1</v>
</b>
<b>
<a>3584</a>
<b>3585</b>
<v>1</v>
</b>
<b>
<a>7878</a>
<b>7879</b>
<v>1</v>
</b>
<b>
<a>19885</a>
<b>19886</b>
<v>1</v>
</b>
<b>
<a>34695</a>
<b>34696</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>24</a>
<b>25</b>
<v>1</v>
</b>
<b>
<a>30</a>
<b>31</b>
<v>1</v>
</b>
<b>
<a>45</a>
<b>46</b>
<v>1</v>
</b>
<b>
<a>63</a>
<b>64</b>
<v>1</v>
</b>
<b>
<a>116</a>
<b>117</b>
<v>1</v>
</b>
<b>
<a>188</a>
<b>189</b>
<v>1</v>
</b>
<b>
<a>344</a>
<b>345</b>
<v>1</v>
</b>
<b>
<a>605</a>
<b>606</b>
<v>1</v>
</b>
<b>
<a>1092</a>
<b>1093</b>
<v>1</v>
</b>
<b>
<a>1741</a>
<b>1742</b>
<v>1</v>
</b>
<b>
<a>2122</a>
<b>2123</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1898</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>700</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>294</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>262</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>310</v>
</b>
<b>
<a>8</a>
<b>24</b>
<v>309</v>
</b>
<b>
<a>24</a>
<b>3588</b>
<v>298</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2804</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>738</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>290</v>
</b>
<b>
<a>4</a>
<b>15</b>
<v>239</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>number_index_type</name>
<cardinality>2038</cardinality>
<columnsizes>
<e>
<k>baseType</k>
<v>2038</v>
</e>
<e>
<k>propertyType</k>
<v>517</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>baseType</src>
<trg>propertyType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2038</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>propertyType</src>
<trg>baseType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>435</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>70</v>
</b>
<b>
<a>3</a>
<b>1259</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>string_index_type</name>
<cardinality>1102</cardinality>
<columnsizes>
<e>
<k>baseType</k>
<v>1102</v>
</e>
<e>
<k>propertyType</k>
<v>256</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>baseType</src>
<trg>propertyType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1102</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>propertyType</src>
<trg>baseType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>219</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>20</v>
</b>
<b>
<a>3</a>
<b>436</b>
<v>17</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>base_type_names</name>
<cardinality>941</cardinality>
<columnsizes>
<e>
<k>typeName</k>
<v>928</v>
</e>
<e>
<k>baseTypeName</k>
<v>369</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typeName</src>
<trg>baseTypeName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>917</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>baseTypeName</src>
<trg>typeName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>175</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>101</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>29</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>29</v>
</b>
<b>
<a>5</a>
<b>11</b>
<v>28</v>
</b>
<b>
<a>15</a>
<b>41</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>self_types</name>
<cardinality>19632</cardinality>
<columnsizes>
<e>
<k>typeName</k>
<v>14119</v>
</e>
<e>
<k>selfType</k>
<v>19632</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typeName</src>
<trg>selfType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10451</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1823</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1845</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>selfType</src>
<trg>typeName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19632</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>tuple_type_min_length</name>
<cardinality>241</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>241</v>
</e>
<e>
<k>minLength</k>
<v>10</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>minLength</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>241</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>minLength</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>3</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>20</a>
<b>21</b>
<v>1</v>
</b>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
<b>
<a>66</a>
<b>67</b>
<v>1</v>
</b>
<b>
<a>93</a>
<b>94</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>tuple_type_rest_index</name>
<cardinality>6</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>6</v>
</e>
<e>
<k>index</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>comments</name>
<key>id</key>
<cardinality>104947</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>104947</v>
</e>
<e>
<k>kind</k>
<v>5</v>
</e>
<e>
<k>toplevel</k>
<v>4497</v>
</e>
<e>
<k>text</k>
<v>73454</v>
</e>
<e>
<k>tostring</k>
<v>57955</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104947</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104947</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104947</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104947</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>8834</a>
<b>8835</b>
<v>1</v>
</b>
<b>
<a>19270</a>
<b>19271</b>
<v>1</v>
</b>
<b>
<a>76841</a>
<b>76842</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>1705</a>
<b>1706</b>
<v>1</v>
</b>
<b>
<a>3107</a>
<b>3108</b>
<v>1</v>
</b>
<b>
<a>3141</a>
<b>3142</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>4893</a>
<b>4894</b>
<v>1</v>
</b>
<b>
<a>12759</a>
<b>12760</b>
<v>1</v>
</b>
<b>
<a>55810</a>
<b>55811</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>1739</a>
<b>1740</b>
<v>1</v>
</b>
<b>
<a>2536</a>
<b>2537</b>
<v>1</v>
</b>
<b>
<a>53678</a>
<b>53679</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1034</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>512</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>332</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>260</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>388</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>401</v>
</b>
<b>
<a>10</a>
<b>14</b>
<v>354</v>
</b>
<b>
<a>14</a>
<b>21</b>
<v>365</v>
</b>
<b>
<a>21</a>
<b>36</b>
<v>338</v>
</b>
<b>
<a>36</a>
<b>99</b>
<v>339</v>
</b>
<b>
<a>99</a>
<b>6350</b>
<v>174</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1856</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1824</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>817</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1043</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>533</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>341</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>266</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>396</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>315</v>
</b>
<b>
<a>9</a>
<b>13</b>
<v>388</v>
</b>
<b>
<a>13</a>
<b>20</b>
<v>385</v>
</b>
<b>
<a>20</a>
<b>35</b>
<v>344</v>
</b>
<b>
<a>35</a>
<b>103</b>
<v>344</v>
</b>
<b>
<a>103</a>
<b>4413</b>
<v>142</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1054</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>571</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>374</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>297</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>232</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>363</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>345</v>
</b>
<b>
<a>11</a>
<b>16</b>
<v>366</v>
</b>
<b>
<a>16</a>
<b>27</b>
<v>352</v>
</b>
<b>
<a>27</a>
<b>60</b>
<v>338</v>
</b>
<b>
<a>60</a>
<b>4394</b>
<v>205</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>59626</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>10314</v>
</b>
<b>
<a>3</a>
<b>1417</b>
<v>3514</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>73446</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>62696</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8455</v>
</b>
<b>
<a>3</a>
<b>257</b>
<v>2303</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>73446</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>44781</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9203</v>
</b>
<b>
<a>3</a>
<b>4589</b>
<v>3971</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>57955</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48252</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7233</v>
</b>
<b>
<a>3</a>
<b>513</b>
<v>2470</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>55262</v>
</b>
<b>
<a>2</a>
<b>3403</b>
<v>2693</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>types</name>
<cardinality>179398</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>179398</v>
</e>
<e>
<k>kind</k>
<v>9</v>
</e>
<e>
<k>tostring</k>
<v>40918</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>179398</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>179398</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>1802</a>
<b>1803</b>
<v>1</v>
</b>
<b>
<a>6109</a>
<b>6110</b>
<v>1</v>
</b>
<b>
<a>12383</a>
<b>12384</b>
<v>1</v>
</b>
<b>
<a>159099</a>
<b>159100</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>50</a>
<b>51</b>
<v>1</v>
</b>
<b>
<a>745</a>
<b>746</b>
<v>1</v>
</b>
<b>
<a>7464</a>
<b>7465</b>
<v>1</v>
</b>
<b>
<a>32936</a>
<b>32937</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22482</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8025</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3362</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>3387</v>
</b>
<b>
<a>7</a>
<b>33</b>
<v>3070</v>
</b>
<b>
<a>33</a>
<b>7284</b>
<v>592</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>40638</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>280</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>type_child</name>
<cardinality>17410</cardinality>
<columnsizes>
<e>
<k>child</k>
<v>9118</v>
</e>
<e>
<k>parent</k>
<v>7772</v>
</e>
<e>
<k>idx</k>
<v>296</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>child</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7113</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>978</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>686</v>
</b>
<b>
<a>8</a>
<b>199</b>
<v>341</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>child</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8255</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>726</v>
</b>
<b>
<a>5</a>
<b>19</b>
<v>137</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5433</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1746</v>
</b>
<b>
<a>3</a>
<b>288</b>
<v>583</v>
</b>
<b>
<a>288</a>
<b>297</b>
<v>10</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5422</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1757</v>
</b>
<b>
<a>3</a>
<b>288</b>
<v>583</v>
</b>
<b>
<a>288</a>
<b>297</b>
<v>10</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>39</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>61</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>37</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>56</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>22</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>18</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>44</v>
</b>
<b>
<a>17</a>
<b>6068</b>
<v>15</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>15</b>
<v>13</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>90</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>81</v>
</b>
<b>
<a>20</a>
<b>23</b>
<v>3</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>75</v>
</b>
<b>
<a>24</a>
<b>55</b>
<v>23</v>
</b>
<b>
<a>55</a>
<b>7773</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ast_node_type</name>
<cardinality>1261889</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>1261889</v>
</e>
<e>
<k>typ</k>
<v>72602</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1261889</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typ</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>39248</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8371</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7888</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3053</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>6417</v>
</b>
<b>
<a>8</a>
<b>28</b>
<v>5528</v>
</b>
<b>
<a>28</a>
<b>588233</b>
<v>2097</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>declared_function_signature</name>
<cardinality>62664</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>62664</v>
</e>
<e>
<k>sig</k>
<v>21731</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>62664</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>16826</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2358</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>1683</v>
</b>
<b>
<a>6</a>
<b>10251</b>
<v>864</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>invoke_expr_signature</name>
<cardinality>140668</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>140668</v>
</e>
<e>
<k>sig</k>
<v>9111</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>sig</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>140668</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>sig</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4612</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1819</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>737</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>696</v>
</b>
<b>
<a>6</a>
<b>14</b>
<v>705</v>
</b>
<b>
<a>14</a>
<b>68351</b>
<v>542</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>invoke_expr_overload_index</name>
<cardinality>73550</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>73550</v>
</e>
<e>
<k>index</k>
<v>47</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>73550</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>17</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>4</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>3</v>
</b>
<b>
<a>8</a>
<b>16</b>
<v>4</v>
</b>
<b>
<a>27</a>
<b>155</b>
<v>4</v>
</b>
<b>
<a>211</a>
<b>68535</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>symbols</name>
<cardinality>10192</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>10192</v>
</e>
<e>
<k>kind</k>
<v>3</v>
</e>
<e>
<k>name</k>
<v>7872</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10192</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10192</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>584</a>
<b>585</b>
<v>1</v>
</b>
<b>
<a>2385</a>
<b>2386</b>
<v>1</v>
</b>
<b>
<a>7223</a>
<b>7224</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>30</a>
<b>31</b>
<v>1</v>
</b>
<b>
<a>2385</a>
<b>2386</b>
<v>1</v>
</b>
<b>
<a>5609</a>
<b>5610</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6929</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>533</v>
</b>
<b>
<a>3</a>
<b>273</b>
<v>410</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7730</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>142</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>symbol_parent</name>
<cardinality>7807</cardinality>
<columnsizes>
<e>
<k>symbol</k>
<v>7807</v>
</e>
<e>
<k>parent</k>
<v>1727</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>symbol</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7807</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>symbol</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>778</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>304</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>212</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>111</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>152</v>
</b>
<b>
<a>8</a>
<b>26</b>
<v>136</v>
</b>
<b>
<a>26</a>
<b>297</b>
<v>34</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>symbol_module</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>symbol</k>
<v>97</v>
</e>
<e>
<k>moduleName</k>
<v>98</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>symbol</src>
<trg>moduleName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>95</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>moduleName</src>
<trg>symbol</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>96</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>symbol_global</name>
<cardinality>354</cardinality>
<columnsizes>
<e>
<k>symbol</k>
<v>354</v>
</e>
<e>
<k>globalName</k>
<v>350</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>symbol</src>
<trg>globalName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>354</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>globalName</src>
<trg>symbol</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>347</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ast_node_symbol</name>
<cardinality>8173</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>8173</v>
</e>
<e>
<k>symbol</k>
<v>8155</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>symbol</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8173</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>symbol</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8147</v>
</b>
<b>
<a>2</a>
<b>12</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>type_symbol</name>
<cardinality>12383</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>12383</v>
</e>
<e>
<k>symbol</k>
<v>6743</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>symbol</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>12383</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>symbol</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6240</v>
</b>
<b>
<a>2</a>
<b>3070</b>
<v>503</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>type_property</name>
<cardinality>331170</cardinality>
<columnsizes>
<e>
<k>typ</k>
<v>49305</v>
</e>
<e>
<k>name</k>
<v>22420</v>
</e>
<e>
<k>propertyType</k>
<v>130857</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>typ</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10275</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14770</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6020</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3153</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1700</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>4257</v>
</b>
<b>
<a>7</a>
<b>19</b>
<v>3783</v>
</b>
<b>
<a>19</a>
<b>23</b>
<v>3833</v>
</b>
<b>
<a>23</a>
<b>1390</b>
<v>1514</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typ</src>
<trg>propertyType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19351</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>10786</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>5073</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2639</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3864</v>
</b>
<b>
<a>7</a>
<b>22</b>
<v>3334</v>
</b>
<b>
<a>22</a>
<b>33</b>
<v>3710</v>
</b>
<b>
<a>33</a>
<b>1390</b>
<v>548</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4735</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7379</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2728</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1467</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>1481</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>1878</v>
</b>
<b>
<a>11</a>
<b>30</b>
<v>1682</v>
</b>
<b>
<a>30</a>
<b>7825</b>
<v>1070</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>propertyType</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14690</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2698</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1925</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>1697</v>
</b>
<b>
<a>8</a>
<b>3373</b>
<v>1410</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>propertyType</src>
<trg>typ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>112801</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12999</v>
</b>
<b>
<a>3</a>
<b>19440</b>
<v>5057</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>propertyType</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>129508</v>
</b>
<b>
<a>2</a>
<b>3475</b>
<v>1349</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>lines</name>
<key>id</key>
<cardinality>1622184</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1622184</v>
</e>
<e>
<k>toplevel</k>
<v>5312</v>
</e>
<e>
<k>text</k>
<v>648122</v>
</e>
<e>
<k>terminator</k>
<v>6</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1622184</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1622184</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>terminator</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1622184</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>12</b>
<v>425</v>
</b>
<b>
<a>12</a>
<b>24</b>
<v>415</v>
</b>
<b>
<a>24</a>
<b>37</b>
<v>419</v>
</b>
<b>
<a>37</a>
<b>50</b>
<v>404</v>
</b>
<b>
<a>50</a>
<b>66</b>
<v>411</v>
</b>
<b>
<a>66</a>
<b>85</b>
<v>400</v>
</b>
<b>
<a>85</a>
<b>108</b>
<v>405</v>
</b>
<b>
<a>108</a>
<b>138</b>
<v>402</v>
</b>
<b>
<a>138</a>
<b>174</b>
<v>402</v>
</b>
<b>
<a>174</a>
<b>232</b>
<v>405</v>
</b>
<b>
<a>232</a>
<b>331</b>
<v>399</v>
</b>
<b>
<a>331</a>
<b>547</b>
<v>399</v>
</b>
<b>
<a>548</a>
<b>4700</b>
<v>399</v>
</b>
<b>
<a>4783</a>
<b>277404</b>
<v>27</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>11</b>
<v>441</v>
</b>
<b>
<a>11</a>
<b>21</b>
<v>427</v>
</b>
<b>
<a>21</a>
<b>30</b>
<v>414</v>
</b>
<b>
<a>30</a>
<b>40</b>
<v>452</v>
</b>
<b>
<a>40</a>
<b>51</b>
<v>435</v>
</b>
<b>
<a>51</a>
<b>64</b>
<v>413</v>
</b>
<b>
<a>64</a>
<b>79</b>
<v>404</v>
</b>
<b>
<a>79</a>
<b>96</b>
<v>401</v>
</b>
<b>
<a>96</a>
<b>121</b>
<v>400</v>
</b>
<b>
<a>121</a>
<b>158</b>
<v>401</v>
</b>
<b>
<a>158</a>
<b>220</b>
<v>399</v>
</b>
<b>
<a>220</a>
<b>387</b>
<v>401</v>
</b>
<b>
<a>388</a>
<b>60934</b>
<v>324</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>terminator</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5046</v>
</b>
<b>
<a>2</a>
<b>6</b>
<v>266</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>513961</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>84265</v>
</b>
<b>
<a>3</a>
<b>49</b>
<v>48993</v>
</b>
<b>
<a>49</a>
<b>175121</b>
<v>903</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>569267</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>56143</v>
</b>
<b>
<a>3</a>
<b>5068</b>
<v>22712</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>terminator</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>647931</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>191</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>terminator</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>349</a>
<b>350</b>
<v>1</v>
</b>
<b>
<a>1830</a>
<b>1831</b>
<v>1</v>
</b>
<b>
<a>1619996</a>
<b>1619997</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>terminator</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>349</a>
<b>350</b>
<v>1</v>
</b>
<b>
<a>5218</a>
<b>5219</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>terminator</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>110</a>
<b>111</b>
<v>1</v>
</b>
<b>
<a>1093</a>
<b>1094</b>
<v>1</v>
</b>
<b>
<a>647111</a>
<b>647112</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>indentation</name>
<cardinality>1145010</cardinality>
<columnsizes>
<e>
<k>file</k>
<v>5728</v>
</e>
<e>
<k>lineno</k>
<v>40788</v>
</e>
<e>
<k>indentChar</k>
<v>2</v>
</e>
<e>
<k>indentDepth</k>
<v>72</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>file</src>
<trg>lineno</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>9</b>
<v>440</v>
</b>
<b>
<a>9</a>
<b>18</b>
<v>471</v>
</b>
<b>
<a>18</a>
<b>29</b>
<v>439</v>
</b>
<b>
<a>29</a>
<b>41</b>
<v>451</v>
</b>
<b>
<a>41</a>
<b>54</b>
<v>460</v>
</b>
<b>
<a>54</a>
<b>71</b>
<v>442</v>
</b>
<b>
<a>71</a>
<b>91</b>
<v>441</v>
</b>
<b>
<a>91</a>
<b>118</b>
<v>430</v>
</b>
<b>
<a>118</a>
<b>152</b>
<v>432</v>
</b>
<b>
<a>152</a>
<b>205</b>
<v>434</v>
</b>
<b>
<a>205</a>
<b>295</b>
<v>431</v>
</b>
<b>
<a>295</a>
<b>503</b>
<v>430</v>
</b>
<b>
<a>503</a>
<b>38151</b>
<v>427</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>indentChar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5692</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>36</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>indentDepth</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>287</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>401</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>665</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>815</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>814</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>687</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>567</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>390</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>503</v>
</b>
<b>
<a>11</a>
<b>17</b>
<v>462</v>
</b>
<b>
<a>17</a>
<b>67</b>
<v>137</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>lineno</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10935</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5303</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12061</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>3644</v>
</b>
<b>
<a>6</a>
<b>13</b>
<v>3223</v>
</b>
<b>
<a>13</a>
<b>31</b>
<v>3090</v>
</b>
<b>
<a>31</a>
<b>3986</b>
<v>2532</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>lineno</src>
<trg>indentChar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38720</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2068</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>lineno</src>
<trg>indentDepth</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11626</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7847</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>10434</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2688</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>3316</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>3144</v>
</b>
<b>
<a>13</a>
<b>39</b>
<v>1733</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentChar</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
<b>
<a>5722</a>
<b>5723</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentChar</src>
<trg>lineno</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2068</a>
<b>2069</b>
<v>1</v>
</b>
<b>
<a>40788</a>
<b>40789</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentChar</src>
<trg>indentDepth</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>72</a>
<b>73</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentDepth</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>6</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>6</v>
</b>
<b>
<a>9</a>
<b>20</b>
<v>6</v>
</b>
<b>
<a>21</a>
<b>30</b>
<v>6</v>
</b>
<b>
<a>38</a>
<b>57</b>
<v>6</v>
</b>
<b>
<a>59</a>
<b>90</b>
<v>6</v>
</b>
<b>
<a>90</a>
<b>124</b>
<v>6</v>
</b>
<b>
<a>132</a>
<b>160</b>
<v>6</v>
</b>
<b>
<a>165</a>
<b>211</b>
<v>6</v>
</b>
<b>
<a>213</a>
<b>337</b>
<v>6</v>
</b>
<b>
<a>377</a>
<b>1532</b>
<v>6</v>
</b>
<b>
<a>1919</a>
<b>5487</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentDepth</src>
<trg>lineno</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>8</b>
<v>6</v>
</b>
<b>
<a>11</a>
<b>19</b>
<v>6</v>
</b>
<b>
<a>25</a>
<b>44</b>
<v>6</v>
</b>
<b>
<a>53</a>
<b>67</b>
<v>6</v>
</b>
<b>
<a>67</a>
<b>89</b>
<v>6</v>
</b>
<b>
<a>102</a>
<b>169</b>
<v>6</v>
</b>
<b>
<a>183</a>
<b>239</b>
<v>6</v>
</b>
<b>
<a>269</a>
<b>411</b>
<v>6</v>
</b>
<b>
<a>417</a>
<b>971</b>
<v>6</v>
</b>
<b>
<a>1129</a>
<b>2732</b>
<v>6</v>
</b>
<b>
<a>4374</a>
<b>9301</b>
<v>6</v>
</b>
<b>
<a>11828</a>
<b>21226</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>indentDepth</src>
<trg>indentChar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>62</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>10</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>js_parse_errors</name>
<cardinality>3</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>3</v>
</e>
<e>
<k>toplevel</k>
<v>3</v>
</e>
<e>
<k>message</k>
<v>1</v>
</e>
<e>
<k>line</k>
<v>3</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>regexpterm</name>
<key>id</key>
<cardinality>33197</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>33197</v>
</e>
<e>
<k>kind</k>
<v>25</v>
</e>
<e>
<k>parent</k>
<v>13313</v>
</e>
<e>
<k>idx</k>
<v>76</v>
</e>
<e>
<k>tostring</k>
<v>4610</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33197</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33197</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33197</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33197</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>16</b>
<v>2</v>
</b>
<b>
<a>59</a>
<b>100</b>
<v>2</v>
</b>
<b>
<a>146</a>
<b>265</b>
<v>2</v>
</b>
<b>
<a>445</a>
<b>479</b>
<v>2</v>
</b>
<b>
<a>599</a>
<b>620</b>
<v>2</v>
</b>
<b>
<a>637</a>
<b>642</b>
<v>2</v>
</b>
<b>
<a>826</a>
<b>1058</b>
<v>2</v>
</b>
<b>
<a>1067</a>
<b>1474</b>
<v>2</v>
</b>
<b>
<a>1573</a>
<b>1693</b>
<v>2</v>
</b>
<b>
<a>2613</a>
<b>3372</b>
<v>2</v>
</b>
<b>
<a>15489</a>
<b>15490</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>2</v>
</b>
<b>
<a>15</a>
<b>46</b>
<v>2</v>
</b>
<b>
<a>79</a>
<b>132</b>
<v>2</v>
</b>
<b>
<a>132</a>
<b>331</b>
<v>2</v>
</b>
<b>
<a>367</a>
<b>381</b>
<v>2</v>
</b>
<b>
<a>437</a>
<b>638</b>
<v>2</v>
</b>
<b>
<a>641</a>
<b>737</b>
<v>2</v>
</b>
<b>
<a>825</a>
<b>1005</b>
<v>2</v>
</b>
<b>
<a>1391</a>
<b>1403</b>
<v>2</v>
</b>
<b>
<a>1465</a>
<b>1645</b>
<v>2</v>
</b>
<b>
<a>2691</a>
<b>3963</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>2</v>
</b>
<b>
<a>17</a>
<b>19</b>
<v>2</v>
</b>
<b>
<a>19</a>
<b>21</b>
<v>2</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>2</v>
</b>
<b>
<a>25</a>
<b>27</b>
<v>2</v>
</b>
<b>
<a>27</a>
<b>30</b>
<v>2</v>
</b>
<b>
<a>42</a>
<b>49</b>
<v>2</v>
</b>
<b>
<a>73</a>
<b>74</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>13</a>
<b>28</b>
<v>2</v>
</b>
<b>
<a>31</a>
<b>59</b>
<v>2</v>
</b>
<b>
<a>65</a>
<b>78</b>
<v>2</v>
</b>
<b>
<a>100</a>
<b>118</b>
<v>2</v>
</b>
<b>
<a>149</a>
<b>171</b>
<v>2</v>
</b>
<b>
<a>175</a>
<b>391</b>
<v>2</v>
</b>
<b>
<a>433</a>
<b>791</b>
<v>2</v>
</b>
<b>
<a>1992</a>
<b>1993</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7691</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2568</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>924</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>1189</v>
</b>
<b>
<a>7</a>
<b>77</b>
<v>941</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10080</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2026</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>1068</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>139</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7691</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2568</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>924</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>1189</v>
</b>
<b>
<a>7</a>
<b>77</b>
<v>941</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7733</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2644</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>940</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>1230</v>
</b>
<b>
<a>7</a>
<b>32</b>
<v>766</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>7</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>7</v>
</b>
<b>
<a>15</a>
<b>22</b>
<v>6</v>
</b>
<b>
<a>26</a>
<b>35</b>
<v>5</v>
</b>
<b>
<a>37</a>
<b>51</b>
<v>6</v>
</b>
<b>
<a>53</a>
<b>75</b>
<v>6</v>
</b>
<b>
<a>79</a>
<b>141</b>
<v>6</v>
</b>
<b>
<a>186</a>
<b>325</b>
<v>6</v>
</b>
<b>
<a>385</a>
<b>1182</b>
<v>6</v>
</b>
<b>
<a>1578</a>
<b>13314</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>18</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>15</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>8</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>6</v>
</b>
<b>
<a>9</a>
<b>13</b>
<v>6</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>7</v>
</b>
<b>
<a>17</a>
<b>20</b>
<v>7</v>
</b>
<b>
<a>21</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>7</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>7</v>
</b>
<b>
<a>15</a>
<b>22</b>
<v>6</v>
</b>
<b>
<a>26</a>
<b>35</b>
<v>5</v>
</b>
<b>
<a>37</a>
<b>51</b>
<v>6</v>
</b>
<b>
<a>53</a>
<b>75</b>
<v>6</v>
</b>
<b>
<a>79</a>
<b>141</b>
<v>6</v>
</b>
<b>
<a>186</a>
<b>325</b>
<v>6</v>
</b>
<b>
<a>385</a>
<b>1182</b>
<v>6</v>
</b>
<b>
<a>1578</a>
<b>13314</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>6</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>6</v>
</b>
<b>
<a>10</a>
<b>15</b>
<v>6</v>
</b>
<b>
<a>16</a>
<b>21</b>
<v>7</v>
</b>
<b>
<a>21</a>
<b>26</b>
<v>6</v>
</b>
<b>
<a>29</a>
<b>48</b>
<v>6</v>
</b>
<b>
<a>48</a>
<b>75</b>
<v>6</v>
</b>
<b>
<a>82</a>
<b>147</b>
<v>6</v>
</b>
<b>
<a>158</a>
<b>940</b>
<v>6</v>
</b>
<b>
<a>3258</a>
<b>3259</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3026</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>751</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>391</v>
</b>
<b>
<a>5</a>
<b>49</b>
<v>346</v>
</b>
<b>
<a>49</a>
<b>1013</b>
<v>96</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4605</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3041</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>746</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>389</v>
</b>
<b>
<a>5</a>
<b>53</b>
<v>346</v>
</b>
<b>
<a>54</a>
<b>875</b>
<v>88</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4102</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>351</v>
</b>
<b>
<a>5</a>
<b>58</b>
<v>157</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>regexp_parse_errors</name>
<key>id</key>
<cardinality>122</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>122</v>
</e>
<e>
<k>regexp</k>
<v>41</v>
</e>
<e>
<k>message</k>
<v>5</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>regexp</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>122</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>122</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>regexp</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>5</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>7</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>regexp</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>18</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>68</a>
<b>69</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>regexp</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>22</a>
<b>23</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_greedy</name>
<cardinality>2629</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>2629</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>isOptionalChaining</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
</columnsizes>
<dependencies/>
</relation>

<relation>
<name>range_quantifier_lower_bound</name>
<cardinality>146</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>146</v>
</e>
<e>
<k>lo</k>
<v>11</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>lo</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>146</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>lo</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>20</a>
<b>21</b>
<v>1</v>
</b>
<b>
<a>28</a>
<b>29</b>
<v>1</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>range_quantifier_upper_bound</name>
<cardinality>45</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>45</v>
</e>
<e>
<k>hi</k>
<v>13</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>hi</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>hi</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_capture</name>
<cardinality>1280</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1280</v>
</e>
<e>
<k>number</k>
<v>14</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>number</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1280</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>number</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>55</a>
<b>56</b>
<v>1</v>
</b>
<b>
<a>108</a>
<b>109</b>
<v>1</v>
</b>
<b>
<a>276</a>
<b>277</b>
<v>1</v>
</b>
<b>
<a>774</a>
<b>775</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_named_capture</name>
<cardinality>1280</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1280</v>
</e>
<e>
<k>name</k>
<v>14</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1280</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>55</a>
<b>56</b>
<v>1</v>
</b>
<b>
<a>108</a>
<b>109</b>
<v>1</v>
</b>
<b>
<a>276</a>
<b>277</b>
<v>1</v>
</b>
<b>
<a>774</a>
<b>775</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>is_inverted</name>
<cardinality>458</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>458</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>regexp_const_value</name>
<cardinality>19032</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>19032</v>
</e>
<e>
<k>value</k>
<v>237</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19032</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>80</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>10</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>20</v>
</b>
<b>
<a>5</a>
<b>17</b>
<v>18</v>
</b>
<b>
<a>17</a>
<b>30</b>
<v>18</v>
</b>
<b>
<a>30</a>
<b>66</b>
<v>18</v>
</b>
<b>
<a>68</a>
<b>143</b>
<v>18</v>
</b>
<b>
<a>155</a>
<b>242</b>
<v>18</v>
</b>
<b>
<a>251</a>
<b>555</b>
<v>18</v>
</b>
<b>
<a>581</a>
<b>1013</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>char_class_escape</name>
<cardinality>1573</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1573</v>
</e>
<e>
<k>value</k>
<v>6</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1573</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>92</a>
<b>93</b>
<v>1</v>
</b>
<b>
<a>199</a>
<b>200</b>
<v>1</v>
</b>
<b>
<a>378</a>
<b>379</b>
<v>1</v>
</b>
<b>
<a>879</a>
<b>880</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>unicode_property_escapename</name>
<cardinality>1573</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1573</v>
</e>
<e>
<k>name</k>
<v>6</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1573</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>92</a>
<b>93</b>
<v>1</v>
</b>
<b>
<a>199</a>
<b>200</b>
<v>1</v>
</b>
<b>
<a>378</a>
<b>379</b>
<v>1</v>
</b>
<b>
<a>879</a>
<b>880</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>unicode_property_escapevalue</name>
<cardinality>1573</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1573</v>
</e>
<e>
<k>value</k>
<v>6</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1573</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>92</a>
<b>93</b>
<v>1</v>
</b>
<b>
<a>199</a>
<b>200</b>
<v>1</v>
</b>
<b>
<a>378</a>
<b>379</b>
<v>1</v>
</b>
<b>
<a>879</a>
<b>880</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>backref</name>
<cardinality>11</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>11</v>
</e>
<e>
<k>value</k>
<v>4</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>named_backref</name>
<cardinality>11</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>11</v>
</e>
<e>
<k>name</k>
<v>4</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>tokeninfo</name>
<key>id</key>
<cardinality>8770869</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>8770869</v>
</e>
<e>
<k>kind</k>
<v>9</v>
</e>
<e>
<k>toplevel</k>
<v>5312</v>
</e>
<e>
<k>idx</k>
<v>1581031</v>
</e>
<e>
<k>value</k>
<v>234179</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8770869</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8770869</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8770869</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8770869</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2773</a>
<b>2774</b>
<v>1</v>
</b>
<b>
<a>5312</a>
<b>5313</b>
<v>1</v>
</b>
<b>
<a>15526</a>
<b>15527</b>
<v>1</v>
</b>
<b>
<a>31654</a>
<b>31655</b>
<v>1</v>
</b>
<b>
<a>269555</a>
<b>269556</b>
<v>1</v>
</b>
<b>
<a>551767</a>
<b>551768</b>
<v>1</v>
</b>
<b>
<a>557620</a>
<b>557621</b>
<v>1</v>
</b>
<b>
<a>2268328</a>
<b>2268329</b>
<v>1</v>
</b>
<b>
<a>5068334</a>
<b>5068335</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>471</a>
<b>472</b>
<v>1</v>
</b>
<b>
<a>2204</a>
<b>2205</b>
<v>1</v>
</b>
<b>
<a>2851</a>
<b>2852</b>
<v>1</v>
</b>
<b>
<a>3204</a>
<b>3205</b>
<v>1</v>
</b>
<b>
<a>5089</a>
<b>5090</b>
<v>1</v>
</b>
<b>
<a>5219</a>
<b>5220</b>
<v>1</v>
</b>
<b>
<a>5294</a>
<b>5295</b>
<v>1</v>
</b>
<b>
<a>5300</a>
<b>5301</b>
<v>1</v>
</b>
<b>
<a>5312</a>
<b>5313</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1949</a>
<b>1950</b>
<v>1</v>
</b>
<b>
<a>2130</a>
<b>2131</b>
<v>1</v>
</b>
<b>
<a>8409</a>
<b>8410</b>
<v>1</v>
</b>
<b>
<a>12883</a>
<b>12884</b>
<v>1</v>
</b>
<b>
<a>51181</a>
<b>51182</b>
<v>1</v>
</b>
<b>
<a>130388</a>
<b>130389</b>
<v>1</v>
</b>
<b>
<a>409369</a>
<b>409370</b>
<v>1</v>
</b>
<b>
<a>583910</a>
<b>583911</b>
<v>1</v>
</b>
<b>
<a>1104589</a>
<b>1104590</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>34</a>
<b>35</b>
<v>1</v>
</b>
<b>
<a>52</a>
<b>53</b>
<v>1</v>
</b>
<b>
<a>1596</a>
<b>1597</b>
<v>1</v>
</b>
<b>
<a>59827</a>
<b>59828</b>
<v>1</v>
</b>
<b>
<a>85214</a>
<b>85215</b>
<v>1</v>
</b>
<b>
<a>87463</a>
<b>87464</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>45</b>
<v>403</v>
</b>
<b>
<a>45</a>
<b>95</b>
<v>408</v>
</b>
<b>
<a>95</a>
<b>149</b>
<v>399</v>
</b>
<b>
<a>149</a>
<b>212</b>
<v>408</v>
</b>
<b>
<a>212</a>
<b>291</b>
<v>405</v>
</b>
<b>
<a>291</a>
<b>362</b>
<v>399</v>
</b>
<b>
<a>362</a>
<b>461</b>
<v>401</v>
</b>
<b>
<a>461</a>
<b>585</b>
<v>399</v>
</b>
<b>
<a>585</a>
<b>756</b>
<v>399</v>
</b>
<b>
<a>756</a>
<b>1013</b>
<v>399</v>
</b>
<b>
<a>1013</a>
<b>1389</b>
<v>399</v>
</b>
<b>
<a>1389</a>
<b>2313</b>
<v>400</v>
</b>
<b>
<a>2320</a>
<b>6681</b>
<v>399</v>
</b>
<b>
<a>6717</a>
<b>1581032</b>
<v>94</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>174</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1046</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1326</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1279</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1214</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>273</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>45</b>
<v>403</v>
</b>
<b>
<a>45</a>
<b>95</b>
<v>408</v>
</b>
<b>
<a>95</a>
<b>149</b>
<v>399</v>
</b>
<b>
<a>149</a>
<b>212</b>
<v>408</v>
</b>
<b>
<a>212</a>
<b>291</b>
<v>405</v>
</b>
<b>
<a>291</a>
<b>362</b>
<v>399</v>
</b>
<b>
<a>362</a>
<b>461</b>
<v>401</v>
</b>
<b>
<a>461</a>
<b>585</b>
<v>399</v>
</b>
<b>
<a>585</a>
<b>756</b>
<v>399</v>
</b>
<b>
<a>756</a>
<b>1013</b>
<v>399</v>
</b>
<b>
<a>1013</a>
<b>1389</b>
<v>399</v>
</b>
<b>
<a>1389</a>
<b>2313</b>
<v>400</v>
</b>
<b>
<a>2320</a>
<b>6681</b>
<v>399</v>
</b>
<b>
<a>6717</a>
<b>1581032</b>
<v>94</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>toplevel</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>21</b>
<v>423</v>
</b>
<b>
<a>21</a>
<b>33</b>
<v>416</v>
</b>
<b>
<a>33</a>
<b>44</b>
<v>424</v>
</b>
<b>
<a>44</a>
<b>55</b>
<v>400</v>
</b>
<b>
<a>55</a>
<b>65</b>
<v>426</v>
</b>
<b>
<a>65</a>
<b>76</b>
<v>407</v>
</b>
<b>
<a>76</a>
<b>88</b>
<v>426</v>
</b>
<b>
<a>88</a>
<b>102</b>
<v>402</v>
</b>
<b>
<a>102</a>
<b>120</b>
<v>405</v>
</b>
<b>
<a>120</a>
<b>144</b>
<v>401</v>
</b>
<b>
<a>144</a>
<b>180</b>
<v>400</v>
</b>
<b>
<a>180</a>
<b>260</b>
<v>400</v>
</b>
<b>
<a>260</a>
<b>46630</b>
<v>382</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1083847</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>166188</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>136823</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>123495</v>
</b>
<b>
<a>9</a>
<b>5313</b>
<v>70678</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1175018</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>207984</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>120754</v>
</b>
<b>
<a>4</a>
<b>10</b>
<v>77275</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1083847</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>166188</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>136823</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>123495</v>
</b>
<b>
<a>9</a>
<b>5313</b>
<v>70678</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1089271</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>165753</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>104658</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>145624</v>
</b>
<b>
<a>8</a>
<b>1449</b>
<v>75725</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104636</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>47235</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>20077</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>16835</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>19608</v>
</b>
<b>
<a>9</a>
<b>34</b>
<v>17687</v>
</b>
<b>
<a>34</a>
<b>789848</b>
<v>8101</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>234168</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>toplevel</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>174552</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>34819</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>18537</v>
</b>
<b>
<a>8</a>
<b>5313</b>
<v>6271</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>105969</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>47057</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19986</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>16682</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>19402</v>
</b>
<b>
<a>9</a>
<b>36</b>
<v>17686</v>
</b>
<b>
<a>36</a>
<b>347359</b>
<v>7397</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>next_token</name>
<cardinality>104943</cardinality>
<columnsizes>
<e>
<k>comment</k>
<v>104943</v>
</e>
<e>
<k>token</k>
<v>74457</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>comment</src>
<trg>token</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>104943</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>token</src>
<trg>comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>59983</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8628</v>
</b>
<b>
<a>3</a>
<b>12</b>
<v>5601</v>
</b>
<b>
<a>12</a>
<b>141</b>
<v>245</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>json</name>
<key>id</key>
<cardinality>1643352</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1643352</v>
</e>
<e>
<k>kind</k>
<v>6</v>
</e>
<e>
<k>parent</k>
<v>617634</v>
</e>
<e>
<k>idx</k>
<v>159429</v>
</e>
<e>
<k>tostring</k>
<v>768907</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1643352</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1643352</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1643352</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1643352</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>24</a>
<b>25</b>
<v>1</v>
</b>
<b>
<a>654</a>
<b>655</b>
<v>1</v>
</b>
<b>
<a>175925</a>
<b>175926</b>
<v>1</v>
</b>
<b>
<a>273113</a>
<b>273114</b>
<v>1</v>
</b>
<b>
<a>441281</a>
<b>441282</b>
<v>1</v>
</b>
<b>
<a>752355</a>
<b>752356</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>411</a>
<b>412</b>
<v>1</v>
</b>
<b>
<a>165183</a>
<b>165184</b>
<v>1</v>
</b>
<b>
<a>167132</a>
<b>167133</b>
<v>1</v>
</b>
<b>
<a>271547</a>
<b>271548</b>
<v>1</v>
</b>
<b>
<a>452264</a>
<b>452265</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>65</a>
<b>66</b>
<v>1</v>
</b>
<b>
<a>152</a>
<b>153</b>
<v>1</v>
</b>
<b>
<a>174</a>
<b>175</b>
<v>1</v>
</b>
<b>
<a>198</a>
<b>199</b>
<v>1</v>
</b>
<b>
<a>159429</a>
<b>159430</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>2865</a>
<b>2866</b>
<v>1</v>
</b>
<b>
<a>100735</a>
<b>100736</b>
<v>1</v>
</b>
<b>
<a>271467</a>
<b>271468</b>
<v>1</v>
</b>
<b>
<a>393837</a>
<b>393838</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>127476</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>184044</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>285109</v>
</b>
<b>
<a>4</a>
<b>159430</b>
<v>21005</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>179808</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>437119</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>707</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>127476</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>184044</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>285109</v>
</b>
<b>
<a>4</a>
<b>159430</b>
<v>21005</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>173483</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>197229</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>240036</v>
</b>
<b>
<a>4</a>
<b>135127</b>
<v>6886</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>158929</v>
</b>
<b>
<a>3</a>
<b>617635</b>
<v>500</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>159178</v>
</b>
<b>
<a>2</a>
<b>7</b>
<v>251</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>158929</v>
</b>
<b>
<a>3</a>
<b>617635</b>
<v>500</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>158929</v>
</b>
<b>
<a>2</a>
<b>429145</b>
<v>500</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>511110</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>165121</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>69702</v>
</b>
<b>
<a>6</a>
<b>63547</b>
<v>22974</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>768907</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>562365</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>144455</v>
</b>
<b>
<a>3</a>
<b>10</b>
<v>58431</v>
</b>
<b>
<a>10</a>
<b>63547</b>
<v>3656</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>554379</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>185366</v>
</b>
<b>
<a>3</a>
<b>720</b>
<v>29162</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>json_literals</name>
<cardinality>1026146</cardinality>
<columnsizes>
<e>
<k>value</k>
<v>397229</v>
</e>
<e>
<k>raw</k>
<v>397431</v>
</e>
<e>
<k>expr</k>
<v>1026146</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>value</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>397027</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>202</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>216149</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>128106</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>28217</v>
</b>
<b>
<a>5</a>
<b>63547</b>
<v>24757</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>397431</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>raw</src>
<trg>expr</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>216237</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>128277</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>28205</v>
</b>
<b>
<a>5</a>
<b>63547</b>
<v>24712</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expr</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1026146</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>expr</src>
<trg>raw</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1026146</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>json_properties</name>
<cardinality>1186648</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>441238</v>
</e>
<e>
<k>property</k>
<v>2285</v>
</e>
<e>
<k>value</k>
<v>1186648</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>obj</src>
<trg>property</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>685</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>161803</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>272428</v>
</b>
<b>
<a>4</a>
<b>252</b>
<v>6322</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>obj</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>685</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>161803</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>272428</v>
</b>
<b>
<a>4</a>
<b>252</b>
<v>6322</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>property</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1378</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>371</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>199</v>
</b>
<b>
<a>4</a>
<b>17</b>
<v>174</v>
</b>
<b>
<a>18</a>
<b>429290</b>
<v>163</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>property</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1378</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>371</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>199</v>
</b>
<b>
<a>4</a>
<b>17</b>
<v>174</v>
</b>
<b>
<a>18</a>
<b>429290</b>
<v>163</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1186648</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>property</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1186648</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>json_errors</name>
<key>id</key>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1</v>
</e>
<e>
<k>message</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>json_locations</name>
<cardinality>712</cardinality>
<columnsizes>
<e>
<k>locatable</k>
<v>712</v>
</e>
<e>
<k>location</k>
<v>712</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>locatable</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>712</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>locatable</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>712</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>hasLocation</name>
<cardinality>19213780</cardinality>
<columnsizes>
<e>
<k>locatable</k>
<v>19213780</v>
</e>
<e>
<k>location</k>
<v>15664049</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>locatable</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19213780</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>locatable</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>12144311</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3490097</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>29641</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>entry_cfg_node</name>
<key>id</key>
<cardinality>121542</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>121542</v>
</e>
<e>
<k>container</k>
<v>121542</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>container</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>121542</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>container</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>121542</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>exit_cfg_node</name>
<key>id</key>
<cardinality>121542</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>121542</v>
</e>
<e>
<k>container</k>
<v>121542</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>container</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>121542</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>container</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>121542</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>guard_node</name>
<cardinality>177785</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>177785</v>
</e>
<e>
<k>kind</k>
<v>2</v>
</e>
<e>
<k>test</k>
<v>91338</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>177785</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>test</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>177785</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>86336</a>
<b>86337</b>
<v>1</v>
</b>
<b>
<a>91449</a>
<b>91450</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>test</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>82430</a>
<b>82431</b>
<v>1</v>
</b>
<b>
<a>89999</a>
<b>90000</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>test</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10245</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>76994</v>
</b>
<b>
<a>3</a>
<b>21</b>
<v>4099</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>test</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10247</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>81091</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>successor</name>
<cardinality>6873752</cardinality>
<columnsizes>
<e>
<k>pred</k>
<v>6717415</v>
</e>
<e>
<k>succ</k>
<v>6718602</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>pred</src>
<trg>succ</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6588118</v>
</b>
<b>
<a>2</a>
<b>21</b>
<v>129297</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>succ</src>
<trg>pred</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6617438</v>
</b>
<b>
<a>2</a>
<b>253</b>
<v>101164</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc</name>
<key>id</key>
<cardinality>19270</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>19270</v>
</e>
<e>
<k>description</k>
<v>9383</v>
</e>
<e>
<k>comment</k>
<v>19270</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>description</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19270</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19270</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>description</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7588</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1387</v>
</b>
<b>
<a>3</a>
<b>5727</b>
<v>408</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>description</src>
<trg>comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7588</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1387</v>
</b>
<b>
<a>3</a>
<b>5727</b>
<v>408</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>comment</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19270</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>comment</src>
<trg>description</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19270</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_tags</name>
<key>id</key>
<cardinality>29323</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>29323</v>
</e>
<e>
<k>title</k>
<v>92</v>
</e>
<e>
<k>parent</k>
<v>14226</v>
</e>
<e>
<k>idx</k>
<v>66</v>
</e>
<e>
<k>tostring</k>
<v>92</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>title</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>29323</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>29323</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>29323</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>29323</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>title</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>8</v>
</b>
<b>
<a>8</a>
<b>12</b>
<v>7</v>
</b>
<b>
<a>13</a>
<b>17</b>
<v>7</v>
</b>
<b>
<a>20</a>
<b>35</b>
<v>7</v>
</b>
<b>
<a>40</a>
<b>55</b>
<v>7</v>
</b>
<b>
<a>58</a>
<b>111</b>
<v>7</v>
</b>
<b>
<a>114</a>
<b>167</b>
<v>8</v>
</b>
<b>
<a>170</a>
<b>331</b>
<v>7</v>
</b>
<b>
<a>587</a>
<b>913</b>
<v>7</v>
</b>
<b>
<a>2221</a>
<b>10284</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>title</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>5</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>7</v>
</b>
<b>
<a>6</a>
<b>10</b>
<v>8</v>
</b>
<b>
<a>10</a>
<b>16</b>
<v>7</v>
</b>
<b>
<a>16</a>
<b>26</b>
<v>7</v>
</b>
<b>
<a>26</a>
<b>36</b>
<v>7</v>
</b>
<b>
<a>38</a>
<b>67</b>
<v>7</v>
</b>
<b>
<a>68</a>
<b>111</b>
<v>7</v>
</b>
<b>
<a>137</a>
<b>213</b>
<v>7</v>
</b>
<b>
<a>232</a>
<b>702</b>
<v>7</v>
</b>
<b>
<a>870</a>
<b>6020</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>title</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>35</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>8</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>8</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>5</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>8</v>
</b>
<b>
<a>10</a>
<b>31</b>
<v>7</v>
</b>
<b>
<a>46</a>
<b>59</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>title</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>92</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6064</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4452</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2064</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>913</v>
</b>
<b>
<a>5</a>
<b>67</b>
<v>733</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>title</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6972</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4911</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1793</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>550</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6064</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4452</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2064</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>913</v>
</b>
<b>
<a>5</a>
<b>67</b>
<v>733</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6972</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4911</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1793</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>550</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>29</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>5</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>6</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>5</v>
</b>
<b>
<a>11</a>
<b>53</b>
<v>5</v>
</b>
<b>
<a>89</a>
<b>1647</b>
<v>5</v>
</b>
<b>
<a>3710</a>
<b>14227</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>title</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>9</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>31</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>6</v>
</b>
<b>
<a>8</a>
<b>21</b>
<v>5</v>
</b>
<b>
<a>29</a>
<b>61</b>
<v>5</v>
</b>
<b>
<a>70</a>
<b>71</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>29</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>5</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>6</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>5</v>
</b>
<b>
<a>11</a>
<b>53</b>
<v>5</v>
</b>
<b>
<a>89</a>
<b>1647</b>
<v>5</v>
</b>
<b>
<a>3710</a>
<b>14227</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>9</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>31</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>6</v>
</b>
<b>
<a>8</a>
<b>21</b>
<v>5</v>
</b>
<b>
<a>29</a>
<b>61</b>
<v>5</v>
</b>
<b>
<a>70</a>
<b>71</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>8</v>
</b>
<b>
<a>8</a>
<b>12</b>
<v>7</v>
</b>
<b>
<a>13</a>
<b>17</b>
<v>7</v>
</b>
<b>
<a>20</a>
<b>35</b>
<v>7</v>
</b>
<b>
<a>40</a>
<b>55</b>
<v>7</v>
</b>
<b>
<a>58</a>
<b>111</b>
<v>7</v>
</b>
<b>
<a>114</a>
<b>167</b>
<v>8</v>
</b>
<b>
<a>170</a>
<b>331</b>
<v>7</v>
</b>
<b>
<a>587</a>
<b>913</b>
<v>7</v>
</b>
<b>
<a>2221</a>
<b>10284</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>title</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>92</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>5</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>7</v>
</b>
<b>
<a>6</a>
<b>10</b>
<v>8</v>
</b>
<b>
<a>10</a>
<b>16</b>
<v>7</v>
</b>
<b>
<a>16</a>
<b>26</b>
<v>7</v>
</b>
<b>
<a>26</a>
<b>36</b>
<v>7</v>
</b>
<b>
<a>38</a>
<b>67</b>
<v>7</v>
</b>
<b>
<a>68</a>
<b>111</b>
<v>7</v>
</b>
<b>
<a>137</a>
<b>213</b>
<v>7</v>
</b>
<b>
<a>232</a>
<b>702</b>
<v>7</v>
</b>
<b>
<a>870</a>
<b>6020</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>35</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>8</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>8</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>5</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>8</v>
</b>
<b>
<a>10</a>
<b>31</b>
<v>7</v>
</b>
<b>
<a>46</a>
<b>59</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_tag_descriptions</name>
<cardinality>13676</cardinality>
<columnsizes>
<e>
<k>tag</k>
<v>13676</v>
</e>
<e>
<k>text</k>
<v>7866</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>tag</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>13676</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6089</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1025</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>596</v>
</b>
<b>
<a>8</a>
<b>459</b>
<v>156</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_tag_names</name>
<cardinality>11506</cardinality>
<columnsizes>
<e>
<k>tag</k>
<v>11506</v>
</e>
<e>
<k>text</k>
<v>2647</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>tag</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11506</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1398</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>569</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>201</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>208</v>
</b>
<b>
<a>7</a>
<b>24</b>
<v>200</v>
</b>
<b>
<a>24</a>
<b>498</b>
<v>71</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_type_exprs</name>
<key>id</key>
<cardinality>22481</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>22481</v>
</e>
<e>
<k>kind</k>
<v>15</v>
</e>
<e>
<k>parent</k>
<v>21039</v>
</e>
<e>
<k>idx</k>
<v>17</v>
</e>
<e>
<k>tostring</k>
<v>1447</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22481</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22481</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22481</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22481</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>1</v>
</b>
<b>
<a>27</a>
<b>28</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>55</a>
<b>56</b>
<v>1</v>
</b>
<b>
<a>91</a>
<b>92</b>
<v>1</v>
</b>
<b>
<a>287</a>
<b>288</b>
<v>1</v>
</b>
<b>
<a>292</a>
<b>293</b>
<v>1</v>
</b>
<b>
<a>303</a>
<b>304</b>
<v>1</v>
</b>
<b>
<a>310</a>
<b>311</b>
<v>1</v>
</b>
<b>
<a>316</a>
<b>317</b>
<v>1</v>
</b>
<b>
<a>536</a>
<b>537</b>
<v>1</v>
</b>
<b>
<a>668</a>
<b>669</b>
<v>1</v>
</b>
<b>
<a>895</a>
<b>896</b>
<v>1</v>
</b>
<b>
<a>18639</a>
<b>18640</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>55</a>
<b>56</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
<b>
<a>287</a>
<b>288</b>
<v>2</v>
</b>
<b>
<a>301</a>
<b>302</b>
<v>1</v>
</b>
<b>
<a>310</a>
<b>311</b>
<v>1</v>
</b>
<b>
<a>314</a>
<b>315</b>
<v>1</v>
</b>
<b>
<a>524</a>
<b>525</b>
<v>1</v>
</b>
<b>
<a>583</a>
<b>584</b>
<v>1</v>
</b>
<b>
<a>890</a>
<b>891</b>
<v>1</v>
</b>
<b>
<a>17717</a>
<b>17718</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>5</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>51</a>
<b>52</b>
<v>1</v>
</b>
<b>
<a>57</a>
<b>58</b>
<v>1</v>
</b>
<b>
<a>86</a>
<b>87</b>
<v>1</v>
</b>
<b>
<a>89</a>
<b>90</b>
<v>1</v>
</b>
<b>
<a>104</a>
<b>105</b>
<v>1</v>
</b>
<b>
<a>155</a>
<b>156</b>
<v>1</v>
</b>
<b>
<a>194</a>
<b>195</b>
<v>1</v>
</b>
<b>
<a>696</a>
<b>697</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19985</v>
</b>
<b>
<a>2</a>
<b>16</b>
<v>1054</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20644</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>395</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19985</v>
</b>
<b>
<a>2</a>
<b>16</b>
<v>1054</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19997</v>
</b>
<b>
<a>2</a>
<b>7</b>
<v>1042</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>4</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>32</a>
<b>33</b>
<v>1</v>
</b>
<b>
<a>93</a>
<b>94</b>
<v>1</v>
</b>
<b>
<a>165</a>
<b>166</b>
<v>1</v>
</b>
<b>
<a>340</a>
<b>341</b>
<v>1</v>
</b>
<b>
<a>750</a>
<b>751</b>
<v>1</v>
</b>
<b>
<a>21021</a>
<b>21022</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>4</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>32</a>
<b>33</b>
<v>1</v>
</b>
<b>
<a>93</a>
<b>94</b>
<v>1</v>
</b>
<b>
<a>165</a>
<b>166</b>
<v>1</v>
</b>
<b>
<a>340</a>
<b>341</b>
<v>1</v>
</b>
<b>
<a>750</a>
<b>751</b>
<v>1</v>
</b>
<b>
<a>21021</a>
<b>21022</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
<b>
<a>103</a>
<b>104</b>
<v>1</v>
</b>
<b>
<a>1378</a>
<b>1379</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>713</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>271</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>105</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>110</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>111</v>
</b>
<b>
<a>12</a>
<b>77</b>
<v>109</v>
</b>
<b>
<a>77</a>
<b>2754</b>
<v>28</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1446</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>713</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>271</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>105</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>110</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>112</v>
</b>
<b>
<a>12</a>
<b>78</b>
<v>110</v>
</b>
<b>
<a>78</a>
<b>2747</b>
<v>26</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1356</v>
</b>
<b>
<a>2</a>
<b>15</b>
<v>91</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_record_field_name</name>
<cardinality>241</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>90</v>
</e>
<e>
<k>idx</k>
<v>15</v>
</e>
<e>
<k>name</k>
<v>123</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>47</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>19</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>8</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>8</v>
</b>
<b>
<a>7</a>
<b>16</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>47</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>19</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>8</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>8</v>
</b>
<b>
<a>7</a>
<b>16</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>3</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>4</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>1</v>
</b>
<b>
<a>24</a>
<b>25</b>
<v>1</v>
</b>
<b>
<a>43</a>
<b>44</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>29</a>
<b>30</b>
<v>1</v>
</b>
<b>
<a>37</a>
<b>38</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>65</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>40</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>10</v>
</b>
<b>
<a>9</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>87</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>34</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>jsdoc_prefix_qualifier</name>
<cardinality>823</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>823</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>jsdoc_has_new_parameter</name>
<cardinality>22</cardinality>
<columnsizes>
<e>
<k>fn</k>
<v>22</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>jsdoc_errors</name>
<key>id</key>
<cardinality>1658</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1658</v>
</e>
<e>
<k>tag</k>
<v>1460</v>
</e>
<e>
<k>message</k>
<v>203</v>
</e>
<e>
<k>tostring</k>
<v>89</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1658</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1658</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1658</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1262</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>198</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1262</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>198</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1262</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>198</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>144</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>27</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>16</v>
</b>
<b>
<a>7</a>
<b>347</b>
<v>16</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>144</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>27</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>16</v>
</b>
<b>
<a>7</a>
<b>347</b>
<v>16</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>203</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>10</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>6</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>7</v>
</b>
<b>
<a>11</a>
<b>27</b>
<v>7</v>
</b>
<b>
<a>34</a>
<b>347</b>
<v>7</v>
</b>
<b>
<a>477</a>
<b>478</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>10</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>6</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>7</v>
</b>
<b>
<a>11</a>
<b>27</b>
<v>7</v>
</b>
<b>
<a>34</a>
<b>347</b>
<v>7</v>
</b>
<b>
<a>477</a>
<b>478</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>66</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>7</v>
</b>
<b>
<a>8</a>
<b>25</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml</name>
<key>id</key>
<cardinality>885</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>885</v>
</e>
<e>
<k>kind</k>
<v>4</v>
</e>
<e>
<k>parent</k>
<v>204</v>
</e>
<e>
<k>idx</k>
<v>25</v>
</e>
<e>
<k>tag</k>
<v>8</v>
</e>
<e>
<k>tostring</k>
<v>318</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>149</a>
<b>150</b>
<v>1</v>
</b>
<b>
<a>700</a>
<b>701</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>67</a>
<b>68</b>
<v>1</v>
</b>
<b>
<a>240</a>
<b>241</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>29</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>21</b>
<v>17</v>
</b>
<b>
<a>22</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>131</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>43</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>30</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>29</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>21</b>
<v>17</v>
</b>
<b>
<a>22</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>120</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>41</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>36</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>5</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>24</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>16</v>
</b>
<b>
<a>16</a>
<b>23</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>2</v>
</b>
<b>
<a>25</a>
<b>33</b>
<v>2</v>
</b>
<b>
<a>33</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>61</a>
<b>64</b>
<v>2</v>
</b>
<b>
<a>95</a>
<b>100</b>
<v>2</v>
</b>
<b>
<a>149</a>
<b>172</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>2</v>
</b>
<b>
<a>25</a>
<b>33</b>
<v>2</v>
</b>
<b>
<a>33</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>61</a>
<b>64</b>
<v>2</v>
</b>
<b>
<a>95</a>
<b>100</b>
<v>2</v>
</b>
<b>
<a>149</a>
<b>172</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>2</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>2</v>
</b>
<b>
<a>28</a>
<b>31</b>
<v>2</v>
</b>
<b>
<a>52</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>87</a>
<b>88</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>26</a>
<b>27</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>149</a>
<b>150</b>
<v>1</v>
</b>
<b>
<a>654</a>
<b>655</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
<b>
<a>67</a>
<b>68</b>
<v>1</v>
</b>
<b>
<a>223</a>
<b>224</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>209</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>42</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>29</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>25</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>13</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>318</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>213</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>41</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>27</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>25</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>272</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>34</v>
</b>
<b>
<a>3</a>
<b>10</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>318</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_anchors</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>1</v>
</e>
<e>
<k>anchor</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>anchor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>anchor</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_aliases</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>alias</k>
<v>1</v>
</e>
<e>
<k>target</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>alias</src>
<trg>target</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>target</src>
<trg>alias</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_scalars</name>
<cardinality>700</cardinality>
<columnsizes>
<e>
<k>scalar</k>
<v>700</v>
</e>
<e>
<k>style</k>
<v>3</v>
</e>
<e>
<k>value</k>
<v>241</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>scalar</src>
<trg>style</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scalar</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>style</src>
<trg>scalar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>97</a>
<b>98</b>
<v>1</v>
</b>
<b>
<a>589</a>
<b>590</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>style</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>47</a>
<b>48</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>scalar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>158</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>32</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>19</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>20</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>style</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>240</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_errors</name>
<key>id</key>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1</v>
</e>
<e>
<k>message</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_locations</name>
<cardinality>71</cardinality>
<columnsizes>
<e>
<k>locatable</k>
<v>71</v>
</e>
<e>
<k>location</k>
<v>71</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>locatable</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>71</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>locatable</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>71</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlEncoding</name>
<cardinality>39724</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>39724</v>
</e>
<e>
<k>encoding</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>encoding</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>39724</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>encoding</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>39724</a>
<b>39725</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlDTDs</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1</v>
</e>
<e>
<k>root</k>
<v>1</v>
</e>
<e>
<k>publicId</k>
<v>1</v>
</e>
<e>
<k>systemId</k>
<v>1</v>
</e>
<e>
<k>fileid</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlElements</name>
<cardinality>1270313</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1270313</v>
</e>
<e>
<k>name</k>
<v>4655</v>
</e>
<e>
<k>parentid</k>
<v>578021</v>
</e>
<e>
<k>idx</k>
<v>35122</v>
</e>
<e>
<k>fileid</k>
<v>39721</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1270313</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1270313</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1270313</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1270313</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>420</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>156</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3832</v>
</b>
<b>
<a>6</a>
<b>310317</b>
<v>247</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>456</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>150</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3829</v>
</b>
<b>
<a>6</a>
<b>161565</b>
<v>220</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4358</v>
</b>
<b>
<a>2</a>
<b>35123</b>
<v>297</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>486</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>133</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3831</v>
</b>
<b>
<a>6</a>
<b>14503</b>
<v>205</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>371969</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>62095</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>104113</v>
</b>
<b>
<a>4</a>
<b>35123</b>
<v>39844</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>500482</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17866</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>49117</v>
</b>
<b>
<a>4</a>
<b>45</b>
<v>10556</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>371969</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>62095</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>104113</v>
</b>
<b>
<a>4</a>
<b>35123</b>
<v>39844</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>578021</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>606</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17851</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>6533</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>859</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4471</v>
</b>
<b>
<a>9</a>
<b>16</b>
<v>2719</v>
</b>
<b>
<a>16</a>
<b>578022</b>
<v>2083</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>18457</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6533</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6178</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>2624</v>
</b>
<b>
<a>8</a>
<b>4397</b>
<v>1330</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>606</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17851</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>6533</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>859</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4471</v>
</b>
<b>
<a>9</a>
<b>16</b>
<v>2719</v>
</b>
<b>
<a>16</a>
<b>578022</b>
<v>2083</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>606</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17851</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>6533</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>859</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4471</v>
</b>
<b>
<a>9</a>
<b>16</b>
<v>2719</v>
</b>
<b>
<a>16</a>
<b>39722</b>
<v>2083</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20457</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3115</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>3026</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>3588</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2220</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>3099</v>
</b>
<b>
<a>11</a>
<b>19</b>
<v>3087</v>
</b>
<b>
<a>19</a>
<b>114506</b>
<v>1129</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20459</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3458</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>2569</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>2172</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>6158</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>3501</v>
</b>
<b>
<a>9</a>
<b>46</b>
<v>1404</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20457</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3870</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>2152</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2876</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2720</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>4132</v>
</b>
<b>
<a>8</a>
<b>14</b>
<v>3096</v>
</b>
<b>
<a>14</a>
<b>31079</b>
<v>418</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>25894</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5301</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3787</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>3268</v>
</b>
<b>
<a>6</a>
<b>35123</b>
<v>1471</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlAttrs</name>
<cardinality>1202020</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1202020</v>
</e>
<e>
<k>elementid</k>
<v>760198</v>
</e>
<e>
<k>name</k>
<v>3649</v>
</e>
<e>
<k>value</k>
<v>121803</v>
</e>
<e>
<k>idx</k>
<v>2000</v>
</e>
<e>
<k>fileid</k>
<v>39448</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1202020</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1202020</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1202020</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1202020</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1202020</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>425697</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>249659</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>66474</v>
</b>
<b>
<a>4</a>
<b>2001</b>
<v>18368</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>425778</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>249579</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>66475</v>
</b>
<b>
<a>4</a>
<b>2001</b>
<v>18366</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>466237</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>266291</v>
</b>
<b>
<a>3</a>
<b>46</b>
<v>27670</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>425697</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>249659</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>66474</v>
</b>
<b>
<a>4</a>
<b>2001</b>
<v>18368</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>760198</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3467</v>
</b>
<b>
<a>2</a>
<b>262475</b>
<v>182</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3467</v>
</b>
<b>
<a>2</a>
<b>262475</b>
<v>182</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3501</v>
</b>
<b>
<a>2</a>
<b>54146</b>
<v>148</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3531</v>
</b>
<b>
<a>2</a>
<b>11</b>
<v>118</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3491</v>
</b>
<b>
<a>2</a>
<b>21768</b>
<v>158</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>72032</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>42366</v>
</b>
<b>
<a>3</a>
<b>199269</b>
<v>7405</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>72036</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>42374</v>
</b>
<b>
<a>3</a>
<b>199269</b>
<v>7393</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>116722</v>
</b>
<b>
<a>2</a>
<b>2041</b>
<v>5081</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>117957</v>
</b>
<b>
<a>2</a>
<b>2001</b>
<v>3846</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>86306</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>28570</v>
</b>
<b>
<a>3</a>
<b>4175</b>
<v>6927</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1955</v>
</b>
<b>
<a>2</a>
<b>760199</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1955</v>
</b>
<b>
<a>2</a>
<b>760199</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1955</v>
</b>
<b>
<a>2</a>
<b>189</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1955</v>
</b>
<b>
<a>2</a>
<b>116643</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1955</v>
</b>
<b>
<a>2</a>
<b>39449</b>
<v>45</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22884</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>2565</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>2294</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3299</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>3272</v>
</b>
<b>
<a>9</a>
<b>16</b>
<v>3143</v>
</b>
<b>
<a>16</a>
<b>129952</b>
<v>1991</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23890</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>2131</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1971</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>4096</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>3519</v>
</b>
<b>
<a>8</a>
<b>16</b>
<v>3137</v>
</b>
<b>
<a>16</a>
<b>106600</b>
<v>704</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22946</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2338</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2726</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2824</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2994</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3876</v>
</b>
<b>
<a>7</a>
<b>2002</b>
<v>1744</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>22916</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>2772</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2112</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3510</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>1993</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>3365</v>
</b>
<b>
<a>11</a>
<b>50357</b>
<v>2780</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26133</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>9699</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>3511</v>
</b>
<b>
<a>5</a>
<b>2001</b>
<v>105</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlNs</name>
<cardinality>71201</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>4185</v>
</e>
<e>
<k>prefixName</k>
<v>958</v>
</e>
<e>
<k>URI</k>
<v>4185</v>
</e>
<e>
<k>fileid</k>
<v>39544</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2602</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1553</v>
</b>
<b>
<a>3</a>
<b>872</b>
<v>30</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4185</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>274</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3825</v>
</b>
<b>
<a>7</a>
<b>24905</b>
<v>86</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>915</v>
</b>
<b>
<a>2</a>
<b>4054</b>
<v>43</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>915</v>
</b>
<b>
<a>2</a>
<b>4054</b>
<v>43</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>828</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>73</v>
</b>
<b>
<a>5</a>
<b>24903</b>
<v>57</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4185</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2602</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1553</v>
</b>
<b>
<a>3</a>
<b>872</b>
<v>30</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>274</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3825</v>
</b>
<b>
<a>7</a>
<b>24905</b>
<v>86</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11655</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>26146</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>1743</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11653</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>25982</v>
</b>
<b>
<a>3</a>
<b>31</b>
<v>1909</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11655</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>26146</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>1743</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlHasNs</name>
<cardinality>1139730</cardinality>
<columnsizes>
<e>
<k>elementId</k>
<v>1139730</v>
</e>
<e>
<k>nsId</k>
<v>4136</v>
</e>
<e>
<k>fileid</k>
<v>39537</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>elementId</src>
<trg>nsId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1139730</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1139730</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>nsId</src>
<trg>elementId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>234</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3824</v>
</b>
<b>
<a>6</a>
<b>643289</b>
<v>78</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>nsId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>257</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>3823</v>
</b>
<b>
<a>6</a>
<b>24759</b>
<v>56</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>elementId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3669</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>20429</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>2536</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>3473</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2258</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>3036</v>
</b>
<b>
<a>11</a>
<b>18</b>
<v>2966</v>
</b>
<b>
<a>18</a>
<b>147552</b>
<v>1170</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>nsId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>18261</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>21032</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>244</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlComments</name>
<cardinality>26812</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>26812</v>
</e>
<e>
<k>text</k>
<v>22933</v>
</e>
<e>
<k>parentid</k>
<v>26546</v>
</e>
<e>
<k>fileid</k>
<v>26368</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26812</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26812</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26812</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21517</v>
</b>
<b>
<a>2</a>
<b>62</b>
<v>1416</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21519</v>
</b>
<b>
<a>2</a>
<b>62</b>
<v>1414</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21522</v>
</b>
<b>
<a>2</a>
<b>62</b>
<v>1411</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26379</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>167</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26379</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>167</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26546</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26161</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>207</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26165</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>203</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26223</v>
</b>
<b>
<a>2</a>
<b>10</b>
<v>145</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlChars</name>
<cardinality>439958</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>439958</v>
</e>
<e>
<k>text</k>
<v>100518</v>
</e>
<e>
<k>parentid</k>
<v>433851</v>
</e>
<e>
<k>idx</k>
<v>4</v>
</e>
<e>
<k>isCDATA</k>
<v>1</v>
</e>
<e>
<k>fileid</k>
<v>26494</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439958</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439958</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439958</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439958</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>439958</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>60389</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>3811</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>29257</v>
</b>
<b>
<a>5</a>
<b>23171</b>
<v>7061</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>60389</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>3811</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>29257</v>
</b>
<b>
<a>5</a>
<b>23171</b>
<v>7061</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>100517</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>100518</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>61284</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>4205</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>28328</v>
</b>
<b>
<a>5</a>
<b>351</b>
<v>6701</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>429716</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4135</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>429716</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4135</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>429716</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4135</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>433851</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>433851</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>80</a>
<b>81</b>
<v>1</v>
</b>
<b>
<a>1892</a>
<b>1893</b>
<v>1</v>
</b>
<b>
<a>4135</a>
<b>4136</b>
<v>1</v>
</b>
<b>
<a>433851</a>
<b>433852</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>16</a>
<b>17</b>
<v>1</v>
</b>
<b>
<a>100499</a>
<b>100500</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>80</a>
<b>81</b>
<v>1</v>
</b>
<b>
<a>1892</a>
<b>1893</b>
<v>1</v>
</b>
<b>
<a>4135</a>
<b>4136</b>
<v>1</v>
</b>
<b>
<a>433851</a>
<b>433852</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>46</a>
<b>47</b>
<v>1</v>
</b>
<b>
<a>97</a>
<b>98</b>
<v>1</v>
</b>
<b>
<a>26494</a>
<b>26495</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>439958</a>
<b>439959</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>100518</a>
<b>100519</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>433851</a>
<b>433852</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>26494</a>
<b>26495</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>25303</v>
</b>
<b>
<a>2</a>
<b>35123</b>
<v>1191</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>25765</v>
</b>
<b>
<a>2</a>
<b>35123</b>
<v>729</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>25312</v>
</b>
<b>
<a>2</a>
<b>35123</b>
<v>1182</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26397</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>97</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26494</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmllocations</name>
<cardinality>3051056</cardinality>
<columnsizes>
<e>
<k>xmlElement</k>
<v>2982460</v>
</e>
<e>
<k>location</k>
<v>3051056</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>xmlElement</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2978326</v>
</b>
<b>
<a>2</a>
<b>24903</b>
<v>4134</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>xmlElement</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3051056</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>filetype</name>
<cardinality>1102</cardinality>
<columnsizes>
<e>
<k>file</k>
<v>1102</v>
</e>
<e>
<k>filetype</k>
<v>3</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>file</src>
<trg>filetype</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1102</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>filetype</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>162</a>
<b>163</b>
<v>1</v>
</b>
<b>
<a>939</a>
<b>940</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>configs</name>
<cardinality>69795</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>69795</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>configNames</name>
<cardinality>69794</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>69794</v>
</e>
<e>
<k>config</k>
<v>69794</v>
</e>
<e>
<k>name</k>
<v>12859</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>config</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69794</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69794</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>config</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69794</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>config</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69794</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4858</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>593</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2806</v>
</b>
<b>
<a>4</a>
<b>10</b>
<v>169</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1900</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1757</v>
</b>
<b>
<a>12</a>
<b>111</b>
<v>776</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>config</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4858</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>593</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2806</v>
</b>
<b>
<a>4</a>
<b>10</b>
<v>169</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1900</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1757</v>
</b>
<b>
<a>12</a>
<b>111</b>
<v>776</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>configValues</name>
<cardinality>69691</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>69691</v>
</e>
<e>
<k>config</k>
<v>69691</v>
</e>
<e>
<k>value</k>
<v>54399</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>config</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>config</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>config</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69691</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48220</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>4804</v>
</b>
<b>
<a>4</a>
<b>546</b>
<v>1375</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>config</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48220</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>4804</v>
</b>
<b>
<a>4</a>
<b>546</b>
<v>1375</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>configLocations</name>
<cardinality>209280</cardinality>
<columnsizes>
<e>
<k>locatable</k>
<v>209280</v>
</e>
<e>
<k>location</k>
<v>209280</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>locatable</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>209280</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>locatable</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>209280</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>extraction_time</name>
<cardinality>378</cardinality>
<columnsizes>
<e>
<k>file</k>
<v>21</v>
</e>
<e>
<k>extractionPhase</k>
<v>9</v>
</e>
<e>
<k>timerKind</k>
<v>2</v>
</e>
<e>
<k>time</k>
<v>43</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>file</src>
<trg>extractionPhase</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>9</a>
<b>10</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>timerKind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>time</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>extractionPhase</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>21</a>
<b>22</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>extractionPhase</src>
<trg>timerKind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>extractionPhase</src>
<trg>time</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8</v>
</b>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>timerKind</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>21</a>
<b>22</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>timerKind</src>
<trg>extractionPhase</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>9</a>
<b>10</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>timerKind</src>
<trg>time</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>22</a>
<b>23</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>time</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>42</v>
</b>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>time</src>
<trg>extractionPhase</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>42</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>time</src>
<trg>timerKind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>42</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>extraction_data</name>
<cardinality>21</cardinality>
<columnsizes>
<e>
<k>file</k>
<v>21</v>
</e>
<e>
<k>cacheFile</k>
<v>21</v>
</e>
<e>
<k>fromCache</k>
<v>1</v>
</e>
<e>
<k>length</k>
<v>21</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>file</src>
<trg>cacheFile</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>fromCache</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>cacheFile</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>cacheFile</src>
<trg>fromCache</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>cacheFile</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fromCache</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fromCache</src>
<trg>cacheFile</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>fromCache</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>21</a>
<b>22</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>cacheFile</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>fromCache</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
</stats>
</dbstats>
