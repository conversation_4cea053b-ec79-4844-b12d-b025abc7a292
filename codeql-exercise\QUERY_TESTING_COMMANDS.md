# CodeQL Query Testing Commands

## 🚀 Quick Start

### **Automated Testing (Recommended)**

**Linux/macOS:**
```bash
chmod +x scripts/test-all-queries.sh
./scripts/test-all-queries.sh
```

**Windows:**
```powershell
.\scripts\test-all-queries.ps1
```

## 📋 Individual Query Commands

### **Security Queries (@kind problem)**

```bash
# Create results directory first
mkdir -p results

# 1. Custom Vulnerability Detection
codeql database analyze node-db queries/custom-vulnerability.ql --format=csv --output=results/custom-vulnerability.csv

# 2. Refined Custom Vulnerability Detection
codeql database analyze node-db queries/refined-custom-vulnerability.ql --format=csv --output=results/refined-custom-vulnerability.csv

# 3. SQL Injection Detection
codeql database analyze node-db queries/sql-injection.ql --format=csv --output=results/sql-injection.csv

# 4. Command Injection Detection (Original)
codeql database analyze node-db queries/command-injection.ql --format=csv --output=results/command-injection.csv

# 5. Simple Working Command Injection
codeql database analyze node-db queries/simple-working-cmd.ql --format=csv --output=results/simple-working-cmd.csv

# 6. Filtered Vulnerability Detection
codeql database analyze node-db queries/filtered-vulnerability.ql --format=csv --output=results/filtered-vulnerability.csv
```

### **Diagnostic Queries (@kind table)**

```bash
# 7. List Source Files
codeql query run queries/list-source-files.ql --database=node-db --output=results/list-source-files.bqrs
codeql bqrs decode --format=csv results/list-source-files.bqrs > results/list-source-files.csv

# 8. Vulnerability by File
codeql query run queries/vulnerability-by-file.ql --database=node-db --output=results/vulnerability-by-file.bqrs
codeql bqrs decode --format=csv results/vulnerability-by-file.bqrs > results/vulnerability-by-file.csv

# 9. Debug Command Injection
codeql query run queries/debug-command-injection.ql --database=node-db --output=results/debug-command-injection.bqrs
codeql bqrs decode --format=csv results/debug-command-injection.bqrs > results/debug-command-injection.csv

# 10. Debug User Input
codeql query run queries/debug-user-input.ql --database=node-db --output=results/debug-user-input.bqrs
codeql bqrs decode --format=csv results/debug-user-input.bqrs > results/debug-user-input.csv

# 11. Debug Why Empty
codeql query run queries/debug-why-empty.ql --database=node-db --output=results/debug-why-empty.bqrs
codeql bqrs decode --format=csv results/debug-why-empty.bqrs > results/debug-why-empty.csv

# 12. List All Files
codeql query run queries/list-files.ql --database=node-db --output=results/list-files.bqrs
codeql bqrs decode --format=csv results/list-files.bqrs > results/list-files.csv
```

### **Experimental Queries (May Have Issues)**

```bash
# 13. Fixed Command Injection (might fail)
codeql database analyze node-db queries/fixed-command-injection.ql --format=csv --output=results/fixed-command-injection.csv

# 14. Working Command Injection (might fail)
codeql database analyze node-db queries/working-command-injection.ql --format=csv --output=results/working-command-injection.csv

# 15. Simple Command Injection (table format)
codeql query run queries/simple-command-injection.ql --database=node-db --output=results/simple-command-injection.bqrs
codeql bqrs decode --format=csv results/simple-command-injection.bqrs > results/simple-command-injection.csv
```

## 📊 View Results

### **View All Non-Empty Results**
```bash
find results -name '*.csv' -size +1c -exec echo '=== {} ===' \; -exec cat {} \;
```

### **View Specific Result**
```bash
cat results/custom-vulnerability.csv
```

### **Count Results in Each File**
```bash
for file in results/*.csv; do
    count=$(tail -n +2 "$file" | wc -l)
    echo "$file: $count results"
done
```

## 🔍 Query Categories

| Query Type | Command | Purpose |
|------------|---------|---------|
| **@kind problem** | `codeql database analyze` | Security vulnerability detection |
| **@kind table** | `codeql query run` + `codeql bqrs decode` | Diagnostic/informational queries |

## ⚠️ Expected Results

| Query | Expected Results | Notes |
|-------|------------------|-------|
| `custom-vulnerability.ql` | 6 detections | Finds "I am hacker's paradise" patterns |
| `refined-custom-vulnerability.ql` | 4 detections | Filtered version with fewer false positives |
| `sql-injection.ql` | 0-2 detections | May find SQL construction patterns |
| `command-injection.ql` | 0 detections | Original query has logic issues |
| `simple-working-cmd.ql` | 2 detections | Working command injection detection |
| `list-source-files.ql` | 3 files | Lists our source files |
| `vulnerability-by-file.ql` | 6-8 detections | Shows which files contain patterns |

## 🎯 Quick Test Command

**Test just the main security queries:**
```bash
mkdir -p results
codeql database analyze node-db queries/custom-vulnerability.ql --format=csv --output=results/custom.csv
codeql database analyze node-db queries/refined-custom-vulnerability.ql --format=csv --output=results/refined.csv
codeql database analyze node-db queries/simple-working-cmd.ql --format=csv --output=results/cmd-injection.csv
echo "Results:"
wc -l results/*.csv
```

## 🔧 Troubleshooting

**If a query fails:**
1. Check the query syntax and imports
2. Verify the database exists: `ls -la node-db/`
3. Try running with verbose output: `codeql ... --verbose`
4. Check if it's a problem vs table query (use appropriate command)

**Common Issues:**
- **Empty results**: Query logic doesn't match code patterns
- **Compilation errors**: Syntax issues in the query
- **Wrong command**: Using `database analyze` for table queries or vice versa
