"Refined Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values while avoiding false positives","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","6","17","6","40"
"Refined Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values while avoiding false positives","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","7","21","7","44"
"Refined Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values while avoiding false positives","error","Hardcoded sensitive value in variable declaration: 'I am hacker's paradise'","/test-vulnerabilities.js","10","21","10","44"
"Refined Custom Vulnerability Detection - Hardcoded Sensitive Values","Detects hardcoded sensitive values while avoiding false positives","error","Hardcoded sensitive value in variable declaration: 'I AM HACKER'S PARADISE'","/test-vulnerabilities.js","11","20","11","43"
