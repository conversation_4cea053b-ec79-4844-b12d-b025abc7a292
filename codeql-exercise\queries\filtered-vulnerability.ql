/**
 * @name Filtered Vulnerability Detection
 * @description Detects vulnerabilities but excludes test-runner.js
 * @kind problem
 * @problem.severity error
 * @security-severity 8.0
 * @precision high
 * @id js/filtered-vulnerability
 * @tags security
 */

import javascript

predicate isSensitiveString(StringLiteral str) {
  str.getValue().toLowerCase().matches("%hacker%paradise%") or
  str.getValue().toLowerCase().matches("%i am hacker%") or
  str.getValue() = "I am hacker's paradise"
}

from StringLiteral str, string message
where 
  isSensitiveString(str) and
  // Exclude specific files from analysis
  not str.getFile().getBaseName() = "test-runner.js" and
  not str.getFile().getBaseName() = "compare-results.js" and
  // Exclude false positive contexts
  not str.getValue().matches("%MASKED%") and
  (
    exists(VariableDeclarator decl |
      decl.getInit() = str and
      message = "Hardcoded sensitive value in variable declaration: '" + str.getValue() + "'"
    ) or
    exists(AssignExpr assign |
      assign.getRhs() = str and
      message = "Hardcoded sensitive value in assignment: '" + str.getValue() + "'"
    ) or
    exists(CallExpr call |
      call.getAnArgument() = str and
      message = "Sensitive value exposed in function call: '" + str.getValue() + "'"
    ) or
    (not exists(VariableDeclarator decl | decl.getInit() = str) and
     not exists(AssignExpr assign | assign.getRhs() = str) and
     not exists(CallExpr call | call.getAnArgument() = str) and
     message = "Hardcoded sensitive value found: '" + str.getValue() + "'")
  )
select str, message + " (File: " + str.getFile().getBaseName() + ", Line: " + str.getLocation().getStartLine() + ")"
