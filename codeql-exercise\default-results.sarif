{"$schema": "https://json.schemastore.org/sarif-2.1.0.json", "version": "2.1.0", "runs": [{"tool": {"driver": {"name": "CodeQL", "organization": "GitHub", "semanticVersion": "2.21.3", "notifications": [{"id": "js/diagnostics/successfully-extracted-files", "name": "js/diagnostics/successfully-extracted-files", "shortDescription": {"text": "Extracted files"}, "fullDescription": {"text": "Lists all files in the source code directory that were extracted."}, "defaultConfiguration": {"enabled": true}, "properties": {"tags": ["successfully-extracted-files"], "description": "Lists all files in the source code directory that were extracted.", "id": "js/diagnostics/successfully-extracted-files", "kind": "diagnostic", "name": "Extracted files"}}, {"id": "js/diagnostics/extraction-errors", "name": "js/diagnostics/extraction-errors", "shortDescription": {"text": "Extraction errors"}, "fullDescription": {"text": "List all extraction errors for files in the source code directory."}, "defaultConfiguration": {"enabled": true}, "properties": {"description": "List all extraction errors for files in the source code directory.", "id": "js/diagnostics/extraction-errors", "kind": "diagnostic", "name": "Extraction errors"}}, {"id": "js/baseline/expected-extracted-files", "name": "js/baseline/expected-extracted-files", "shortDescription": {"text": "Expected extracted files"}, "fullDescription": {"text": "Files appearing in the source archive that are expected to be extracted."}, "defaultConfiguration": {"enabled": true}, "properties": {"tags": ["expected-extracted-files", "telemetry"]}}], "rules": [{"id": "js/angular/dependency-injection-mismatch", "name": "js/angular/dependency-injection-mismatch", "shortDescription": {"text": "Dependency mismatch"}, "fullDescription": {"text": "If the injected dependencies of a function go out of sync with its parameters, the function will become difficult to understand and maintain."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "maintainability", "frameworks/angularjs"], "description": "If the injected dependencies of a function go out of sync\n              with its parameters, the function will become difficult to\n              understand and maintain.", "id": "js/angular/dependency-injection-mismatch", "kind": "problem", "name": "Dependency mismatch", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/angular/disabling-sce", "name": "js/angular/disabling-sce", "shortDescription": {"text": "Disabling SCE"}, "fullDescription": {"text": "Disabling strict contextual escaping (SCE) can cause security vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "maintainability", "frameworks/angularjs", "external/cwe/cwe-116"], "description": "Disabling strict contextual escaping (SCE) can cause security vulnerabilities.", "id": "js/angular/disabling-sce", "kind": "problem", "name": "Disabling SCE", "precision": "very-high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/angular/double-compilation", "name": "js/angular/double-compilation", "shortDescription": {"text": "Double compilation"}, "fullDescription": {"text": "Recompiling an already compiled part of the DOM can lead to unexpected behavior of directives, performance problems, and memory leaks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "frameworks/angularjs", "security", "external/cwe/cwe-1176"], "description": "Recompiling an already compiled part of the DOM can lead to\n              unexpected behavior of directives, performance problems, and memory leaks.", "id": "js/angular/double-compilation", "kind": "problem", "name": "Double compilation", "precision": "very-high", "problem.severity": "warning", "security-severity": "8.8"}}, {"id": "js/angular/duplicate-dependency", "name": "js/angular/duplicate-dependency", "shortDescription": {"text": "Duplicate dependency"}, "fullDescription": {"text": "Repeated dependency names are redundant for AngularJS dependency injection."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "frameworks/angularjs"], "description": "Repeated dependency names are redundant for AngularJS dependency injection.", "id": "js/angular/duplicate-dependency", "kind": "problem", "name": "Duplicate dependency", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/angular/incompatible-service", "name": "js/angular/incompatible-service", "shortDescription": {"text": "Incompatible dependency injection"}, "fullDescription": {"text": "Dependency-injecting a service of the wrong kind causes an error at runtime."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "frameworks/angularjs"], "description": "Dependency-injecting a service of the wrong kind causes an error at runtime.", "id": "js/angular/incompatible-service", "kind": "problem", "name": "Incompatible dependency injection", "precision": "high", "problem.severity": "error"}}, {"id": "js/angular/insecure-url-whitelist", "name": "js/angular/insecure-url-whitelist", "shortDescription": {"text": "Insecure URL whitelist"}, "fullDescription": {"text": "URL whitelists that are too permissive can cause security vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "frameworks/angularjs", "external/cwe/cwe-183", "external/cwe/cwe-625"], "description": "URL whitelists that are too permissive can cause security vulnerabilities.", "id": "js/angular/insecure-url-whitelist", "kind": "problem", "name": "Insecure URL whitelist", "precision": "very-high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/angular/missing-explicit-injection", "name": "js/angular/missing-explicit-injection", "shortDescription": {"text": "Missing explicit dependency injection"}, "fullDescription": {"text": "Functions without explicit dependency injections will not work when their parameter names are minified."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "maintainability", "frameworks/angularjs"], "description": "Functions without explicit dependency injections\n                will not work when their parameter names are minified.", "id": "js/angular/missing-explicit-injection", "kind": "problem", "name": "Missing explicit dependency injection", "precision": "high", "problem.severity": "warning"}}, {"id": "js/angular/repeated-dependency-injection", "name": "js/angular/repeated-dependency-injection", "shortDescription": {"text": "Repeated dependency injection"}, "fullDescription": {"text": "Specifying dependency injections of an AngularJS component multiple times overrides earlier specifications."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "frameworks/angularjs"], "description": "Specifying dependency injections of an AngularJS component multiple times overrides earlier specifications.", "id": "js/angular/repeated-dependency-injection", "kind": "problem", "name": "Repeated dependency injection", "precision": "high", "problem.severity": "warning"}}, {"id": "js/angular/expression-in-url-attribute", "name": "js/angular/expression-in-url-attribute", "shortDescription": {"text": "Use of AngularJS markup in URL-valued attribute"}, "fullDescription": {"text": "Using AngularJS markup in an HTML attribute that references a URL (such as 'href' or 'src') may cause the browser to send a request with an invalid URL."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "frameworks/angularjs"], "description": "Using AngularJS markup in an HTML attribute that references a URL\n              (such as 'href' or 'src') may cause the browser to send a request\n              with an invalid URL.", "id": "js/angular/expression-in-url-attribute", "kind": "problem", "name": "Use of AngularJS markup in URL-valued attribute", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/arguments-redefinition", "name": "js/arguments-redefinition", "shortDescription": {"text": "Arguments redefined"}, "fullDescription": {"text": "The special 'arguments' variable can be redefined, but this should be avoided since it makes code hard to read and maintain and may prevent compiler optimizations."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["efficiency", "maintainability"], "description": "The special 'arguments' variable can be redefined, but this should be avoided\n              since it makes code hard to read and maintain and may prevent compiler\n              optimizations.", "id": "js/arguments-redefinition", "kind": "problem", "name": "Arguments redefined", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/assignment-to-constant", "name": "js/assignment-to-constant", "shortDescription": {"text": "Assignment to constant"}, "fullDescription": {"text": "Assigning to a variable that is declared 'const' has either no effect or leads to a runtime error, depending on the platform."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness"], "description": "Assigning to a variable that is declared 'const' has either no effect or leads to a\n              runtime error, depending on the platform.", "id": "js/assignment-to-constant", "kind": "problem", "name": "Assignment to constant", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/variable-initialization-conflict", "name": "js/variable-initialization-conflict", "shortDescription": {"text": "Conflicting variable initialization"}, "fullDescription": {"text": "If a variable is declared and initialized twice inside the same variable declaration statement, the second initialization immediately overwrites the first one."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-563"], "description": "If a variable is declared and initialized twice inside the same variable declaration\n              statement, the second initialization immediately overwrites the first one.", "id": "js/variable-initialization-conflict", "kind": "problem", "name": "Conflicting variable initialization", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/function-declaration-conflict", "name": "js/function-declaration-conflict", "shortDescription": {"text": "Conflicting function declarations"}, "fullDescription": {"text": "If two functions with the same name are declared in the same scope, one of the declarations overrides the other without warning. This makes the code hard to read and maintain, and may even lead to platform-dependent behavior."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-563"], "description": "If two functions with the same name are declared in the same scope, one of the declarations\n              overrides the other without warning. This makes the code hard to read and maintain, and\n              may even lead to platform-dependent behavior.", "id": "js/function-declaration-conflict", "kind": "problem", "name": "Conflicting function declarations", "precision": "high", "problem.severity": "error"}}, {"id": "js/useless-assignment-to-local", "name": "js/useless-assignment-to-local", "shortDescription": {"text": "Useless assignment to local variable"}, "fullDescription": {"text": "An assignment to a local variable that is not used later on, or whose value is always overwritten, has no effect."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "external/cwe/cwe-563"], "description": "An assignment to a local variable that is not used later on, or whose value is always\n              overwritten, has no effect.", "id": "js/useless-assignment-to-local", "kind": "problem", "name": "Useless assignment to local variable", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/useless-assignment-to-property", "name": "js/useless-assignment-to-property", "shortDescription": {"text": "Useless assignment to property"}, "fullDescription": {"text": "An assignment to a property whose value is always overwritten has no effect."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability"], "description": "An assignment to a property whose value is always overwritten has no effect.", "id": "js/useless-assignment-to-property", "kind": "problem", "name": "Useless assignment to property", "precision": "high", "problem.severity": "warning"}}, {"id": "js/use-before-declaration", "name": "js/use-before-declaration", "shortDescription": {"text": "Variable not declared before use"}, "fullDescription": {"text": "Variables should be declared before their first use."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "readability"], "description": "Variables should be declared before their first use.", "id": "js/use-before-declaration", "kind": "problem", "name": "Variable not declared before use", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/nested-function-reference-in-default-parameter", "name": "js/nested-function-reference-in-default-parameter", "shortDescription": {"text": "Default parameter references nested function"}, "fullDescription": {"text": "If a default parameter value references a function that is nested inside the function to which the parameter belongs, a runtime error will occur, since the function is not yet defined at the point where it is referenced."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness"], "description": "If a default parameter value references a function that is nested inside the\n              function to which the parameter belongs, a runtime error will occur, since\n              the function is not yet defined at the point where it is referenced.", "id": "js/nested-function-reference-in-default-parameter", "kind": "problem", "name": "Default parameter references nested function", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/duplicate-variable-declaration", "name": "js/duplicate-variable-declaration", "shortDescription": {"text": "Duplicate variable declaration"}, "fullDescription": {"text": "A variable declaration statement that declares the same variable twice is confusing and hard to maintain."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["maintainability"], "description": "A variable declaration statement that declares the same variable twice is\n              confusing and hard to maintain.", "id": "js/duplicate-variable-declaration", "kind": "problem", "name": "Duplicate variable declaration", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/ineffective-parameter-type", "name": "js/ineffective-parameter-type", "shortDescription": {"text": "Ineffective parameter type"}, "fullDescription": {"text": "Omitting the name of a parameter causes its type annotation to be parsed as the name."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "typescript", "quality"], "description": "Omitting the name of a parameter causes its type annotation to be parsed as the name.", "id": "js/ineffective-parameter-type", "kind": "problem", "name": "Ineffective parameter type", "precision": "high", "problem.severity": "warning"}}, {"id": "js/missing-this-qualifier", "name": "js/missing-this-qualifier", "shortDescription": {"text": "Missing 'this' qualifier"}, "fullDescription": {"text": "Referencing an undeclared global variable in a class that has a member of the same name is confusing and may indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["maintainability", "correctness", "methods"], "description": "Referencing an undeclared global variable in a class that has a member of the same name is confusing and may indicate a bug.", "id": "js/missing-this-qualifier", "kind": "problem", "name": "Missing 'this' qualifier", "precision": "high", "problem.severity": "error"}}, {"id": "js/missing-variable-declaration", "name": "js/missing-variable-declaration", "shortDescription": {"text": "Missing variable declaration"}, "fullDescription": {"text": "If a variable is not declared as a local variable, it becomes a global variable by default, which may be unintentional and could lead to unexpected behavior."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "maintainability"], "description": "If a variable is not declared as a local variable, it becomes a global variable\n              by default, which may be unintentional and could lead to unexpected behavior.", "id": "js/missing-variable-declaration", "kind": "problem", "name": "Missing variable declaration", "precision": "high", "problem.severity": "warning"}}, {"id": "js/mixed-static-instance-this-access", "name": "js/mixed-static-instance-this-access", "shortDescription": {"text": "Wrong use of 'this' for static method"}, "fullDescription": {"text": "A reference to a static method from within an instance method needs to be qualified with the class name, not `this`."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "methods"], "description": "A reference to a static method from within an instance method needs to be qualified with the class name, not `this`.", "id": "js/mixed-static-instance-this-access", "kind": "problem", "name": "Wrong use of 'this' for static method", "precision": "high", "problem.severity": "error"}}, {"id": "js/suspicious-method-name-declaration", "name": "js/suspicious-method-name-declaration", "shortDescription": {"text": "Suspicious method name declaration"}, "fullDescription": {"text": "A method declaration with a name that is a special keyword in another context is suspicious."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "typescript", "methods"], "description": "A method declaration with a name that is a special keyword in another\n              context is suspicious.", "id": "js/suspicious-method-name-declaration", "kind": "problem", "name": "Suspicious method name declaration", "precision": "high", "problem.severity": "warning"}}, {"id": "js/variable-use-in-temporal-dead-zone", "name": "js/variable-use-in-temporal-dead-zone", "shortDescription": {"text": "Access to let-bound variable in temporal dead zone"}, "fullDescription": {"text": "Accessing a let-bound variable before its declaration will lead to a runtime error on ECMAScript 2015-compatible platforms."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["portability", "correctness"], "description": "Accessing a let-bound variable before its declaration will lead to a runtime\n              error on ECMAScript 2015-compatible platforms.", "id": "js/variable-use-in-temporal-dead-zone", "kind": "problem", "name": "Access to let-bound variable in temporal dead zone", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/duplicate-parameter-name", "name": "js/duplicate-parameter-name", "shortDescription": {"text": "Duplicate parameter names"}, "fullDescription": {"text": "If a function has two parameters with the same name, the second parameter shadows the first one, which makes the code hard to understand and error-prone."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness"], "description": "If a function has two parameters with the same name, the second parameter\n              shadows the first one, which makes the code hard to understand and error-prone.", "id": "js/duplicate-parameter-name", "kind": "problem", "name": "Duplicate parameter names", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/overwritten-property", "name": "js/overwritten-property", "shortDescription": {"text": "Overwritten property"}, "fullDescription": {"text": "If an object literal has two properties with the same name, the second property overwrites the first one, which makes the code hard to understand and error-prone."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-563"], "description": "If an object literal has two properties with the same name,\n              the second property overwrites the first one,\n              which makes the code hard to understand and error-prone.", "id": "js/overwritten-property", "kind": "problem", "name": "Overwritten property", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/unreachable-method-overloads", "name": "js/unreachable-method-overloads", "shortDescription": {"text": "Unreachable method overloads"}, "fullDescription": {"text": "Having multiple overloads with the same parameter types in TypeScript makes all overloads except the first one unreachable, as the compiler always resolves calls to the textually first matching overload."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "typescript"], "description": "Having multiple overloads with the same parameter types in TypeScript\n              makes all overloads except the first one unreachable, as the compiler\n              always resolves calls to the textually first matching overload.", "id": "js/unreachable-method-overloads", "kind": "problem", "name": "Unreachable method overloads", "precision": "high", "problem.severity": "warning"}}, {"id": "js/unused-local-variable", "name": "js/unused-local-variable", "shortDescription": {"text": "Unused variable, import, function or class"}, "fullDescription": {"text": "Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["maintainability"], "description": "Unused variables, imports, functions or classes may be a symptom of a bug\n              and should be examined carefully.", "id": "js/unused-local-variable", "kind": "problem", "name": "Unused variable, import, function or class", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/duplicate-html-attribute", "name": "js/duplicate-html-attribute", "shortDescription": {"text": "Duplicate HTML element attributes"}, "fullDescription": {"text": "Specifying the same attribute twice on the same HTML element is redundant and may indicate a copy-paste mistake."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "readability"], "description": "Specifying the same attribute twice on the same HTML element is\n              redundant and may indicate a copy-paste mistake.", "id": "js/duplicate-html-attribute", "kind": "problem", "name": "Duplicate HTML element attributes", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/malformed-html-id", "name": "js/malformed-html-id", "shortDescription": {"text": "Malformed id attribute"}, "fullDescription": {"text": "If the id of an HTML attribute is malformed, its interpretation may be browser-dependent."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "external/cwe/cwe-758"], "description": "If the id of an HTML attribute is malformed, its\n              interpretation may be browser-dependent.", "id": "js/malformed-html-id", "kind": "problem", "name": "Malformed id attribute", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/eval-like-call", "name": "js/eval-like-call", "shortDescription": {"text": "Call to eval-like DOM function"}, "fullDescription": {"text": "DOM functions that act like 'eval' and execute strings as code are dangerous and impede program analysis and understanding. Consequently, they should not be used."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["maintainability", "external/cwe/cwe-676"], "description": "DOM functions that act like 'eval' and execute strings as code are dangerous and impede\n              program analysis and understanding. Consequently, they should not be used.", "id": "js/eval-like-call", "kind": "problem", "name": "Call to eval-like DOM function", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/enabling-electron-insecure-content", "name": "js/enabling-electron-insecure-content", "shortDescription": {"text": "Enabling Electron allowRunningInsecureContent"}, "fullDescription": {"text": "Enabling allowRunningInsecureContent can allow remote code execution."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "frameworks/electron", "external/cwe/cwe-494"], "description": "Enabling allowRunningInsecureContent can allow remote code execution.", "id": "js/enabling-electron-insecure-content", "kind": "problem", "name": "Enabling Electron allowRunningInsecureContent", "precision": "very-high", "problem.severity": "error", "security-severity": "8.8"}}, {"id": "js/disabling-electron-websecurity", "name": "js/disabling-electron-websecurity", "shortDescription": {"text": "Disabling Electron webSecurity"}, "fullDescription": {"text": "Disabling webSecurity can cause critical security vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "frameworks/electron", "external/cwe/cwe-079"], "description": "Disabling webSecurity can cause critical security vulnerabilities.", "id": "js/disabling-electron-websecurity", "kind": "problem", "name": "Disabling Electron webSecurity", "precision": "very-high", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/comparison-with-nan", "name": "js/comparison-with-nan", "shortDescription": {"text": "Comparison with NaN"}, "fullDescription": {"text": "Arithmetic comparisons with NaN are useless: nothing is considered to be equal to NaN, not even NaN itself, and similarly nothing is considered greater or less than NaN."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-570", "external/cwe/cwe-571"], "description": "Arithmetic comparisons with NaN are useless: nothing is considered to be equal to NaN, not even NaN itself,\n              and similarly nothing is considered greater or less than NaN.", "id": "js/comparison-with-nan", "kind": "problem", "name": "Comparison with NaN", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/duplicate-condition", "name": "js/duplicate-condition", "shortDescription": {"text": "Duplicate 'if' condition"}, "fullDescription": {"text": "If two conditions in an 'if'-'else if' chain are identical, the second condition will never hold."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "external/cwe/cwe-561"], "description": "If two conditions in an 'if'-'else if' chain are identical, the\n              second condition will never hold.", "id": "js/duplicate-condition", "kind": "problem", "name": "Duplicate 'if' condition", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/duplicate-property", "name": "js/duplicate-property", "shortDescription": {"text": "Duplicate property"}, "fullDescription": {"text": "Listing the same property twice in one object literal is redundant and may indicate a copy-paste mistake."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "external/cwe/cwe-563"], "description": "Listing the same property twice in one object literal is\n              redundant and may indicate a copy-paste mistake.", "id": "js/duplicate-property", "kind": "problem", "name": "Duplicate property", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/duplicate-switch-case", "name": "js/duplicate-switch-case", "shortDescription": {"text": "Duplicate switch case"}, "fullDescription": {"text": "If two cases in a switch statement have the same label, the second case will never be executed."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "external/cwe/cwe-561"], "description": "If two cases in a switch statement have the same label, the second case\n              will never be executed.", "id": "js/duplicate-switch-case", "kind": "problem", "name": "Duplicate switch case", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/useless-expression", "name": "js/useless-expression", "shortDescription": {"text": "Expression has no effect"}, "fullDescription": {"text": "An expression that has no effect and is used in a void context is most likely redundant and may indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["quality", "maintainability", "correctness", "external/cwe/cwe-480", "external/cwe/cwe-561", "useless-code"], "description": "An expression that has no effect and is used in a void context is most\n              likely redundant and may indicate a bug.", "id": "js/useless-expression", "kind": "problem", "name": "Expression has no effect", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/comparison-between-incompatible-types", "name": "js/comparison-between-incompatible-types", "shortDescription": {"text": "Comparison between inconvertible types"}, "fullDescription": {"text": "An equality comparison between two values that cannot be meaningfully converted to the same type will always yield 'false', and an inequality comparison will always yield 'true'."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-570", "external/cwe/cwe-571"], "description": "An equality comparison between two values that cannot be meaningfully converted to\n              the same type will always yield 'false', and an inequality comparison will always\n              yield 'true'.", "id": "js/comparison-between-incompatible-types", "kind": "problem", "name": "Comparison between inconvertible types", "precision": "high", "problem.severity": "warning"}}, {"id": "js/implicit-operand-conversion", "name": "js/implicit-operand-conversion", "shortDescription": {"text": "Implicit operand conversion"}, "fullDescription": {"text": "Relying on implicit conversion of operands is error-prone and makes code hard to read."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "readability", "external/cwe/cwe-704"], "description": "Relying on implicit conversion of operands is error-prone and makes code\n              hard to read.", "id": "js/implicit-operand-conversion", "kind": "problem", "name": "Implicit operand conversion", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/missing-await", "name": "js/missing-await", "shortDescription": {"text": "Missing await"}, "fullDescription": {"text": "Using a promise without awaiting its result can lead to unexpected behavior."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "quality"], "description": "Using a promise without awaiting its result can lead to unexpected behavior.", "id": "js/missing-await", "kind": "problem", "name": "Missing await", "precision": "high", "problem.severity": "warning"}}, {"id": "js/missing-dot-length-in-comparison", "name": "js/missing-dot-length-in-comparison", "shortDescription": {"text": "Missing '.length' in comparison"}, "fullDescription": {"text": "Two variables are being compared using a relational operator, but one is also used to index into the other, suggesting a \".length\" is missing from the comparison."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "Two variables are being compared using a relational operator, but one is also used\n             to index into the other, suggesting a \".length\" is missing from the comparison.", "id": "js/missing-dot-length-in-comparison", "kind": "problem", "name": "Missing '.length' in comparison", "precision": "high", "problem.severity": "warning"}}, {"id": "js/missing-space-in-concatenation", "name": "js/missing-space-in-concatenation", "shortDescription": {"text": "Missing space in string concatenation"}, "fullDescription": {"text": "Joining constant strings into a longer string where two words are concatenated without a separating space usually indicates a text error."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["readability"], "description": "Joining constant strings into a longer string where\n              two words are concatenated without a separating space\n              usually indicates a text error.", "id": "js/missing-space-in-concatenation", "kind": "problem", "name": "Missing space in string concatenation", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/misspelled-variable-name", "name": "js/misspelled-variable-name", "shortDescription": {"text": "Misspelled variable name"}, "fullDescription": {"text": "Misspelling a variable name implicitly introduces a global variable, which may not lead to a runtime error, but is likely to give wrong results."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "readability", "correctness"], "description": "Misspelling a variable name implicitly introduces a global\n              variable, which may not lead to a runtime error, but is\n              likely to give wrong results.", "id": "js/misspelled-variable-name", "kind": "problem", "name": "Misspelled variable name", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/redundant-operation", "name": "js/redundant-operation", "shortDescription": {"text": "Identical operands"}, "fullDescription": {"text": "Passing identical, or seemingly identical, operands to an operator such as subtraction or conjunction may indicate a typo; even if it is intentional, it makes the code hard to read."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-480", "external/cwe/cwe-561"], "description": "Passing identical, or seemingly identical, operands to an\n              operator such as subtraction or conjunction may indicate a typo;\n              even if it is intentional, it makes the code hard to read.", "id": "js/redundant-operation", "kind": "problem", "name": "Identical operands", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/redundant-assignment", "name": "js/redundant-assignment", "shortDescription": {"text": "Self assignment"}, "fullDescription": {"text": "Assigning a variable to itself has no effect."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-480", "external/cwe/cwe-561"], "description": "Assigning a variable to itself has no effect.", "id": "js/redundant-assignment", "kind": "problem", "name": "Self assignment", "precision": "high", "problem.severity": "warning"}}, {"id": "js/shift-out-of-range", "name": "js/shift-out-of-range", "shortDescription": {"text": "Shift out of range"}, "fullDescription": {"text": "The integer shift operators '<<', '>>' and '>>>' only take the five least significant bits of their right operand into account. Thus, it is not possible to shift an integer by more than 31 bits."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "external/cwe/cwe-197"], "description": "The integer shift operators '<<', '>>' and '>>>' only take the five least significant bits of their\n              right operand into account. Thus, it is not possible to shift an integer by more than 31 bits.", "id": "js/shift-out-of-range", "kind": "problem", "name": "Shift out of range", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/string-instead-of-regex", "name": "js/string-instead-of-regex", "shortDescription": {"text": "String instead of regular expression"}, "fullDescription": {"text": "Calling 'String.prototype.replace' or 'String.prototype.split' with a string argument that looks like a regular expression is probably a mistake because the called function will not convert the string into a regular expression."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "Calling 'String.prototype.replace' or 'String.prototype.split' with a string argument that looks like a regular expression is probably a mistake because the called function will not convert the string into a regular expression.", "id": "js/string-instead-of-regex", "kind": "problem", "name": "String instead of regular expression", "precision": "high", "problem.severity": "warning"}}, {"id": "js/call-to-non-callable", "name": "js/call-to-non-callable", "shortDescription": {"text": "Invocation of non-function"}, "fullDescription": {"text": "Trying to invoke a value that is not a function will result in a runtime exception."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "external/cwe/cwe-476"], "description": "Trying to invoke a value that is not a function will result\n              in a runtime exception.", "id": "js/call-to-non-callable", "kind": "problem", "name": "Invocation of non-function", "precision": "high", "problem.severity": "error"}}, {"id": "js/property-access-on-non-object", "name": "js/property-access-on-non-object", "shortDescription": {"text": "Property access on null or undefined"}, "fullDescription": {"text": "Trying to access a property of \"null\" or \"undefined\" will result in a runtime exception."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "external/cwe/cwe-476"], "description": "Trying to access a property of \"null\" or \"undefined\" will result\n              in a runtime exception.", "id": "js/property-access-on-non-object", "kind": "problem", "name": "Property access on null or undefined", "precision": "high", "problem.severity": "error"}}, {"id": "js/unbound-event-handler-receiver", "name": "js/unbound-event-handler-receiver", "shortDescription": {"text": "Unbound event handler receiver"}, "fullDescription": {"text": "Invoking an event handler method as a function can cause a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness"], "description": "Invoking an event handler method as a function can cause a runtime error.", "id": "js/unbound-event-handler-receiver", "kind": "problem", "name": "Unbound event handler receiver", "precision": "high", "problem.severity": "error"}}, {"id": "js/unclear-operator-precedence", "name": "js/unclear-operator-precedence", "shortDescription": {"text": "Unclear precedence of nested operators"}, "fullDescription": {"text": "Nested expressions involving binary bitwise operators and comparisons are easy to misunderstand without additional disambiguating parentheses or whitespace."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["maintainability", "correctness", "statistical", "non-attributable", "external/cwe/cwe-783"], "description": "Nested expressions involving binary bitwise operators and comparisons are easy\n              to misunderstand without additional disambiguating parentheses or whitespace.", "id": "js/unclear-operator-precedence", "kind": "problem", "name": "Unclear precedence of nested operators", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/unknown-directive", "name": "js/unknown-directive", "shortDescription": {"text": "Unknown directive"}, "fullDescription": {"text": "An unknown directive has no effect and may indicate a misspelling."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "An unknown directive has no effect and may indicate a misspelling.", "id": "js/unknown-directive", "kind": "problem", "name": "Unknown directive", "precision": "high", "problem.severity": "warning"}}, {"id": "js/unneeded-defensive-code", "name": "js/unneeded-defensive-code", "shortDescription": {"text": "Unneeded defensive code"}, "fullDescription": {"text": "Defensive code that guards against a situation that never happens is not needed."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["correctness", "external/cwe/cwe-570", "external/cwe/cwe-571"], "description": "Defensive code that guards against a situation that never happens is not needed.", "id": "js/unneeded-defensive-code", "kind": "problem", "name": "Unneeded defensive code", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/whitespace-contradicts-precedence", "name": "js/whitespace-contradicts-precedence", "shortDescription": {"text": "Whitespace contradicts operator precedence"}, "fullDescription": {"text": "Nested expressions where the formatting contradicts the grouping enforced by operator precedence are difficult to read and may even indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "statistical", "non-attributable", "external/cwe/cwe-783"], "description": "Nested expressions where the formatting contradicts the grouping enforced by operator precedence\n              are difficult to read and may even indicate a bug.", "id": "js/whitespace-contradicts-precedence", "kind": "problem", "name": "Whitespace contradicts operator precedence", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/useless-type-test", "name": "js/useless-type-test", "shortDescription": {"text": "Useless type test"}, "fullDescription": {"text": "Comparing the result of a typeof test against a string other than 'undefined', 'boolean', 'number', 'string', 'object', 'function' or 'symbol' is useless, since this comparison can never succeed."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["maintainability", "correctness", "language-features", "external/cwe/cwe-570", "external/cwe/cwe-571"], "description": "Comparing the result of a typeof test against a string other than 'undefined',\n              'boolean', 'number', 'string', 'object', 'function' or 'symbol' is useless, since\n              this comparison can never succeed.", "id": "js/useless-type-test", "kind": "problem", "name": "Useless type test", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/conditional-comment", "name": "js/conditional-comment", "shortDescription": {"text": "Conditional comments"}, "fullDescription": {"text": "Conditional comments are an IE-specific feature and not portable."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["portability", "maintainability", "language-features", "external/cwe/cwe-758"], "description": "Conditional comments are an IE-specific feature and not portable.", "id": "js/conditional-comment", "kind": "problem", "name": "Conditional comments", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/deletion-of-non-property", "name": "js/deletion-of-non-property", "shortDescription": {"text": "Deleting non-property"}, "fullDescription": {"text": "The operand of the 'delete' operator should always be a property accessor."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "maintainability", "language-features", "external/cwe/cwe-480"], "description": "The operand of the 'delete' operator should always be a property accessor.", "id": "js/deletion-of-non-property", "kind": "problem", "name": "Deleting non-property", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/non-standard-language-feature", "name": "js/non-standard-language-feature", "shortDescription": {"text": "Use of platform-specific language features"}, "fullDescription": {"text": "Non-standard language features such as expression closures or let expressions make it harder to reuse code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["portability", "maintainability", "language-features", "external/cwe/cwe-758"], "description": "Non-standard language features such as expression closures or let expressions\n              make it harder to reuse code.", "id": "js/non-standard-language-feature", "kind": "problem", "name": "Use of platform-specific language features", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/for-in-comprehension", "name": "js/for-in-comprehension", "shortDescription": {"text": "Use of for-in comprehension blocks"}, "fullDescription": {"text": "'for'-'in' comprehension blocks are a Mozilla-specific language extension that is no longer supported."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["portability", "maintainability", "language-features", "external/cwe/cwe-758"], "description": "'for'-'in' comprehension blocks are a Mozilla-specific language extension\n              that is no longer supported.", "id": "js/for-in-comprehension", "kind": "problem", "name": "Use of for-in comprehension blocks", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/illegal-invocation", "name": "js/illegal-invocation", "shortDescription": {"text": "Illegal invocation"}, "fullDescription": {"text": "Attempting to invoke a method or an arrow function using 'new', or invoking a constructor as a function, will cause a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "language-features"], "description": "Attempting to invoke a method or an arrow function using 'new',\n              or invoking a constructor as a function, will cause a runtime\n              error.", "id": "js/illegal-invocation", "kind": "problem", "name": "Illegal invocation", "precision": "high", "problem.severity": "error"}}, {"id": "js/inconsistent-use-of-new", "name": "js/inconsistent-use-of-new", "shortDescription": {"text": "Inconsistent use of 'new'"}, "fullDescription": {"text": "If a function is intended to be a constructor, it should always be invoked with 'new'. Otherwise, it should always be invoked as a normal function, that is, without 'new'."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "language-features"], "description": "If a function is intended to be a constructor, it should always\n              be invoked with 'new'. Otherwise, it should always be invoked\n              as a normal function, that is, without 'new'.", "id": "js/inconsistent-use-of-new", "kind": "problem", "name": "Inconsistent use of 'new'", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/invalid-prototype-value", "name": "js/invalid-prototype-value", "shortDescription": {"text": "Invalid prototype value"}, "fullDescription": {"text": "An attempt to use a value that is not an object or 'null' as a prototype will either be ignored or result in a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "language-features", "external/cwe/cwe-704"], "description": "An attempt to use a value that is not an object or 'null' as a\n              prototype will either be ignored or result in a runtime error.", "id": "js/invalid-prototype-value", "kind": "problem", "name": "Invalid prototype value", "precision": "high", "problem.severity": "error"}}, {"id": "js/index-out-of-bounds", "name": "js/index-out-of-bounds", "shortDescription": {"text": "Off-by-one comparison against length"}, "fullDescription": {"text": "An array index is compared to be less than or equal to the 'length' property, and then used in an indexing operation that could be out of bounds."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "logic", "external/cwe/cwe-193"], "description": "An array index is compared to be less than or equal to the 'length' property,\n              and then used in an indexing operation that could be out of bounds.", "id": "js/index-out-of-bounds", "kind": "problem", "name": "Off-by-one comparison against length", "precision": "high", "problem.severity": "warning"}}, {"id": "js/non-linear-pattern", "name": "js/non-linear-pattern", "shortDescription": {"text": "Non-linear pattern"}, "fullDescription": {"text": "If the same pattern variable appears twice in an array or object pattern, the second binding will silently overwrite the first binding, which is probably unintentional."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "language-features"], "description": "If the same pattern variable appears twice in an array or object pattern,\n              the second binding will silently overwrite the first binding, which is probably\n              unintentional.", "id": "js/non-linear-pattern", "kind": "problem", "name": "Non-linear pattern", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/property-assignment-on-primitive", "name": "js/property-assignment-on-primitive", "shortDescription": {"text": "Assignment to property of primitive value"}, "fullDescription": {"text": "Assigning to a property of a primitive value has no effect and may trigger a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "language-features", "external/cwe/cwe-704"], "description": "Assigning to a property of a primitive value has no effect\n              and may trigger a runtime error.", "id": "js/property-assignment-on-primitive", "kind": "problem", "name": "Assignment to property of primitive value", "precision": "high", "problem.severity": "error"}}, {"id": "js/automatic-semicolon-insertion", "name": "js/automatic-semicolon-insertion", "shortDescription": {"text": "Semicolon insertion"}, "fullDescription": {"text": "Code that uses automatic semicolon insertion inconsistently is hard to read and maintain."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["maintainability", "language-features", "statistical", "non-attributable"], "description": "Code that uses automatic semicolon insertion inconsistently is hard to read and maintain.", "id": "js/automatic-semicolon-insertion", "kind": "problem", "name": "Semicolon insertion", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/setter-return", "name": "js/setter-return", "shortDescription": {"text": "Useless return in setter"}, "fullDescription": {"text": "Returning a value from a setter function is useless, since it will always be ignored."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "language-features"], "description": "Returning a value from a setter function is useless, since it will\n              always be ignored.", "id": "js/setter-return", "kind": "problem", "name": "Useless return in setter", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/superfluous-trailing-arguments", "name": "js/superfluous-trailing-arguments", "shortDescription": {"text": "Superfluous trailing arguments"}, "fullDescription": {"text": "A function is invoked with extra trailing arguments that are ignored."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "language-features", "quality", "external/cwe/cwe-685"], "description": "A function is invoked with extra trailing arguments that are ignored.", "id": "js/superfluous-trailing-arguments", "kind": "problem", "name": "Superfluous trailing arguments", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/strict-mode-call-stack-introspection", "name": "js/strict-mode-call-stack-introspection", "shortDescription": {"text": "Use of call stack introspection in strict mode"}, "fullDescription": {"text": "Accessing properties 'arguments.caller', 'arguments.callee', 'Function.prototype.caller' or 'Function.prototype.arguments' in strict mode will cause a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "language-features"], "description": "Accessing properties 'arguments.caller', 'arguments.callee',\n              'Function.prototype.caller' or 'Function.prototype.arguments'\n              in strict mode will cause a runtime error.", "id": "js/strict-mode-call-stack-introspection", "kind": "problem", "name": "Use of call stack introspection in strict mode", "precision": "high", "problem.severity": "error"}}, {"id": "js/syntax-error", "name": "js/syntax-error", "shortDescription": {"text": "Syntax error"}, "fullDescription": {"text": "A piece of code could not be parsed due to syntax errors."}, "defaultConfiguration": {"enabled": true, "level": "note"}, "properties": {"tags": ["reliability", "correctness", "language-features"], "description": "A piece of code could not be parsed due to syntax errors.", "id": "js/syntax-error", "kind": "problem", "name": "Syntax error", "precision": "very-high", "problem.severity": "recommendation"}}, {"id": "js/template-syntax-in-string-literal", "name": "js/template-syntax-in-string-literal", "shortDescription": {"text": "Template syntax in string literal"}, "fullDescription": {"text": "A string literal appears to use template syntax but is not quoted with backticks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "A string literal appears to use template syntax but is not quoted with backticks.", "id": "js/template-syntax-in-string-literal", "kind": "problem", "name": "Template syntax in string literal", "precision": "high", "problem.severity": "warning"}}, {"id": "js/incomplete-object-initialization", "name": "js/incomplete-object-initialization", "shortDescription": {"text": "Use of incompletely initialized object"}, "fullDescription": {"text": "Accessing 'this' or a property of 'super' in the constructor of a subclass before calling the super constructor will cause a runtime error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "language-features"], "description": "Accessing 'this' or a property of 'super' in the constructor of a\n              subclass before calling the super constructor will cause a runtime error.", "id": "js/incomplete-object-initialization", "kind": "problem", "name": "Use of incompletely initialized object", "precision": "high", "problem.severity": "error"}}, {"id": "js/unused-index-variable", "name": "js/unused-index-variable", "shortDescription": {"text": "Unused index variable"}, "fullDescription": {"text": "Iterating over an array but not using the index variable to access array elements may indicate a typo or logic error."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "Iterating over an array but not using the index variable to access array elements\n              may indicate a typo or logic error.", "id": "js/unused-index-variable", "kind": "problem", "name": "Unused index variable", "precision": "high", "problem.severity": "warning"}}, {"id": "js/with-statement", "name": "js/with-statement", "shortDescription": {"text": "With statement"}, "fullDescription": {"text": "The 'with' statement has subtle semantics and should not be used."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "language-features"], "description": "The 'with' statement has subtle semantics and should not be used.", "id": "js/with-statement", "kind": "problem", "name": "With statement", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/yield-outside-generator", "name": "js/yield-outside-generator", "shortDescription": {"text": "Yield in non-generator function"}, "fullDescription": {"text": "'yield' should only be used in generator functions."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["maintainability", "language-features", "external/cwe/cwe-758"], "description": "'yield' should only be used in generator functions.", "id": "js/yield-outside-generator", "kind": "problem", "name": "Yield in non-generator function", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/node/assignment-to-exports-variable", "name": "js/node/assignment-to-exports-variable", "shortDescription": {"text": "Assignment to exports variable"}, "fullDescription": {"text": "Assigning to the special 'exports' variable only overwrites its value and does not export anything. Such an assignment is hence most likely unintentional."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "frameworks/node.js", "external/cwe/cwe-563"], "description": "Assigning to the special 'exports' variable only overwrites its value and does not export\n              anything. Such an assignment is hence most likely unintentional.", "id": "js/node/assignment-to-exports-variable", "kind": "problem", "name": "Assignment to exports variable", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/node/missing-exports-qualifier", "name": "js/node/missing-exports-qualifier", "shortDescription": {"text": "Missing exports qualifier"}, "fullDescription": {"text": "Referencing an undeclared global variable in a module that exports a definition of the same name is confusing and may indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["maintainability", "frameworks/node.js"], "description": "Referencing an undeclared global variable in a module that exports\n              a definition of the same name is confusing and may indicate a bug.", "id": "js/node/missing-exports-qualifier", "kind": "problem", "name": "Missing exports qualifier", "precision": "high", "problem.severity": "error"}}, {"id": "js/polynomial-redos", "name": "js/polynomial-redos", "shortDescription": {"text": "Polynomial regular expression used on uncontrolled data"}, "fullDescription": {"text": "A regular expression that can require polynomial time to match may be vulnerable to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-1333", "external/cwe/cwe-730", "external/cwe/cwe-400"], "description": "A regular expression that can require polynomial time\n              to match may be vulnerable to denial-of-service attacks.", "id": "js/polynomial-redos", "kind": "path-problem", "name": "Polynomial regular expression used on uncontrolled data", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/redos", "name": "js/redos", "shortDescription": {"text": "Inefficient regular expression"}, "fullDescription": {"text": "A regular expression that requires exponential time to match certain inputs can be a performance bottleneck, and may be vulnerable to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-1333", "external/cwe/cwe-730", "external/cwe/cwe-400"], "description": "A regular expression that requires exponential time to match certain inputs\n              can be a performance bottleneck, and may be vulnerable to denial-of-service\n              attacks.", "id": "js/redos", "kind": "problem", "name": "Inefficient regular expression", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/react/direct-state-mutation", "name": "js/react/direct-state-mutation", "shortDescription": {"text": "Direct state mutation"}, "fullDescription": {"text": "Mutating the state of a React component directly may lead to lost updates."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "frameworks/react"], "description": "Mutating the state of a React component directly may lead to\n              lost updates.", "id": "js/react/direct-state-mutation", "kind": "problem", "name": "Direct state mutation", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/react/inconsistent-state-update", "name": "js/react/inconsistent-state-update", "shortDescription": {"text": "Potentially inconsistent state update"}, "fullDescription": {"text": "Updating the state of a component based on the current value of 'this.state' or 'this.props' may lead to inconsistent component state."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "frameworks/react"], "description": "Updating the state of a component based on the current value of\n              'this.state' or 'this.props' may lead to inconsistent component\n              state.", "id": "js/react/inconsistent-state-update", "kind": "problem", "name": "Potentially inconsistent state update", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/react/unsupported-state-update-in-lifecycle-method", "name": "js/react/unsupported-state-update-in-lifecycle-method", "shortDescription": {"text": "Unsupported state update in lifecycle method"}, "fullDescription": {"text": "Attempting to update the state of a React component at the wrong time can cause undesired behavior."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "frameworks/react"], "description": "Attempting to update the state of a React component at the wrong time can cause undesired behavior.", "id": "js/react/unsupported-state-update-in-lifecycle-method", "kind": "problem", "name": "Unsupported state update in lifecycle method", "precision": "high", "problem.severity": "warning"}}, {"id": "js/react/unused-or-undefined-state-property", "name": "js/react/unused-or-undefined-state-property", "shortDescription": {"text": "Unused or undefined state property"}, "fullDescription": {"text": "Unused or undefined component state properties may be a symptom of a bug and should be examined carefully."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "reliability", "frameworks/react"], "description": "Unused or undefined component state properties may be a symptom of a bug and should be examined carefully.", "id": "js/react/unused-or-undefined-state-property", "kind": "problem", "name": "Unused or undefined state property", "precision": "high", "problem.severity": "warning"}}, {"id": "js/regex/back-reference-before-group", "name": "js/regex/back-reference-before-group", "shortDescription": {"text": "Back reference precedes capture group"}, "fullDescription": {"text": "If a back reference precedes the capture group it refers to, it matches the empty string, which is probably not what was expected."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions"], "description": "If a back reference precedes the capture group it refers to, it matches the empty string,\n              which is probably not what was expected.", "id": "js/regex/back-reference-before-group", "kind": "problem", "name": "Back reference precedes capture group", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/regex/back-reference-to-negative-lookahead", "name": "js/regex/back-reference-to-negative-lookahead", "shortDescription": {"text": "Back reference into negative lookahead assertion"}, "fullDescription": {"text": "If a back reference refers to a capture group inside a preceding negative lookahead assertion, then the back reference always matches the empty string, which probably indicates a mistake."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions"], "description": "If a back reference refers to a capture group inside a preceding negative lookahead assertion,\n              then the back reference always matches the empty string, which probably indicates a mistake.", "id": "js/regex/back-reference-to-negative-lookahead", "kind": "problem", "name": "Back reference into negative lookahead assertion", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/regex/duplicate-in-character-class", "name": "js/regex/duplicate-in-character-class", "shortDescription": {"text": "Duplicate character in character class"}, "fullDescription": {"text": "If a character class in a regular expression contains the same character twice, this may indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions"], "description": "If a character class in a regular expression contains the same character twice, this may\n              indicate a bug.", "id": "js/regex/duplicate-in-character-class", "kind": "problem", "name": "Duplicate character in character class", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/regex/empty-character-class", "name": "js/regex/empty-character-class", "shortDescription": {"text": "Empty character class"}, "fullDescription": {"text": "Empty character classes are not normally useful and may indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions"], "description": "Empty character classes are not normally useful and may indicate a bug.", "id": "js/regex/empty-character-class", "kind": "problem", "name": "Empty character class", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/identity-replacement", "name": "js/identity-replacement", "shortDescription": {"text": "Replacement of a substring with itself"}, "fullDescription": {"text": "Replacing a substring with itself has no effect and may indicate a mistake."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-116"], "description": "Replacing a substring with itself has no effect and may indicate a mistake.", "id": "js/identity-replacement", "kind": "problem", "name": "Replacement of a substring with itself", "precision": "very-high", "problem.severity": "warning", "security-severity": "5.0"}}, {"id": "js/regex/always-matches", "name": "js/regex/always-matches", "shortDescription": {"text": "Regular expression always matches"}, "fullDescription": {"text": "Regular expression tests that always find a match indicate dead code or a logic error"}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "regular-expressions", "quality"], "description": "Regular expression tests that always find a match indicate dead code or a logic error", "id": "js/regex/always-matches", "kind": "problem", "name": "Regular expression always matches", "precision": "high", "problem.severity": "warning"}}, {"id": "js/regex/unbound-back-reference", "name": "js/regex/unbound-back-reference", "shortDescription": {"text": "Unbound back reference"}, "fullDescription": {"text": "Regular expression escape sequences of the form '\\n', where 'n' is a positive number greater than the number of capture groups in the regular expression, are not allowed by the ECMAScript standard."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions"], "description": "Regular expression escape sequences of the form '\\n', where 'n' is a positive number\n              greater than the number of capture groups in the regular expression, are not allowed\n              by the ECMAScript standard.", "id": "js/regex/unbound-back-reference", "kind": "problem", "name": "Unbound back reference", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/regex/unmatchable-caret", "name": "js/regex/unmatchable-caret", "shortDescription": {"text": "Unmatchable caret in regular expression"}, "fullDescription": {"text": "If a caret assertion '^' appears in a regular expression after another term that cannot match the empty string, then this assertion can never match, so the entire regular expression cannot match any string."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions", "external/cwe/cwe-561"], "description": "If a caret assertion '^' appears in a regular expression after another term that\n              cannot match the empty string, then this assertion can never match, so the entire\n              regular expression cannot match any string.", "id": "js/regex/unmatchable-caret", "kind": "problem", "name": "Unmatchable caret in regular expression", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/regex/unmatchable-dollar", "name": "js/regex/unmatchable-dollar", "shortDescription": {"text": "Unmatchable dollar in regular expression"}, "fullDescription": {"text": "If a dollar assertion '$' appears in a regular expression before another term that cannot match the empty string, then this assertion can never match, so the entire regular expression cannot match any string."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["reliability", "correctness", "regular-expressions", "external/cwe/cwe-561"], "description": "If a dollar assertion '$' appears in a regular expression before another term that\n              cannot match the empty string, then this assertion can never match, so the entire\n              regular expression cannot match any string.", "id": "js/regex/unmatchable-dollar", "kind": "problem", "name": "Unmatchable dollar in regular expression", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/incomplete-hostname-regexp", "name": "js/incomplete-hostname-regexp", "shortDescription": {"text": "Incomplete regular expression for hostnames"}, "fullDescription": {"text": "Matching a URL or hostname against a regular expression that contains an unescaped dot as part of the hostname might match more hostnames than expected."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020"], "description": "Matching a URL or hostname against a regular expression that contains an unescaped dot as part of the hostname might match more hostnames than expected.", "id": "js/incomplete-hostname-regexp", "kind": "problem", "name": "Incomplete regular expression for hostnames", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/incomplete-url-scheme-check", "name": "js/incomplete-url-scheme-check", "shortDescription": {"text": "Incomplete URL scheme check"}, "fullDescription": {"text": "Checking for the \"javascript:\" URL scheme without also checking for \"vbscript:\" and \"data:\" suggests a logic error or even a security vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "correctness", "external/cwe/cwe-020", "external/cwe/cwe-184"], "description": "Checking for the \"javascript:\" URL scheme without also checking for \"vbscript:\"\n              and \"data:\" suggests a logic error or even a security vulnerability.", "id": "js/incomplete-url-scheme-check", "kind": "problem", "name": "Incomplete URL scheme check", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/incomplete-url-substring-sanitization", "name": "js/incomplete-url-substring-sanitization", "shortDescription": {"text": "Incomplete URL substring sanitization"}, "fullDescription": {"text": "Security checks on the substrings of an unparsed URL are often vulnerable to bypassing."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020"], "description": "Security checks on the substrings of an unparsed URL are often vulnerable to bypassing.", "id": "js/incomplete-url-substring-sanitization", "kind": "problem", "name": "Incomplete URL substring sanitization", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/incorrect-suffix-check", "name": "js/incorrect-suffix-check", "shortDescription": {"text": "Incorrect suffix check"}, "fullDescription": {"text": "Using indexOf to implement endsWith functionality is error-prone if the -1 case is not explicitly handled."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "correctness", "external/cwe/cwe-020"], "description": "Using indexOf to implement endsWith functionality is error-prone if the -1 case is not explicitly handled.", "id": "js/incorrect-suffix-check", "kind": "problem", "name": "Incorrect suffix check", "precision": "high", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/overly-large-range", "name": "js/overly-large-range", "shortDescription": {"text": "Overly permissive regular expression range"}, "fullDescription": {"text": "Overly permissive regular expression ranges match a wider range of characters than intended. This may allow an attacker to bypass a filter or sanitizer."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020"], "description": "Overly permissive regular expression ranges match a wider range of characters than intended.\n              This may allow an attacker to bypass a filter or sanitizer.", "id": "js/overly-large-range", "kind": "problem", "name": "Overly permissive regular expression range", "precision": "high", "problem.severity": "warning", "security-severity": "5.0"}}, {"id": "js/useless-regexp-character-escape", "name": "js/useless-regexp-character-escape", "shortDescription": {"text": "Useless regular-expression character escape"}, "fullDescription": {"text": "Prepending a backslash to an ordinary character in a string does not have any effect, and may make regular expressions constructed from this string behave unexpectedly."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020"], "description": "Prepending a backslash to an ordinary character in a string\n              does not have any effect, and may make regular expressions constructed from this string\n              behave unexpectedly.", "id": "js/useless-regexp-character-escape", "kind": "problem", "name": "Useless regular-expression character escape", "precision": "high", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/path-injection", "name": "js/path-injection", "shortDescription": {"text": "Uncontrolled data used in path expression"}, "fullDescription": {"text": "Accessing paths influenced by users can allow an attacker to access unexpected resources."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-022", "external/cwe/cwe-023", "external/cwe/cwe-036", "external/cwe/cwe-073", "external/cwe/cwe-099"], "description": "Accessing paths influenced by users can allow an attacker to access\n              unexpected resources.", "id": "js/path-injection", "kind": "path-problem", "name": "Uncontrolled data used in path expression", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/zipslip", "name": "js/zipslip", "shortDescription": {"text": "Arbitrary file access during archive extraction (\"Zip Slip\")"}, "fullDescription": {"text": "Extracting files from a malicious ZIP file, or similar type of archive, without validating that the destination file path is within the destination directory can allow an attacker to unexpectedly gain access to resources."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-022"], "description": "Extracting files from a malicious ZIP file, or similar type of archive, without\n              validating that the destination file path is within the destination directory\n              can allow an attacker to unexpectedly gain access to resources.", "id": "js/zipslip", "kind": "path-problem", "name": "Arbitrary file access during archive extraction (\"Zip Slip\")", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/template-object-injection", "name": "js/template-object-injection", "shortDescription": {"text": "Template Object Injection"}, "fullDescription": {"text": "Instantiating a template using a user-controlled object is vulnerable to local file read and potential remote code execution."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-073", "external/cwe/cwe-094"], "description": "Instantiating a template using a user-controlled object is vulnerable to local file read and potential remote code execution.", "id": "js/template-object-injection", "kind": "path-problem", "name": "Template Object Injection", "precision": "high", "problem.severity": "error", "security-severity": "9.3"}}, {"id": "js/command-line-injection", "name": "js/command-line-injection", "shortDescription": {"text": "Uncontrolled command line"}, "fullDescription": {"text": "Using externally controlled strings in a command line may allow a malicious user to change the meaning of the command."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-078", "external/cwe/cwe-088"], "description": "Using externally controlled strings in a command line may allow a malicious\n              user to change the meaning of the command.", "id": "js/command-line-injection", "kind": "path-problem", "name": "Uncontrolled command line", "precision": "high", "problem.severity": "error", "security-severity": "9.8"}}, {"id": "js/second-order-command-line-injection", "name": "js/second-order-command-line-injection", "shortDescription": {"text": "Second order command injection"}, "fullDescription": {"text": "Using user-controlled data as arguments to some commands, such as git clone, can allow arbitrary commands to be executed."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-078", "external/cwe/cwe-088"], "description": "Using user-controlled data as arguments to some commands, such as git clone,\n              can allow arbitrary commands to be executed.", "id": "js/second-order-command-line-injection", "kind": "path-problem", "name": "Second order command injection", "precision": "high", "problem.severity": "error", "security-severity": "7.0"}}, {"id": "js/shell-command-injection-from-environment", "name": "js/shell-command-injection-from-environment", "shortDescription": {"text": "Shell command built from environment values"}, "fullDescription": {"text": "Building a shell command string with values from the enclosing environment may cause subtle bugs or vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-078", "external/cwe/cwe-088"], "description": "Building a shell command string with values from the enclosing\n              environment may cause subtle bugs or vulnerabilities.", "id": "js/shell-command-injection-from-environment", "kind": "path-problem", "name": "Shell command built from environment values", "precision": "high", "problem.severity": "warning", "security-severity": "6.3"}}, {"id": "js/shell-command-constructed-from-input", "name": "js/shell-command-constructed-from-input", "shortDescription": {"text": "Unsafe shell command constructed from library input"}, "fullDescription": {"text": "Using externally controlled strings in a command line may allow a malicious user to change the meaning of the command."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-078", "external/cwe/cwe-088"], "description": "Using externally controlled strings in a command line may allow a malicious\n              user to change the meaning of the command.", "id": "js/shell-command-constructed-from-input", "kind": "path-problem", "name": "Unsafe shell command constructed from library input", "precision": "high", "problem.severity": "error", "security-severity": "6.3"}}, {"id": "js/unnecessary-use-of-cat", "name": "js/unnecessary-use-of-cat", "shortDescription": {"text": "Unnecessary use of `cat` process"}, "fullDescription": {"text": "Using the `cat` process to read a file is unnecessarily complex, inefficient, unportable, and can lead to subtle bugs, or even security vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "security", "maintainability", "external/cwe/cwe-078"], "description": "Using the  `cat` process to read a file is unnecessarily complex, inefficient, unportable, and can lead to subtle bugs, or even security vulnerabilities.", "id": "js/unnecessary-use-of-cat", "kind": "problem", "name": "Unnecessary use of `cat` process", "precision": "high", "problem.severity": "error", "security-severity": "6.3"}}, {"id": "js/xss-through-exception", "name": "js/xss-through-exception", "shortDescription": {"text": "Exception text reinterpreted as HTML"}, "fullDescription": {"text": "Reinterpreting text from an exception as HTML can lead to a cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Reinterpreting text from an exception as HTML\n              can lead to a cross-site scripting vulnerability.", "id": "js/xss-through-exception", "kind": "path-problem", "name": "Exception text reinterpreted as HTML", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/reflected-xss", "name": "js/reflected-xss", "shortDescription": {"text": "Reflected cross-site scripting"}, "fullDescription": {"text": "Writing user input directly to an HTTP response allows for a cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Writing user input directly to an HTTP response allows for\n              a cross-site scripting vulnerability.", "id": "js/reflected-xss", "kind": "path-problem", "name": "Reflected cross-site scripting", "precision": "high", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/stored-xss", "name": "js/stored-xss", "shortDescription": {"text": "Stored cross-site scripting"}, "fullDescription": {"text": "Using uncontrolled stored values in HTML allows for a stored cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Using uncontrolled stored values in HTML allows for\n              a stored cross-site scripting vulnerability.", "id": "js/stored-xss", "kind": "path-problem", "name": "Stored cross-site scripting", "precision": "high", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/html-constructed-from-input", "name": "js/html-constructed-from-input", "shortDescription": {"text": "Unsafe HTML constructed from library input"}, "fullDescription": {"text": "Using externally controlled strings to construct HTML might allow a malicious user to perform a cross-site scripting attack."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Using externally controlled strings to construct HTML might allow a malicious\n              user to perform a cross-site scripting attack.", "id": "js/html-constructed-from-input", "kind": "path-problem", "name": "Unsafe HTML constructed from library input", "precision": "high", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/unsafe-jquery-plugin", "name": "js/unsafe-jquery-plugin", "shortDescription": {"text": "Unsafe jQuery plugin"}, "fullDescription": {"text": "A jQuery plugin that unintentionally constructs HTML from some of its options may be unsafe to use for clients."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116", "frameworks/jquery"], "description": "A jQuery plugin that unintentionally constructs HTML from some of its options may be unsafe to use for clients.", "id": "js/unsafe-jquery-plugin", "kind": "path-problem", "name": "Unsafe jQuery plugin", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/xss", "name": "js/xss", "shortDescription": {"text": "Client-side cross-site scripting"}, "fullDescription": {"text": "Writing user input directly to the DOM allows for a cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Writing user input directly to the DOM allows for\n              a cross-site scripting vulnerability.", "id": "js/xss", "kind": "path-problem", "name": "Client-side cross-site scripting", "precision": "high", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/xss-through-dom", "name": "js/xss-through-dom", "shortDescription": {"text": "DOM text reinterpreted as HTML"}, "fullDescription": {"text": "Reinterpreting text from the DOM as HTML can lead to a cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Reinterpreting text from the DOM as HTML\n              can lead to a cross-site scripting vulnerability.", "id": "js/xss-through-dom", "kind": "path-problem", "name": "DOM text reinterpreted as HTML", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/sql-injection", "name": "js/sql-injection", "shortDescription": {"text": "Database query built from user-controlled sources"}, "fullDescription": {"text": "Building a database query from user-controlled sources is vulnerable to insertion of malicious code by the user."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-089", "external/cwe/cwe-090", "external/cwe/cwe-943"], "description": "Building a database query from user-controlled sources is vulnerable to insertion of\n              malicious code by the user.", "id": "js/sql-injection", "kind": "path-problem", "name": "Database query built from user-controlled sources", "precision": "high", "problem.severity": "error", "security-severity": "8.8"}}, {"id": "js/code-injection", "name": "js/code-injection", "shortDescription": {"text": "Code injection"}, "fullDescription": {"text": "Interpreting unsanitized user input as code allows a malicious user arbitrary code execution."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-094", "external/cwe/cwe-095", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Interpreting unsanitized user input as code allows a malicious user arbitrary\n              code execution.", "id": "js/code-injection", "kind": "path-problem", "name": "Code injection", "precision": "high", "problem.severity": "error", "security-severity": "9.3"}}, {"id": "js/actions/command-injection", "name": "js/actions/command-injection", "shortDescription": {"text": "Expression injection in Actions"}, "fullDescription": {"text": "Using user-controlled GitHub Actions contexts like `run:` or `script:` may allow a malicious user to inject code into the GitHub action."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["actions", "security", "external/cwe/cwe-094"], "description": "Using user-controlled GitHub Actions contexts like `run:` or `script:` may allow a malicious\n              user to inject code into the GitHub action.", "id": "js/actions/command-injection", "kind": "problem", "name": "Expression injection in Actions", "precision": "high", "problem.severity": "warning", "security-severity": "9.3"}}, {"id": "js/bad-code-sanitization", "name": "js/bad-code-sanitization", "shortDescription": {"text": "Improper code sanitization"}, "fullDescription": {"text": "Escaping code as HTML does not provide protection against code injection."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-094", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Escaping code as HTML does not provide protection against code injection.", "id": "js/bad-code-sanitization", "kind": "path-problem", "name": "Improper code sanitization", "precision": "high", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/unsafe-dynamic-method-access", "name": "js/unsafe-dynamic-method-access", "shortDescription": {"text": "Unsafe dynamic method access"}, "fullDescription": {"text": "Invoking user-controlled methods on certain objects can lead to remote code execution."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-094"], "description": "Invoking user-controlled methods on certain objects can lead to remote code execution.", "id": "js/unsafe-dynamic-method-access", "kind": "path-problem", "name": "Unsafe dynamic method access", "precision": "high", "problem.severity": "error", "security-severity": "9.3"}}, {"id": "js/client-exposed-cookie", "name": "js/client-exposed-cookie", "shortDescription": {"text": "Sensitive server cookie exposed to the client"}, "fullDescription": {"text": "Sensitive cookies set by a server can be read by the client if the `httpOnly` flag is not set."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-1004"], "description": "Sensitive cookies set by a server can be read by the client if the `httpOnly` flag is not set.", "id": "js/client-exposed-cookie", "kind": "problem", "name": "Sensitive server cookie exposed to the client", "precision": "high", "problem.severity": "warning", "security-severity": "5.0"}}, {"id": "js/bad-tag-filter", "name": "js/bad-tag-filter", "shortDescription": {"text": "Bad HTML filtering regexp"}, "fullDescription": {"text": "Matching HTML tags using regular expressions is hard to do right, and can easily lead to security issues."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020", "external/cwe/cwe-080", "external/cwe/cwe-116", "external/cwe/cwe-184", "external/cwe/cwe-185", "external/cwe/cwe-186"], "description": "Matching HTML tags using regular expressions is hard to do right, and can easily lead to security issues.", "id": "js/bad-tag-filter", "kind": "problem", "name": "Bad HTML filtering regexp", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/double-escaping", "name": "js/double-escaping", "shortDescription": {"text": "Double escaping or unescaping"}, "fullDescription": {"text": "When escaping special characters using a meta-character like backslash or ampersand, the meta-character has to be escaped first to avoid double-escaping, and conversely it has to be unescaped last to avoid double-unescaping."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-116", "external/cwe/cwe-020"], "description": "When escaping special characters using a meta-character like backslash or\n              ampersand, the meta-character has to be escaped first to avoid double-escaping,\n              and conversely it has to be unescaped last to avoid double-unescaping.", "id": "js/double-escaping", "kind": "problem", "name": "Double escaping or unescaping", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/incomplete-html-attribute-sanitization", "name": "js/incomplete-html-attribute-sanitization", "shortDescription": {"text": "Incomplete HTML attribute sanitization"}, "fullDescription": {"text": "Writing incompletely sanitized values to HTML attribute strings can lead to a cross-site scripting vulnerability."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116", "external/cwe/cwe-020"], "description": "Writing incompletely sanitized values to HTML\n              attribute strings can lead to a cross-site\n              scripting vulnerability.", "id": "js/incomplete-html-attribute-sanitization", "kind": "path-problem", "name": "Incomplete HTML attribute sanitization", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/incomplete-multi-character-sanitization", "name": "js/incomplete-multi-character-sanitization", "shortDescription": {"text": "Incomplete multi-character sanitization"}, "fullDescription": {"text": "A sanitizer that removes a sequence of characters may reintroduce the dangerous sequence."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020", "external/cwe/cwe-080", "external/cwe/cwe-116"], "description": "A sanitizer that removes a sequence of characters may reintroduce the dangerous sequence.", "id": "js/incomplete-multi-character-sanitization", "kind": "problem", "name": "Incomplete multi-character sanitization", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/incomplete-sanitization", "name": "js/incomplete-sanitization", "shortDescription": {"text": "Incomplete string escaping or encoding"}, "fullDescription": {"text": "A string transformer that does not replace or escape all occurrences of a meta-character may be ineffective."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020", "external/cwe/cwe-080", "external/cwe/cwe-116"], "description": "A string transformer that does not replace or escape all occurrences of a\n              meta-character may be ineffective.", "id": "js/incomplete-sanitization", "kind": "problem", "name": "Incomplete string escaping or encoding", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/unsafe-html-expansion", "name": "js/unsafe-html-expansion", "shortDescription": {"text": "Unsafe expansion of self-closing HTML tag"}, "fullDescription": {"text": "Using regular expressions to expand self-closing HTML tags may lead to cross-site scripting vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Using regular expressions to expand self-closing HTML\n              tags may lead to cross-site scripting vulnerabilities.", "id": "js/unsafe-html-expansion", "kind": "problem", "name": "Unsafe expansion of self-closing HTML tag", "precision": "very-high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/tainted-format-string", "name": "js/tainted-format-string", "shortDescription": {"text": "Use of externally-controlled format string"}, "fullDescription": {"text": "Using external input in format strings can lead to garbled output."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-134"], "description": "Using external input in format strings can lead to garbled output.", "id": "js/tainted-format-string", "kind": "path-problem", "name": "Use of externally-controlled format string", "precision": "high", "problem.severity": "warning", "security-severity": "7.3"}}, {"id": "js/case-sensitive-middleware-path", "name": "js/case-sensitive-middleware-path", "shortDescription": {"text": "Case-sensitive middleware path"}, "fullDescription": {"text": "Middleware with case-sensitive paths do not protect endpoints with case-insensitive paths."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-178"], "description": "Middleware with case-sensitive paths do not protect endpoints with case-insensitive paths.", "id": "js/case-sensitive-middleware-path", "kind": "problem", "name": "Case-sensitive middleware path", "precision": "high", "problem.severity": "warning", "security-severity": "7.3"}}, {"id": "js/exposure-of-private-files", "name": "js/exposure-of-private-files", "shortDescription": {"text": "Exposure of private files"}, "fullDescription": {"text": "Exposing a node_modules folder, or the project folder to the public, can cause exposure of private information."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-200", "external/cwe/cwe-219", "external/cwe/cwe-548"], "description": "Exposing a node_modules folder, or the project folder to the public, can cause exposure\n              of private information.", "id": "js/exposure-of-private-files", "kind": "problem", "name": "Exposure of private files", "precision": "high", "problem.severity": "warning", "security-severity": "6.5"}}, {"id": "js/cross-window-information-leak", "name": "js/cross-window-information-leak", "shortDescription": {"text": "Cross-window communication with unrestricted target origin"}, "fullDescription": {"text": "When sending sensitive information to another window using `postMessage`, the origin of the target window should be restricted to avoid unintentional information leaks."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-201", "external/cwe/cwe-359"], "description": "When sending sensitive information to another window using `postMessage`,\n              the origin of the target window should be restricted to avoid unintentional\n              information leaks.", "id": "js/cross-window-information-leak", "kind": "path-problem", "name": "Cross-window communication with unrestricted target origin", "precision": "high", "problem.severity": "error", "security-severity": "4.3"}}, {"id": "js/stack-trace-exposure", "name": "js/stack-trace-exposure", "shortDescription": {"text": "Information exposure through a stack trace"}, "fullDescription": {"text": "Propagating stack trace information to an external user can unintentionally reveal implementation details that are useful to an attacker for developing a subsequent exploit."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-209", "external/cwe/cwe-497"], "description": "Propagating stack trace information to an external user can\n              unintentionally reveal implementation details that are useful\n              to an attacker for developing a subsequent exploit.", "id": "js/stack-trace-exposure", "kind": "path-problem", "name": "Information exposure through a stack trace", "precision": "very-high", "problem.severity": "warning", "security-severity": "5.4"}}, {"id": "js/disabling-certificate-validation", "name": "js/disabling-certificate-validation", "shortDescription": {"text": "Disabling certificate validation"}, "fullDescription": {"text": "Disabling cryptographic certificate validation can cause security vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-295", "external/cwe/cwe-297"], "description": "Disabling cryptographic certificate validation can cause security vulnerabilities.", "id": "js/disabling-certificate-validation", "kind": "problem", "name": "Disabling certificate validation", "precision": "very-high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/insecure-dependency", "name": "js/insecure-dependency", "shortDescription": {"text": "Dependency download using unencrypted communication channel"}, "fullDescription": {"text": "Using unencrypted protocols to fetch dependencies can leave an application open to man-in-the-middle attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-300", "external/cwe/cwe-319", "external/cwe/cwe-494", "external/cwe/cwe-829"], "description": "Using unencrypted protocols to fetch dependencies can leave an application\n              open to man-in-the-middle attacks.", "id": "js/insecure-dependency", "kind": "problem", "name": "Dependency download using unencrypted communication channel", "precision": "high", "problem.severity": "warning", "security-severity": "8.1"}}, {"id": "js/actions/actions-artifact-leak", "name": "js/actions/actions-artifact-leak", "shortDescription": {"text": "Storage of sensitive information in GitHub Actions artifact"}, "fullDescription": {"text": "Including sensitive information in a GitHub Actions artifact can expose it to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["actions", "security", "external/cwe/cwe-312", "external/cwe/cwe-315", "external/cwe/cwe-359"], "description": "Including sensitive information in a GitHub Actions artifact can\n              expose it to an attacker.", "id": "js/actions/actions-artifact-leak", "kind": "problem", "name": "Storage of sensitive information in GitHub Actions artifact", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/build-artifact-leak", "name": "js/build-artifact-leak", "shortDescription": {"text": "Storage of sensitive information in build artifact"}, "fullDescription": {"text": "Including sensitive information in a build artifact can expose it to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-312", "external/cwe/cwe-315", "external/cwe/cwe-359"], "description": "Including sensitive information in a build artifact can\n              expose it to an attacker.", "id": "js/build-artifact-leak", "kind": "path-problem", "name": "Storage of sensitive information in build artifact", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/clear-text-logging", "name": "js/clear-text-logging", "shortDescription": {"text": "Clear-text logging of sensitive information"}, "fullDescription": {"text": "Logging sensitive information without encryption or hashing can expose it to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-312", "external/cwe/cwe-359", "external/cwe/cwe-532"], "description": "Logging sensitive information without encryption or hashing can\n              expose it to an attacker.", "id": "js/clear-text-logging", "kind": "path-problem", "name": "Clear-text logging of sensitive information", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/clear-text-storage-of-sensitive-data", "name": "js/clear-text-storage-of-sensitive-data", "shortDescription": {"text": "Clear text storage of sensitive information"}, "fullDescription": {"text": "Sensitive information stored without encryption or hashing can expose it to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-312", "external/cwe/cwe-315", "external/cwe/cwe-359"], "description": "Sensitive information stored without encryption or hashing can expose it to an\n              attacker.", "id": "js/clear-text-storage-of-sensitive-data", "kind": "path-problem", "name": "Clear text storage of sensitive information", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/insufficient-key-size", "name": "js/insufficient-key-size", "shortDescription": {"text": "Use of a weak cryptographic key"}, "fullDescription": {"text": "Using a weak cryptographic key can allow an attacker to compromise security."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-326"], "description": "Using a weak cryptographic key can allow an attacker to compromise security.", "id": "js/insufficient-key-size", "kind": "problem", "name": "Use of a weak cryptographic key", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/biased-cryptographic-random", "name": "js/biased-cryptographic-random", "shortDescription": {"text": "Creating biased random numbers from a cryptographically secure source"}, "fullDescription": {"text": "Some mathematical operations on random numbers can cause bias in the results and compromise security."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-327"], "description": "Some mathematical operations on random numbers can cause bias in\n              the results and compromise security.", "id": "js/biased-cryptographic-random", "kind": "problem", "name": "Creating biased random numbers from a cryptographically secure source", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/weak-cryptographic-algorithm", "name": "js/weak-cryptographic-algorithm", "shortDescription": {"text": "Use of a broken or weak cryptographic algorithm"}, "fullDescription": {"text": "Using broken or weak cryptographic algorithms can compromise security."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-327", "external/cwe/cwe-328"], "description": "Using broken or weak cryptographic algorithms can compromise security.", "id": "js/weak-cryptographic-algorithm", "kind": "path-problem", "name": "Use of a broken or weak cryptographic algorithm", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/insecure-randomness", "name": "js/insecure-randomness", "shortDescription": {"text": "Insecure randomness"}, "fullDescription": {"text": "Using a cryptographically weak pseudo-random number generator to generate a security-sensitive value may allow an attacker to predict what value will be generated."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-338"], "description": "Using a cryptographically weak pseudo-random number generator to generate a\n              security-sensitive value may allow an attacker to predict what value will\n              be generated.", "id": "js/insecure-randomness", "kind": "path-problem", "name": "Insecure randomness", "precision": "high", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/cors-misconfiguration-for-credentials", "name": "js/cors-misconfiguration-for-credentials", "shortDescription": {"text": "CORS misconfiguration for credentials transfer"}, "fullDescription": {"text": "Misconfiguration of CORS HTTP headers allows for leaks of secret credentials."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-346", "external/cwe/cwe-639", "external/cwe/cwe-942"], "description": "Misconfiguration of CORS HTTP headers allows for leaks of secret credentials.", "id": "js/cors-misconfiguration-for-credentials", "kind": "path-problem", "name": "CORS misconfiguration for credentials transfer", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/jwt-missing-verification", "name": "js/jwt-missing-verification", "shortDescription": {"text": "JWT missing secret or public key verification"}, "fullDescription": {"text": "The application does not verify the JWT payload with a cryptographic secret or public key."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-347"], "description": "The application does not verify the JWT payload with a cryptographic secret or public key.", "id": "js/jwt-missing-verification", "kind": "problem", "name": "JWT missing secret or public key verification", "precision": "high", "problem.severity": "warning", "security-severity": "7.0"}}, {"id": "js/missing-token-validation", "name": "js/missing-token-validation", "shortDescription": {"text": "Missing CSRF middleware"}, "fullDescription": {"text": "Using cookies without CSRF protection may allow malicious websites to submit requests on behalf of the user."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-352"], "description": "Using cookies without CSRF protection may allow malicious websites to\n              submit requests on behalf of the user.", "id": "js/missing-token-validation", "kind": "problem", "name": "Missing CSRF middleware", "precision": "high", "problem.severity": "error", "security-severity": "8.8"}}, {"id": "js/resource-exhaustion-from-deep-object-traversal", "name": "js/resource-exhaustion-from-deep-object-traversal", "shortDescription": {"text": "Resources exhaustion from deep object traversal"}, "fullDescription": {"text": "Processing user-controlled object hierarchies inefficiently can lead to denial of service."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-400"], "description": "Processing user-controlled object hierarchies inefficiently can lead to denial of service.", "id": "js/resource-exhaustion-from-deep-object-traversal", "kind": "path-problem", "name": "Resources exhaustion from deep object traversal", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/unsafe-deserialization", "name": "js/unsafe-deserialization", "shortDescription": {"text": "Deserialization of user-controlled data"}, "fullDescription": {"text": "Deserializing user-controlled data may allow attackers to execute arbitrary code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-502"], "description": "Deserializing user-controlled data may allow attackers to\n              execute arbitrary code.", "id": "js/unsafe-deserialization", "kind": "path-problem", "name": "Deserialization of user-controlled data", "precision": "high", "problem.severity": "warning", "security-severity": "9.8"}}, {"id": "js/sensitive-get-query", "name": "js/sensitive-get-query", "shortDescription": {"text": "Sensitive data read from GET request"}, "fullDescription": {"text": "Placing sensitive data in a GET request increases the risk of the data being exposed to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-598"], "description": "Placing sensitive data in a GET request increases the risk of\n              the data being exposed to an attacker.", "id": "js/sensitive-get-query", "kind": "problem", "name": "Sensitive data read from GET request", "precision": "high", "problem.severity": "warning", "security-severity": "6.5"}}, {"id": "js/client-side-unvalidated-url-redirection", "name": "js/client-side-unvalidated-url-redirection", "shortDescription": {"text": "Client-side URL redirect"}, "fullDescription": {"text": "Client-side URL redirection based on unvalidated user input may cause redirection to malicious web sites."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-079", "external/cwe/cwe-116", "external/cwe/cwe-601"], "description": "Client-side URL redirection based on unvalidated user input\n              may cause redirection to malicious web sites.", "id": "js/client-side-unvalidated-url-redirection", "kind": "path-problem", "name": "Client-side URL redirect", "precision": "high", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/server-side-unvalidated-url-redirection", "name": "js/server-side-unvalidated-url-redirection", "shortDescription": {"text": "Server-side URL redirect"}, "fullDescription": {"text": "Server-side URL redirection based on unvalidated user input may cause redirection to malicious web sites."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-601"], "description": "Server-side URL redirection based on unvalidated user input\n              may cause redirection to malicious web sites.", "id": "js/server-side-unvalidated-url-redirection", "kind": "path-problem", "name": "Server-side URL redirect", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/xxe", "name": "js/xxe", "shortDescription": {"text": "XML external entity expansion"}, "fullDescription": {"text": "Parsing user input as an XML document with external entity expansion is vulnerable to XXE attacks."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-611", "external/cwe/cwe-827"], "description": "Parsing user input as an XML document with external\n              entity expansion is vulnerable to XXE attacks.", "id": "js/xxe", "kind": "path-problem", "name": "XML external entity expansion", "precision": "high", "problem.severity": "error", "security-severity": "9.1"}}, {"id": "js/clear-text-cookie", "name": "js/clear-text-cookie", "shortDescription": {"text": "Clear text transmission of sensitive cookie"}, "fullDescription": {"text": "Sending sensitive information in a cookie without requring SSL encryption can expose the cookie to an attacker."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-614", "external/cwe/cwe-311", "external/cwe/cwe-312", "external/cwe/cwe-319"], "description": "Sending sensitive information in a cookie without requring SSL encryption\n              can expose the cookie to an attacker.", "id": "js/clear-text-cookie", "kind": "problem", "name": "Clear text transmission of sensitive cookie", "precision": "high", "problem.severity": "warning", "security-severity": "5.0"}}, {"id": "js/host-header-forgery-in-email-generation", "name": "js/host-header-forgery-in-email-generation", "shortDescription": {"text": "Host header poisoning in email generation"}, "fullDescription": {"text": "Using the HTTP Host header to construct a link in an email can facilitate phishing attacks and leak password reset tokens."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-640"], "description": "Using the HTTP Host header to construct a link in an email can facilitate phishing\n              attacks and leak password reset tokens.", "id": "js/host-header-forgery-in-email-generation", "kind": "path-problem", "name": "Host header poisoning in email generation", "precision": "high", "problem.severity": "error", "security-severity": "9.8"}}, {"id": "js/xpath-injection", "name": "js/xpath-injection", "shortDescription": {"text": "XPath injection"}, "fullDescription": {"text": "Building an XPath expression from user-controlled sources is vulnerable to insertion of malicious code by the user."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-643"], "description": "Building an XPath expression from user-controlled sources is vulnerable to insertion of\n              malicious code by the user.", "id": "js/xpath-injection", "kind": "path-problem", "name": "XPath injection", "precision": "high", "problem.severity": "error", "security-severity": "9.8"}}, {"id": "js/insecure-helmet-configuration", "name": "js/insecure-helmet-configuration", "shortDescription": {"text": "Insecure configuration of Helmet security middleware"}, "fullDescription": {"text": "The Helmet middleware is used to set security-related HTTP headers in Express applications. This query finds instances where the middleware is configured with important security features disabled."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-693", "external/cwe/cwe-1021"], "description": "The Helmet middleware is used to set security-related HTTP headers in Express applications. This query finds instances where the middleware is configured with important security features disabled.", "id": "js/insecure-helmet-configuration", "kind": "problem", "name": "Insecure configuration of Helmet security middleware", "precision": "high", "problem.severity": "error", "security-severity": "7.0"}}, {"id": "js/regex-injection", "name": "js/regex-injection", "shortDescription": {"text": "Regular expression injection"}, "fullDescription": {"text": "User input should not be used in regular expressions without first being escaped, otherwise a malicious user may be able to inject an expression that could require exponential time on certain inputs."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-730", "external/cwe/cwe-400"], "description": "User input should not be used in regular expressions without first being escaped,\n              otherwise a malicious user may be able to inject an expression that could require\n              exponential time on certain inputs.", "id": "js/regex-injection", "kind": "path-problem", "name": "Regular expression injection", "precision": "high", "problem.severity": "error", "security-severity": "7.5"}}, {"id": "js/server-crash", "name": "js/server-crash", "shortDescription": {"text": "Server crash"}, "fullDescription": {"text": "A server that can be forced to crash may be vulnerable to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-248", "external/cwe/cwe-730"], "description": "A server that can be forced to crash may be vulnerable to denial-of-service\n              attacks.", "id": "js/server-crash", "kind": "path-problem", "name": "Server crash", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/unvalidated-dynamic-method-call", "name": "js/unvalidated-dynamic-method-call", "shortDescription": {"text": "Unvalidated dynamic method call"}, "fullDescription": {"text": "Calling a method with a user-controlled name may dispatch to an unexpected target, which could cause an exception."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-754"], "description": "Calling a method with a user-controlled name may dispatch to\n              an unexpected target, which could cause an exception.", "id": "js/unvalidated-dynamic-method-call", "kind": "path-problem", "name": "Unvalidated dynamic method call", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/missing-rate-limiting", "name": "js/missing-rate-limiting", "shortDescription": {"text": "Missing rate limiting"}, "fullDescription": {"text": "An HTTP request handler that performs expensive operations without restricting the rate at which operations can be carried out is vulnerable to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-770", "external/cwe/cwe-307", "external/cwe/cwe-400"], "description": "An HTTP request handler that performs expensive operations without\n              restricting the rate at which operations can be carried out is vulnerable\n              to denial-of-service attacks.", "id": "js/missing-rate-limiting", "kind": "problem", "name": "Missing rate limiting", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/resource-exhaustion", "name": "js/resource-exhaustion", "shortDescription": {"text": "Resource exhaustion"}, "fullDescription": {"text": "Allocating objects or timers with user-controlled sizes or durations can cause resource exhaustion."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-400", "external/cwe/cwe-770"], "description": "Allocating objects or timers with user-controlled\n              sizes or durations can cause resource exhaustion.", "id": "js/resource-exhaustion", "kind": "path-problem", "name": "Resource exhaustion", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/xml-bomb", "name": "js/xml-bomb", "shortDescription": {"text": "XML internal entity expansion"}, "fullDescription": {"text": "Parsing user input as an XML document with arbitrary internal entity expansion is vulnerable to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-776", "external/cwe/cwe-400"], "description": "Parsing user input as an XML document with arbitrary internal\n              entity expansion is vulnerable to denial-of-service attacks.", "id": "js/xml-bomb", "kind": "path-problem", "name": "XML internal entity expansion", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/hardcoded-credentials", "name": "js/hardcoded-credentials", "shortDescription": {"text": "Hard-coded credentials"}, "fullDescription": {"text": "Hard-coding credentials in source code may enable an attacker to gain unauthorized access."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-259", "external/cwe/cwe-321", "external/cwe/cwe-798"], "description": "Hard-coding credentials in source code may enable an attacker\n              to gain unauthorized access.", "id": "js/hardcoded-credentials", "kind": "path-problem", "name": "Hard-coded credentials", "precision": "high", "problem.severity": "warning", "security-severity": "9.8"}}, {"id": "js/insecure-download", "name": "js/insecure-download", "shortDescription": {"text": "Download of sensitive file through insecure connection"}, "fullDescription": {"text": "Downloading executables and other sensitive files over an insecure connection opens up for potential man-in-the-middle attacks."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-829"], "description": "Downloading executables and other sensitive files over an insecure connection\n              opens up for potential man-in-the-middle attacks.", "id": "js/insecure-download", "kind": "path-problem", "name": "Download of sensitive file through insecure connection", "precision": "high", "problem.severity": "error", "security-severity": "8.1"}}, {"id": "js/functionality-from-untrusted-domain", "name": "js/functionality-from-untrusted-domain", "shortDescription": {"text": "Untrusted domain used in script or other content"}, "fullDescription": {"text": "Using a resource from an untrusted or compromised domain makes your code vulnerable to receiving malicious code."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-830"], "description": "Using a resource from an untrusted or compromised domain makes your code vulnerable to receiving malicious code.", "id": "js/functionality-from-untrusted-domain", "kind": "problem", "name": "Untrusted domain used in script or other content", "precision": "high", "problem.severity": "error", "security-severity": "7.2"}}, {"id": "js/functionality-from-untrusted-source", "name": "js/functionality-from-untrusted-source", "shortDescription": {"text": "Inclusion of functionality from an untrusted source"}, "fullDescription": {"text": "Including functionality from an untrusted source may allow an attacker to control the functionality and execute arbitrary code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-830"], "description": "Including functionality from an untrusted source may allow\n              an attacker to control the functionality and execute arbitrary code.", "id": "js/functionality-from-untrusted-source", "kind": "problem", "name": "Inclusion of functionality from an untrusted source", "precision": "high", "problem.severity": "warning", "security-severity": "6.0"}}, {"id": "js/loop-bound-injection", "name": "js/loop-bound-injection", "shortDescription": {"text": "Loop bound injection"}, "fullDescription": {"text": "Iterating over an object with a user-controlled .length property can cause indefinite looping."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-834", "external/cwe/cwe-730"], "description": "Iterating over an object with a user-controlled .length\n              property can cause indefinite looping.", "id": "js/loop-bound-injection", "kind": "path-problem", "name": "Loop bound injection", "precision": "high", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/type-confusion-through-parameter-tampering", "name": "js/type-confusion-through-parameter-tampering", "shortDescription": {"text": "Type confusion through parameter tampering"}, "fullDescription": {"text": "Sanitizing an HTTP request parameter may be ineffective if the user controls its type."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-843"], "description": "Sanitizing an HTTP request parameter may be ineffective if the user controls its type.", "id": "js/type-confusion-through-parameter-tampering", "kind": "path-problem", "name": "Type confusion through parameter tampering", "precision": "high", "problem.severity": "error", "security-severity": "9.8"}}, {"id": "js/prototype-polluting-assignment", "name": "js/prototype-polluting-assignment", "shortDescription": {"text": "Prototype-polluting assignment"}, "fullDescription": {"text": "Modifying an object obtained via a user-controlled property name may lead to accidental mutation of the built-in Object prototype, and possibly escalate to remote code execution or cross-site scripting."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-078", "external/cwe/cwe-079", "external/cwe/cwe-094", "external/cwe/cwe-400", "external/cwe/cwe-471", "external/cwe/cwe-915"], "description": "Modifying an object obtained via a user-controlled property name may\n              lead to accidental mutation of the built-in Object prototype,\n              and possibly escalate to remote code execution or cross-site scripting.", "id": "js/prototype-polluting-assignment", "kind": "path-problem", "name": "Prototype-polluting assignment", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/prototype-pollution-utility", "name": "js/prototype-pollution-utility", "shortDescription": {"text": "Prototype-polluting function"}, "fullDescription": {"text": "Functions recursively assigning properties on objects may be the cause of accidental modification of a built-in prototype object."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-078", "external/cwe/cwe-079", "external/cwe/cwe-094", "external/cwe/cwe-400", "external/cwe/cwe-471", "external/cwe/cwe-915"], "description": "Functions recursively assigning properties on objects may be\n              the cause of accidental modification of a built-in prototype object.", "id": "js/prototype-pollution-utility", "kind": "path-problem", "name": "Prototype-polluting function", "precision": "high", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/prototype-pollution", "name": "js/prototype-pollution", "shortDescription": {"text": "Prototype-polluting merge call"}, "fullDescription": {"text": "Recursively merging a user-controlled object into another object can allow an attacker to modify the built-in Object prototype, and possibly escalate to remote code execution or cross-site scripting."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-078", "external/cwe/cwe-079", "external/cwe/cwe-094", "external/cwe/cwe-400", "external/cwe/cwe-471", "external/cwe/cwe-915"], "description": "Recursively merging a user-controlled object into another object\n              can allow an attacker to modify the built-in Object prototype,\n              and possibly escalate to remote code execution or cross-site scripting.", "id": "js/prototype-pollution", "kind": "path-problem", "name": "Prototype-polluting merge call", "precision": "high", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/insufficient-password-hash", "name": "js/insufficient-password-hash", "shortDescription": {"text": "Use of password hash with insufficient computational effort"}, "fullDescription": {"text": "Creating a hash of a password with low computational effort makes the hash vulnerable to password cracking attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-916"], "description": "Creating a hash of a password with low computational effort makes the hash vulnerable to password cracking attacks.", "id": "js/insufficient-password-hash", "kind": "path-problem", "name": "Use of password hash with insufficient computational effort", "precision": "high", "problem.severity": "warning", "security-severity": "8.1"}}, {"id": "js/request-forgery", "name": "js/request-forgery", "shortDescription": {"text": "Server-side request forgery"}, "fullDescription": {"text": "Making a network request with user-controlled data in the URL allows for request forgery attacks."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-918"], "description": "Making a network request with user-controlled data in the URL allows for request forgery attacks.", "id": "js/request-forgery", "kind": "path-problem", "name": "Server-side request forgery", "precision": "high", "problem.severity": "error", "security-severity": "9.1"}}, {"id": "js/misleading-indentation-of-dangling-else", "name": "js/misleading-indentation-of-dangling-else", "shortDescription": {"text": "Misleading indentation of dangling 'else'"}, "fullDescription": {"text": "The 'else' clause of an 'if' statement should be aligned with the 'if' it belongs to."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["readability", "statistical", "non-attributable", "external/cwe/cwe-483"], "description": "The 'else' clause of an 'if' statement should be aligned with the 'if' it belongs to.", "id": "js/misleading-indentation-of-dangling-else", "kind": "problem", "name": "Misleading indentation of dangling 'else'", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/ignore-array-result", "name": "js/ignore-array-result", "shortDescription": {"text": "Ignoring result from pure array method"}, "fullDescription": {"text": "Ignoring the result of an array method that does not modify its receiver is generally an error."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness"], "description": "Ignoring the result of an array method that does not modify its receiver is generally an error.", "id": "js/ignore-array-result", "kind": "problem", "name": "Ignoring result from pure array method", "precision": "high", "problem.severity": "warning"}}, {"id": "js/inconsistent-loop-direction", "name": "js/inconsistent-loop-direction", "shortDescription": {"text": "Inconsistent direction of for loop"}, "fullDescription": {"text": "A 'for' loop that increments its loop variable but checks it against a lower bound, or decrements its loop variable but checks it against an upper bound, will either stop iterating immediately or keep iterating indefinitely, and is usually indicative of a typo."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["correctness", "external/cwe/cwe-835"], "description": "A 'for' loop that increments its loop variable but checks it\n              against a lower bound, or decrements its loop variable but\n              checks it against an upper bound, will either stop iterating\n              immediately or keep iterating indefinitely, and is usually\n              indicative of a typo.", "id": "js/inconsistent-loop-direction", "kind": "problem", "name": "Inconsistent direction of for loop", "precision": "very-high", "problem.severity": "error"}}, {"id": "js/label-in-switch", "name": "js/label-in-switch", "shortDescription": {"text": "Non-case label in switch statement"}, "fullDescription": {"text": "A non-case label appearing in a switch statement that is textually aligned with a case label is confusing to read, or may even indicate a bug."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "readability"], "description": "A non-case label appearing in a switch statement that is textually aligned with a case\n              label is confusing to read, or may even indicate a bug.", "id": "js/label-in-switch", "kind": "problem", "name": "Non-case label in switch statement", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/loop-iteration-skipped-due-to-shifting", "name": "js/loop-iteration-skipped-due-to-shifting", "shortDescription": {"text": "Loop iteration skipped due to shifting"}, "fullDescription": {"text": "Removing elements from an array while iterating over it can cause the loop to skip over some elements, unless the loop index is decremented accordingly."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "Removing elements from an array while iterating over it can cause the loop to skip over some elements,\n              unless the loop index is decremented accordingly.", "id": "js/loop-iteration-skipped-due-to-shifting", "kind": "problem", "name": "Loop iteration skipped due to shifting", "precision": "high", "problem.severity": "warning"}}, {"id": "js/misleading-indentation-after-control-statement", "name": "js/misleading-indentation-after-control-statement", "shortDescription": {"text": "Misleading indentation after control statement"}, "fullDescription": {"text": "The body of a control statement should have appropriate indentation to clarify which statements it controls and which ones it does not control."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "statistical", "non-attributable", "external/cwe/cwe-483"], "description": "The body of a control statement should have appropriate indentation to clarify which\n              statements it controls and which ones it does not control.", "id": "js/misleading-indentation-after-control-statement", "kind": "problem", "name": "Misleading indentation after control statement", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/useless-assignment-in-return", "name": "js/useless-assignment-in-return", "shortDescription": {"text": "Return statement assigns local variable"}, "fullDescription": {"text": "An assignment to a local variable in a return statement is useless, since the variable will immediately go out of scope and its value is lost."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "readability", "external/cwe/cwe-563"], "description": "An assignment to a local variable in a return statement is useless, since the variable will\n              immediately go out of scope and its value is lost.", "id": "js/useless-assignment-in-return", "kind": "problem", "name": "Return statement assigns local variable", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/unused-loop-variable", "name": "js/unused-loop-variable", "shortDescription": {"text": "Unused loop iteration variable"}, "fullDescription": {"text": "A loop iteration variable is unused, which suggests an error."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["maintainability", "correctness"], "description": "A loop iteration variable is unused, which suggests an error.", "id": "js/unused-loop-variable", "kind": "problem", "name": "Unused loop iteration variable", "precision": "high", "problem.severity": "error"}}, {"id": "js/unreachable-statement", "name": "js/unreachable-statement", "shortDescription": {"text": "Unreachable statement"}, "fullDescription": {"text": "Unreachable statements are often indicative of missing code or latent bugs and should be avoided."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness", "external/cwe/cwe-561"], "description": "Unreachable statements are often indicative of missing code or latent bugs and should be avoided.", "id": "js/unreachable-statement", "kind": "problem", "name": "Unreachable statement", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/useless-comparison-test", "name": "js/useless-comparison-test", "shortDescription": {"text": "Useless comparison test"}, "fullDescription": {"text": "A comparison that always evaluates to true or always evaluates to false may indicate faulty logic and dead code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness"], "description": "A comparison that always evaluates to true or always evaluates to false may\n              indicate faulty logic and dead code.", "id": "js/useless-comparison-test", "kind": "problem", "name": "Useless comparison test", "precision": "high", "problem.severity": "warning"}}, {"id": "js/trivial-conditional", "name": "js/trivial-conditional", "shortDescription": {"text": "Useless conditional"}, "fullDescription": {"text": "If a conditional expression always evaluates to true or always evaluates to false, this suggests incomplete code or a logic error."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "external/cwe/cwe-570", "external/cwe/cwe-571"], "description": "If a conditional expression always evaluates to true or always\n              evaluates to false, this suggests incomplete code or a logic\n              error.", "id": "js/trivial-conditional", "kind": "problem", "name": "Useless conditional", "precision": "very-high", "problem.severity": "warning"}}, {"id": "js/use-of-returnless-function", "name": "js/use-of-returnless-function", "shortDescription": {"text": "Use of returnless function"}, "fullDescription": {"text": "Using the return value of a function that does not return an expression is indicative of a mistake."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["maintainability", "correctness"], "description": "Using the return value of a function that does not return an expression is indicative of a mistake.", "id": "js/use-of-returnless-function", "kind": "problem", "name": "Use of returnless function", "precision": "high", "problem.severity": "warning"}}, {"id": "js/vue/arrow-method-on-vue-instance", "name": "js/vue/arrow-method-on-vue-instance", "shortDescription": {"text": "Arrow method on Vue instance"}, "fullDescription": {"text": "An arrow method on a Vue instance doesn't have its `this` variable bound to the Vue instance."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["reliability", "frameworks/vue"], "description": "An arrow method on a Vue instance doesn't have its `this` variable bound to the Vue instance.", "id": "js/vue/arrow-method-on-vue-instance", "kind": "problem", "name": "Arrow method on Vue instance", "precision": "high", "problem.severity": "warning"}}, {"id": "js/missing-origin-check", "name": "js/missing-origin-check", "shortDescription": {"text": "Missing origin verification in `postMessage` handler"}, "fullDescription": {"text": "Missing origin verification in a `postMessage` handler allows any windows to send arbitrary data to the handler."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020", "external/cwe/cwe-940"], "description": "Missing origin verification in a `postMessage` handler allows any windows to send arbitrary data to the handler.", "id": "js/missing-origin-check", "kind": "problem", "name": "Missing origin verification in `postMessage` handler", "precision": "medium", "problem.severity": "warning", "security-severity": "5"}}, {"id": "js/regex/missing-regexp-anchor", "name": "js/regex/missing-regexp-anchor", "shortDescription": {"text": "Missing regular expression anchor"}, "fullDescription": {"text": "Regular expressions without anchors can be vulnerable to bypassing."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-020"], "description": "Regular expressions without anchors can be vulnerable to bypassing.", "id": "js/regex/missing-regexp-anchor", "kind": "problem", "name": "Missing regular expression anchor", "precision": "medium", "problem.severity": "warning", "security-severity": "7.8"}}, {"id": "js/indirect-command-line-injection", "name": "js/indirect-command-line-injection", "shortDescription": {"text": "Indirect uncontrolled command line"}, "fullDescription": {"text": "Forwarding command-line arguments to a child process executed within a shell may indirectly introduce command-line injection vulnerabilities."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["correctness", "security", "external/cwe/cwe-078", "external/cwe/cwe-088"], "description": "Forwarding command-line arguments to a child process\n              executed within a shell may indirectly introduce\n              command-line injection vulnerabilities.", "id": "js/indirect-command-line-injection", "kind": "path-problem", "name": "Indirect uncontrolled command line", "precision": "medium", "problem.severity": "warning", "security-severity": "6.3"}}, {"id": "js/unsafe-code-construction", "name": "js/unsafe-code-construction", "shortDescription": {"text": "Unsafe code constructed from library input"}, "fullDescription": {"text": "Using externally controlled strings to construct code may allow a malicious user to execute arbitrary code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-094", "external/cwe/cwe-079", "external/cwe/cwe-116"], "description": "Using externally controlled strings to construct code may allow a malicious\n              user to execute arbitrary code.", "id": "js/unsafe-code-construction", "kind": "path-problem", "name": "Unsafe code constructed from library input", "precision": "medium", "problem.severity": "warning", "security-severity": "6.1"}}, {"id": "js/log-injection", "name": "js/log-injection", "shortDescription": {"text": "Log injection"}, "fullDescription": {"text": "Building log entries from user-controlled sources is vulnerable to insertion of forged log entries by a malicious user."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-117"], "description": "Building log entries from user-controlled sources is vulnerable to\n              insertion of forged log entries by a malicious user.", "id": "js/log-injection", "kind": "path-problem", "name": "Log injection", "precision": "medium", "problem.severity": "error", "security-severity": "6.1"}}, {"id": "js/samesite-none-cookie", "name": "js/samesite-none-cookie", "shortDescription": {"text": "Sensitive cookie without SameSite restrictions"}, "fullDescription": {"text": "Sensitive cookies where the SameSite attribute is set to \"None\" can in some cases allow for Cross-Site Request Forgery (CSRF) attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-1275"], "description": "Sensitive cookies where the SameSite attribute is set to \"None\" can\n              in some cases allow for Cross-Site Request Forgery (CSRF) attacks.", "id": "js/samesite-none-cookie", "kind": "problem", "name": "Sensitive cookie without SameSite restrictions", "precision": "medium", "problem.severity": "warning", "security-severity": "5.0"}}, {"id": "js/file-access-to-http", "name": "js/file-access-to-http", "shortDescription": {"text": "File data in outbound network request"}, "fullDescription": {"text": "Directly sending file data in an outbound network request can indicate unauthorized information disclosure."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-200"], "description": "Directly sending file data in an outbound network request can indicate unauthorized information disclosure.", "id": "js/file-access-to-http", "kind": "path-problem", "name": "File data in outbound network request", "precision": "medium", "problem.severity": "warning", "security-severity": "6.5"}}, {"id": "js/password-in-configuration-file", "name": "js/password-in-configuration-file", "shortDescription": {"text": "Password in configuration file"}, "fullDescription": {"text": "Storing unencrypted passwords in configuration files is unsafe."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-256", "external/cwe/cwe-260", "external/cwe/cwe-313", "external/cwe/cwe-522"], "description": "Storing unencrypted passwords in configuration files is unsafe.", "id": "js/password-in-configuration-file", "kind": "problem", "name": "Password in configuration file", "precision": "medium", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/file-system-race", "name": "js/file-system-race", "shortDescription": {"text": "Potential file system race condition"}, "fullDescription": {"text": "Separately checking the state of a file before operating on it may allow an attacker to modify the file between the two operations."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-367"], "description": "Separately checking the state of a file before operating\n              on it may allow an attacker to modify the file between\n              the two operations.", "id": "js/file-system-race", "kind": "problem", "name": "Potential file system race condition", "precision": "medium", "problem.severity": "warning", "security-severity": "7.7"}}, {"id": "js/insecure-temporary-file", "name": "js/insecure-temporary-file", "shortDescription": {"text": "Insecure temporary file"}, "fullDescription": {"text": "Creating a temporary file that is accessible by other users can lead to information disclosure and sometimes remote code execution."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["external/cwe/cwe-377", "external/cwe/cwe-378", "security"], "description": "Creating a temporary file that is accessible by other users can\n lead to information disclosure and sometimes remote code execution.", "id": "js/insecure-temporary-file", "kind": "path-problem", "name": "Insecure temporary file", "precision": "medium", "problem.severity": "warning", "security-severity": "7.0"}}, {"id": "js/session-fixation", "name": "js/session-fixation", "shortDescription": {"text": "Failure to abandon session"}, "fullDescription": {"text": "Reusing an existing session as a different user could allow an attacker to access someone else's account by using their session."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-384"], "description": "Reusing an existing session as a different user could allow\n              an attacker to access someone else's account by using\n              their session.", "id": "js/session-fixation", "kind": "problem", "name": "Failure to abandon session", "precision": "medium", "problem.severity": "warning", "security-severity": "5"}}, {"id": "js/remote-property-injection", "name": "js/remote-property-injection", "shortDescription": {"text": "Remote property injection"}, "fullDescription": {"text": "Allowing writes to arbitrary properties of an object may lead to denial-of-service attacks."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-250", "external/cwe/cwe-400"], "description": "Allowing writes to arbitrary properties of an object may lead to\n              denial-of-service attacks.", "id": "js/remote-property-injection", "kind": "path-problem", "name": "Remote property injection", "precision": "medium", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/hardcoded-data-interpreted-as-code", "name": "js/hardcoded-data-interpreted-as-code", "shortDescription": {"text": "Hard-coded data interpreted as code"}, "fullDescription": {"text": "Transforming hard-coded data (such as hexadecimal constants) into code to be executed is a technique often associated with backdoors and should be avoided."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-506"], "description": "Transforming hard-coded data (such as hexadecimal constants) into code\n              to be executed is a technique often associated with backdoors and should\n              be avoided.", "id": "js/hardcoded-data-interpreted-as-code", "kind": "path-problem", "name": "Hard-coded data interpreted as code", "precision": "medium", "problem.severity": "error", "security-severity": "9.1"}}, {"id": "js/user-controlled-bypass", "name": "js/user-controlled-bypass", "shortDescription": {"text": "User-controlled bypass of security check"}, "fullDescription": {"text": "Conditions that the user controls are not suited for making security-related decisions."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-807", "external/cwe/cwe-290"], "description": "Conditions that the user controls are not suited for making security-related decisions.", "id": "js/user-controlled-bypass", "kind": "path-problem", "name": "User-controlled bypass of security check", "precision": "medium", "problem.severity": "error", "security-severity": "7.8"}}, {"id": "js/empty-password-in-configuration-file", "name": "js/empty-password-in-configuration-file", "shortDescription": {"text": "Empty password in configuration file"}, "fullDescription": {"text": "Failing to set a password reduces the security of your code."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-258", "external/cwe/cwe-862"], "description": "Failing to set a password reduces the security of your code.", "id": "js/empty-password-in-configuration-file", "kind": "problem", "name": "Empty password in configuration file", "precision": "medium", "problem.severity": "warning", "security-severity": "7.5"}}, {"id": "js/http-to-file-access", "name": "js/http-to-file-access", "shortDescription": {"text": "Network data written to file"}, "fullDescription": {"text": "Writing network data directly to the file system allows arbitrary file upload and might indicate a backdoor."}, "defaultConfiguration": {"enabled": true, "level": "warning"}, "properties": {"tags": ["security", "external/cwe/cwe-912", "external/cwe/cwe-434"], "description": "Writing network data directly to the file system allows arbitrary file upload and might indicate a backdoor.", "id": "js/http-to-file-access", "kind": "path-problem", "name": "Network data written to file", "precision": "medium", "problem.severity": "warning", "security-severity": "6.3"}}, {"id": "js/client-side-request-forgery", "name": "js/client-side-request-forgery", "shortDescription": {"text": "Client-side request forgery"}, "fullDescription": {"text": "Making a client-to-server request with user-controlled data in the URL allows a request forgery attack against the client."}, "defaultConfiguration": {"enabled": true, "level": "error"}, "properties": {"tags": ["security", "external/cwe/cwe-918"], "description": "Making a client-to-server request with user-controlled data in the URL allows a request forgery attack\n              against the client.", "id": "js/client-side-request-forgery", "kind": "path-problem", "name": "Client-side request forgery", "precision": "medium", "problem.severity": "error", "security-severity": "5.0"}}, {"id": "js/summary/lines-of-code", "name": "js/summary/lines-of-code", "shortDescription": {"text": "Total lines of JavaScript and TypeScript code in the database"}, "fullDescription": {"text": "The total number of lines of JavaScript or TypeScript code across all files checked into the repository, except in `node_modules`. This is a useful metric of the size of a database. For all files that were seen during extraction, this query counts the lines of code, excluding whitespace or comments."}, "defaultConfiguration": {"enabled": true}, "properties": {"tags": ["summary", "telemetry"], "description": "The total number of lines of JavaScript or TypeScript code across all files checked into the repository, except in `node_modules`. This is a useful metric of the size of a database. For all files that were seen during extraction, this query counts the lines of code, excluding whitespace or comments.", "id": "js/summary/lines-of-code", "kind": "metric", "name": "Total lines of JavaScript and TypeScript code in the database"}}, {"id": "js/summary/lines-of-user-code", "name": "js/summary/lines-of-user-code", "shortDescription": {"text": "Total lines of user written JavaScript and TypeScript code in the database"}, "fullDescription": {"text": "The total number of lines of JavaScript and TypeScript code from the source code directory, excluding auto-generated files and files in `node_modules`. This query counts the lines of code, excluding whitespace or comments."}, "defaultConfiguration": {"enabled": true}, "properties": {"tags": ["summary", "lines-of-code", "debug"], "description": "The total number of lines of JavaScript and TypeScript code from the source code directory,\n   excluding auto-generated files and files in `node_modules`. This query counts the lines of code, excluding\n   whitespace or comments.", "id": "js/summary/lines-of-user-code", "kind": "metric", "name": "Total lines of user written JavaScript and TypeScript code in the database"}}]}, "extensions": [{"name": "codeql/javascript-queries", "semanticVersion": "1.6.0+4bb829ebec2082e8c06f02a7d9c07e181d27c784", "locations": [{"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/", "description": {"text": "The QL pack root directory."}, "properties": {"tags": ["CodeQL/LocalPackRoot"]}}, {"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/qlpack.yml", "description": {"text": "The QL pack definition file."}, "properties": {"tags": ["CodeQL/LocalPackDefinitionFile"]}}]}, {"name": "codeql/javascript-all", "semanticVersion": "2.6.3+4bb829ebec2082e8c06f02a7d9c07e181d27c784", "locations": [{"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/.codeql/libraries/codeql/javascript-all/2.6.3/", "description": {"text": "The QL pack root directory."}, "properties": {"tags": ["CodeQL/LocalPackRoot"]}}, {"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/.codeql/libraries/codeql/javascript-all/2.6.3/qlpack.yml", "description": {"text": "The QL pack definition file."}, "properties": {"tags": ["CodeQL/LocalPackDefinitionFile"]}}]}, {"name": "codeql/threat-models", "semanticVersion": "1.0.23+4bb829ebec2082e8c06f02a7d9c07e181d27c784", "locations": [{"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/.codeql/libraries/codeql/threat-models/1.0.23/", "description": {"text": "The QL pack root directory."}, "properties": {"tags": ["CodeQL/LocalPackRoot"]}}, {"uri": "file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/javascript-queries/1.6.0/.codeql/libraries/codeql/threat-models/1.0.23/qlpack.yml", "description": {"text": "The QL pack definition file."}, "properties": {"tags": ["CodeQL/LocalPackDefinitionFile"]}}]}]}, "invocations": [{"toolExecutionNotifications": [{"locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}}}], "message": {"text": ""}, "level": "none", "descriptor": {"id": "js/diagnostics/successfully-extracted-files", "index": 0}, "properties": {"formattedMessage": {"text": ""}}}, {"locations": [{"physicalLocation": {"artifactLocation": {"uri": "package.json", "uriBaseId": "%SRCROOT%", "index": 2}}}], "message": {"text": ""}, "level": "none", "descriptor": {"id": "js/diagnostics/successfully-extracted-files", "index": 0}, "properties": {"formattedMessage": {"text": ""}}}, {"locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}}}], "message": {"text": ""}, "level": "none", "descriptor": {"id": "js/diagnostics/successfully-extracted-files", "index": 0}, "properties": {"formattedMessage": {"text": ""}}}, {"locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}}}], "message": {"text": ""}, "level": "none", "descriptor": {"id": "js/baseline/expected-extracted-files", "index": 2}, "properties": {"formattedMessage": {"text": ""}}}, {"locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}}}], "message": {"text": ""}, "level": "none", "descriptor": {"id": "js/baseline/expected-extracted-files", "index": 2}, "properties": {"formattedMessage": {"text": ""}}}], "executionSuccessful": true}], "artifacts": [{"location": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}}, {"location": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}}, {"location": {"uri": "package.json", "uriBaseId": "%SRCROOT%", "index": 2}}], "results": [{"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable mysql."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 2, "startColumn": 7, "endColumn": 12}}}], "partialFingerprints": {"primaryLocationLineHash": "44eaf9157767cb12:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable bcrypt."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 3, "startColumn": 7, "endColumn": 13}}}], "partialFingerprints": {"primaryLocationLineHash": "815e1f0c72befd44:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable jwt."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 4, "startColumn": 7, "endColumn": 10}}}], "partialFingerprints": {"primaryLocationLineHash": "f260e7018f8a698f:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused function maskSensitiveData."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 88, "startColumn": 10, "endColumn": 27}}}], "partialFingerprints": {"primaryLocationLineHash": "ada71fe6b284b4d6:1", "primaryLocationStartColumnFingerprint": "9"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable MASK_VAR."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 90, "startColumn": 11, "endColumn": 19}}}], "partialFingerprints": {"primaryLocationLineHash": "62d690f2466bd78:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable CONFIG_TEMPLATE."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 95, "startColumn": 7, "endColumn": 22}}}], "partialFingerprints": {"primaryLocationLineHash": "a97cc75f6bd7988a:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable ANOTHER_VAL."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 7, "startColumn": 7, "endColumn": 18}}}], "partialFingerprints": {"primaryLocationLineHash": "cb16ce07d187a034:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable sensitiveData."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 10, "startColumn": 5, "endColumn": 18}}}], "partialFingerprints": {"primaryLocationLineHash": "7a0e47a36f5999f0:1", "primaryLocationStartColumnFingerprint": "4"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable hackerString."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 11, "startColumn": 5, "endColumn": 17}}}], "partialFingerprints": {"primaryLocationLineHash": "5fb874bc278d072f:1", "primaryLocationStartColumnFingerprint": "4"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable MASK_VAR."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 16, "startColumn": 11, "endColumn": 19}}}], "partialFingerprints": {"primaryLocationLineHash": "a1c0b632d8461cf6:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused variable configTemplate."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 21, "startColumn": 7, "endColumn": 21}}}], "partialFingerprints": {"primaryLocationLineHash": "7e81adc840663a6a:1", "primaryLocationStartColumnFingerprint": "6"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused function anotherVulnerableQuery."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 35, "startColumn": 10, "endColumn": 32}}}], "partialFingerprints": {"primaryLocationLineHash": "77c997c87d58aee9:1", "primaryLocationStartColumnFingerprint": "9"}}, {"ruleId": "js/unused-local-variable", "ruleIndex": 27, "rule": {"id": "js/unused-local-variable", "index": 27}, "message": {"text": "Unused function anotherVulnerableCommand."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "test-vulnerabilities.js", "uriBaseId": "%SRCROOT%", "index": 1}, "region": {"startLine": 45, "startColumn": 10, "endColumn": 34}}}], "partialFingerprints": {"primaryLocationLineHash": "f6898b4354f78cc0:1", "primaryLocationStartColumnFingerprint": "9"}}, {"ruleId": "js/path-injection", "ruleIndex": 99, "rule": {"id": "js/path-injection", "index": 99}, "message": {"text": "This path depends on a [user-provided value](1)."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 52, "startColumn": 17, "endColumn": 25}}}], "partialFingerprints": {"primaryLocationLineHash": "7e55988e74196212:1", "primaryLocationStartColumnFingerprint": "12"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 48, "startColumn": 22, "endColumn": 41}}, "message": {"text": "req.params.filename"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 48, "startColumn": 11, "endColumn": 41}}, "message": {"text": "filename"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 51, "startColumn": 35, "endColumn": 43}}, "message": {"text": "filename"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 51, "startColumn": 22, "endColumn": 45}}, "message": {"text": "`./uplo ... ename}`"}}, "taxa": [{"id": "Config", "properties": {"CodeQL/DataflowRole": "step"}}]}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 51, "startColumn": 11, "endColumn": 45}}, "message": {"text": "filePath"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 52, "startColumn": 17, "endColumn": 25}}, "message": {"text": "filePath"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 48, "startColumn": 22, "endColumn": 41}}, "message": {"text": "user-provided value"}}]}, {"ruleId": "js/command-line-injection", "ruleIndex": 102, "rule": {"id": "js/command-line-injection", "index": 102}, "message": {"text": "This command line depends on a [user-provided value](1)."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 37, "startColumn": 10, "endColumn": 17}}}], "partialFingerprints": {"primaryLocationLineHash": "d87d6e77279bbf56:1", "primaryLocationStartColumnFingerprint": "5"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 33, "startColumn": 26, "endColumn": 34}}, "message": {"text": "req.body"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 33, "startColumn": 11, "endColumn": 23}}, "message": {"text": "{ filename }"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 33, "startColumn": 11, "endColumn": 34}}, "message": {"text": "filename"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 36, "startColumn": 40, "endColumn": 48}}, "message": {"text": "filename"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 36, "startColumn": 11, "endColumn": 64}}, "message": {"text": "command"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 37, "startColumn": 10, "endColumn": 17}}, "message": {"text": "command"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 33, "startColumn": 26, "endColumn": 34}}, "message": {"text": "user-provided value"}}]}, {"ruleId": "js/code-injection", "ruleIndex": 115, "rule": {"id": "js/code-injection", "index": 115}, "message": {"text": "This code execution depends on a [user-provided value](1)."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 137, "startColumn": 29, "endColumn": 40}}}], "partialFingerprints": {"primaryLocationLineHash": "d6267749ab7cd6e:1", "primaryLocationStartColumnFingerprint": "20"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 133, "startColumn": 22, "endColumn": 30}}, "message": {"text": "req.body"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 133, "startColumn": 11, "endColumn": 19}}, "message": {"text": "{ data }"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 133, "startColumn": 11, "endColumn": 30}}, "message": {"text": "data"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 137, "startColumn": 33, "endColumn": 37}}, "message": {"text": "data"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 137, "startColumn": 29, "endColumn": 40}}, "message": {"text": "`(${data})`"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 133, "startColumn": 22, "endColumn": 30}}, "message": {"text": "user-provided value"}}]}, {"ruleId": "js/clear-text-logging", "ruleIndex": 135, "rule": {"id": "js/clear-text-logging", "index": 135}, "message": {"text": "This logs sensitive data returned by [an access to password](1) as clear text."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 27, "startColumn": 37, "endColumn": 42}}}], "partialFingerprints": {"primaryLocationLineHash": "2967a1d8dfe696d:1", "primaryLocationStartColumnFingerprint": "32"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 89, "endColumn": 97}}, "message": {"text": "password"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 11, "endColumn": 100}}, "message": {"text": "query"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 27, "startColumn": 37, "endColumn": 42}}, "message": {"text": "query"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 89, "endColumn": 97}}, "message": {"text": "an access to password"}}]}, {"ruleId": "js/missing-rate-limiting", "ruleIndex": 157, "rule": {"id": "js/missing-rate-limiting", "index": 157}, "message": {"text": "This route handler performs [a system command](1), but is not rate-limited."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 32, "startColumn": 21, "endLine": 44, "endColumn": 2}}}], "partialFingerprints": {"primaryLocationLineHash": "206927734519811c:1", "primaryLocationStartColumnFingerprint": "20"}, "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 37, "startColumn": 5, "endLine": 43, "endColumn": 7}}, "message": {"text": "a system command"}}]}, {"ruleId": "js/missing-rate-limiting", "ruleIndex": 157, "rule": {"id": "js/missing-rate-limiting", "index": 157}, "message": {"text": "This route handler performs [a file system access](1), but is not rate-limited."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 47, "startColumn": 28, "endLine": 59, "endColumn": 2}}}], "partialFingerprints": {"primaryLocationLineHash": "71d75c155f75a7fc:1", "primaryLocationStartColumnFingerprint": "27"}, "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 52, "startColumn": 5, "endLine": 58, "endColumn": 7}}, "message": {"text": "a file system access"}}]}, {"ruleId": "js/prototype-pollution-utility", "ruleIndex": 167, "rule": {"id": "js/prototype-pollution-utility", "index": 167}, "message": {"text": "Properties are copied from [source](1) to [target](2) without guarding against prototype pollution."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 121, "startColumn": 17, "endColumn": 23}}}], "partialFingerprints": {"primaryLocationLineHash": "6c2eb7006c612bed:1", "primaryLocationStartColumnFingerprint": "0"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 116, "startColumn": 18, "endColumn": 21}}, "message": {"text": "key"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 119, "startColumn": 30, "endColumn": 33}}, "message": {"text": "key"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 119, "startColumn": 23, "endColumn": 34}}, "message": {"text": "target[key]"}}, "taxa": [{"id": "Config", "properties": {"CodeQL/DataflowRole": "step"}}]}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 115, "startColumn": 20, "endColumn": 26}}, "message": {"text": "target"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 121, "startColumn": 17, "endColumn": 23}}, "message": {"text": "target"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 116, "startColumn": 25, "endColumn": 31}}, "message": {"text": "source"}}, {"id": 2, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 121, "startColumn": 17, "endColumn": 23}}, "message": {"text": "target"}}]}, {"ruleId": "js/log-injection", "ruleIndex": 188, "rule": {"id": "js/log-injection", "index": 188}, "message": {"text": "Log entry depends on a [user-provided value](1)."}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 27, "startColumn": 37, "endColumn": 42}}}], "partialFingerprints": {"primaryLocationLineHash": "2967a1d8dfe696d:1", "primaryLocationStartColumnFingerprint": "32"}, "codeFlows": [{"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 36, "endColumn": 44}}, "message": {"text": "req.body"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 11, "endColumn": 33}}, "message": {"text": "{ usern ... sword }"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 11, "endColumn": 44}}, "message": {"text": "username"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 60, "endColumn": 68}}, "message": {"text": "username"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 11, "endColumn": 100}}, "message": {"text": "query"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 27, "startColumn": 37, "endColumn": 42}}, "message": {"text": "query"}}}]}]}, {"threadFlows": [{"locations": [{"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 36, "endColumn": 44}}, "message": {"text": "req.body"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 11, "endColumn": 33}}, "message": {"text": "{ usern ... sword }"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 11, "endColumn": 44}}, "message": {"text": "password"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 89, "endColumn": 97}}, "message": {"text": "password"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 24, "startColumn": 11, "endColumn": 100}}, "message": {"text": "query"}}}, {"location": {"physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 27, "startColumn": 37, "endColumn": 42}}, "message": {"text": "query"}}}]}]}], "relatedLocations": [{"id": 1, "physicalLocation": {"artifactLocation": {"uri": "app.js", "uriBaseId": "%SRCROOT%", "index": 0}, "region": {"startLine": 21, "startColumn": 36, "endColumn": 44}}, "message": {"text": "user-provided value"}}]}], "newlineSequences": ["\r\n", "\n", " ", " "], "columnKind": "utf16CodeUnits", "properties": {"semmle.formatSpecifier": "sarif-latest", "metricResults": [{"rule": {"id": "js/summary/lines-of-code", "index": 201}, "ruleId": "js/summary/lines-of-code", "ruleIndex": 201, "value": 180}, {"rule": {"id": "js/summary/lines-of-user-code", "index": 202}, "ruleId": "js/summary/lines-of-user-code", "ruleIndex": 202, "value": 180, "baseline": 180}]}}]}