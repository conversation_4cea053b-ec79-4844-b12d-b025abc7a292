/**
 * @name Custom Vulnerability Detection - Hardcoded Sensitive Values
 * @description Detects hardcoded sensitive values like "I am hacker's paradise"
 * @kind problem
 * @problem.severity error
 * @security-severity 8.0
 * @precision high
 * @id js/hardcoded-sensitive-value
 * @tags security
 *       external/cwe/cwe-798
 */

import javascript

/**
 * Predicate to identify suspicious string literals that contain sensitive patterns
 */
predicate isSensitiveString(StringLiteral str) {
  str.getValue().toLowerCase().matches("%hacker%paradise%") or
  str.getValue().toLowerCase().matches("%i am hacker%") or
  str.getValue() = "I am hacker's paradise"
}

/**
 * Predicate to identify variable assignments with sensitive values
 */
predicate isSensitiveAssignment(AssignExpr assign, StringLiteral str) {
  assign.getRhs() = str and
  isSensitiveString(str) and
  // Exclude false positives: comments, templates, and masking functions
  not exists(Comment c | c.getText().matches("%template%") or c.getText().matches("%MASKED%")) and
  not exists(Function f | 
    f.getName().toLowerCase().matches("%mask%") and
    assign.getContainer() = f
  )
}

/**
 * Predicate to identify variable declarations with sensitive values
 */
predicate isSensitiveVarDecl(VarDecl decl, StringLiteral str) {
  decl.getInit() = str and
  isSensitiveString(str) and
  // Exclude false positives
  not exists(Comment c | 
    c.getLocation().getStartLine() = decl.getLocation().getStartLine() and
    (c.getText().matches("%template%") or c.getText().matches("%MASKED%"))
  ) and
  not exists(Function f | 
    f.getName().toLowerCase().matches("%mask%") and
    decl.getContainer() = f
  )
}

from ASTNode node, StringLiteral str, string message
where 
  (
    // Case 1: Variable assignment
    exists(AssignExpr assign | 
      isSensitiveAssignment(assign, str) and
      node = assign and
      message = "Hardcoded sensitive value found in assignment: '" + str.getValue() + "'"
    )
  ) or (
    // Case 2: Variable declaration
    exists(VarDecl decl | 
      isSensitiveVarDecl(decl, str) and
      node = decl and
      message = "Hardcoded sensitive value found in variable declaration: '" + str.getValue() + "'"
    )
  ) or (
    // Case 3: Direct string usage in sensitive contexts
    exists(CallExpr call |
      (call.getCalleeName() = "log" or call.getCalleeName() = "console.log") and
      call.getAnArgument() = str and
      isSensitiveString(str) and
      node = call and
      message = "Sensitive value exposed in logging: '" + str.getValue() + "'"
    )
  )
select node, message
