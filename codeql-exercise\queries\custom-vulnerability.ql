/**
 * @name Custom Vulnerability Detection - Hardcoded Sensitive Values
 * @description Detects hardcoded sensitive values like "I am hacker's paradise"
 * @kind problem
 * @problem.severity error
 * @security-severity 8.0
 * @precision high
 * @id js/hardcoded-sensitive-value
 * @tags security
 *       external/cwe/cwe-798
 */

import javascript

/**
 * Predicate to identify suspicious string literals that contain sensitive patterns
 */
predicate isSensitiveString(StringLiteral str) {
  str.getValue().toLowerCase().matches("%hacker%paradise%") or
  str.getValue().toLowerCase().matches("%i am hacker%") or
  str.getValue() = "I am hacker's paradise"
}

/**
 * Predicate to check if a string is in a masking context (false positive)
 */
predicate isInMaskingContext(StringLiteral str) {
  // Check if the string contains "MASKED" or similar indicators
  str.getValue().matches("%MASKED%") or
  str.getValue().matches("%TEMPLATE%") or
  str.getValue().matches("%EXAMPLE%") or
  // Check if it's in a function with masking-related name
  exists(Function f |
    f.getName().toLowerCase().matches("%mask%") or
    f.getName().toLowerCase().matches("%sanitize%") or
    f.getName().toLowerCase().matches("%clean%")
  |
    str.getParent*() = f
  )
}

from StringLiteral str, string message
where
  isSensitiveString(str) and
  not isInMaskingContext(str) and
  (
    // Case 1: In variable declarations
    exists(VariableDeclarator decl |
      decl.getInit() = str and
      message = "Hardcoded sensitive value in variable declaration: '" + str.getValue() + "'"
    ) or
    // Case 2: In assignments
    exists(AssignExpr assign |
      assign.getRhs() = str and
      message = "Hardcoded sensitive value in assignment: '" + str.getValue() + "'"
    ) or
    // Case 3: In function calls (like console.log)
    exists(CallExpr call |
      call.getAnArgument() = str and
      message = "Sensitive value exposed in function call: '" + str.getValue() + "'"
    ) or
    // Case 4: Other contexts
    (not exists(VariableDeclarator decl | decl.getInit() = str) and
     not exists(AssignExpr assign | assign.getRhs() = str) and
     not exists(CallExpr call | call.getAnArgument() = str) and
     message = "Hardcoded sensitive value found: '" + str.getValue() + "'")
  )
select str, message
