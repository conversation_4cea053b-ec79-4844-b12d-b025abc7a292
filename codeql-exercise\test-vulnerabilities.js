/**
 * Test file to demonstrate various vulnerabilities and false positives
 */

// VULNERABILITY: Custom injected vulnerability
const VAL_VAR = "I am hacker's paradise";
const ANOTHER_VAL = "I am hacker's paradise";

// VULNERABILITY: More hardcoded sensitive data
let sensitiveData = "I am hacker's paradise";
var hackerString = "I AM HACKER'S PARADISE";

// FALSE POSITIVE: Legitimate masking function
function maskSensitiveInformation(data) {
    // This should NOT be flagged - it's a legitimate security function
    const MASK_VAR = "I am hacker's paradise - MASKED";
    return data.replace(/sensitive/gi, '***');
}

// FALSE POSITIVE: Configuration template with comments
const configTemplate = {
    // Template example: VAL_VAR="I am hacker's paradise"
    database: {
        host: 'localhost',
        port: 3306
    }
};

// VULNERABILITY: SQL Injection examples
function vulnerableSqlQuery(userInput) {
    const query = "SELECT * FROM users WHERE name = '" + userInput + "'";
    return query;
}

function anotherVulnerableQuery(id, name) {
    return `DELETE FROM users WHERE id = ${id} AND name = '${name}'`;
}

// VULNERABILITY: Command injection examples
function vulnerableCommand(filename) {
    const cmd = "cat /var/log/" + filename;
    require('child_process').exec(cmd);
}

function anotherVulnerableCommand(userInput) {
    const command = `ls -la ${userInput}`;
    return command;
}

// VULNERABILITY: Path traversal
function readUserFile(filename) {
    const fs = require('fs');
    const path = "./uploads/" + filename;
    return fs.readFileSync(path, 'utf8');
}

// VULNERABILITY: Weak cryptography
function weakHash(data) {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(data).digest('hex');
}

// VULNERABILITY: Information disclosure
function debugEndpoint() {
    return {
        secret: VAL_VAR,
        environment: process.env,
        config: {
            database_password: "admin123",
            api_key: "secret_key_123"
        }
    };
}

// FALSE POSITIVE: Secure implementation
function secureFunction() {
    // This is a secure implementation - should not be flagged
    const secureValue = "This is a legitimate security message";
    return secureValue;
}

// VULNERABILITY: Prototype pollution
function merge(target, source) {
    for (let key in source) {
        if (typeof source[key] === 'object') {
            if (!target[key]) target[key] = {};
            merge(target[key], source[key]);
        } else {
            target[key] = source[key];
        }
    }
    return target;
}

// VULNERABILITY: Insecure random
function generateToken() {
    return Math.random().toString(36).substring(2);
}

// Export for testing
module.exports = {
    vulnerableSqlQuery,
    vulnerableCommand,
    readUserFile,
    weakHash,
    debugEndpoint,
    merge,
    generateToken,
    maskSensitiveInformation,
    secureFunction
};
