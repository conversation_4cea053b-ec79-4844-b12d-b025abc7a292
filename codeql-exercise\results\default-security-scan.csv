"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable mysql.","/app.js","2","7","2","11"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable bcrypt.","/app.js","3","7","3","12"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable jwt.","/app.js","4","7","4","9"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused function maskSensitiveData.","/app.js","88","10","88","26"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable MASK_VAR.","/app.js","90","11","90","18"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable CONFIG_TEMPLATE.","/app.js","95","7","95","21"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable ANOTHER_VAL.","/test-vulnerabilities.js","7","7","7","17"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable sensitiveData.","/test-vulnerabilities.js","10","5","10","17"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable hackerString.","/test-vulnerabilities.js","11","5","11","16"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable MASK_VAR.","/test-vulnerabilities.js","16","11","16","18"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused variable configTemplate.","/test-vulnerabilities.js","21","7","21","20"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused function anotherVulnerableQuery.","/test-vulnerabilities.js","35","10","35","31"
"Unused variable, import, function or class","Unused variables, imports, functions or classes may be a symptom of a bug and should be examined carefully.","recommendation","Unused function anotherVulnerableCommand.","/test-vulnerabilities.js","45","10","45","33"
"Uncontrolled data used in path expression","Accessing paths influenced by users can allow an attacker to access unexpected resources.","error","This path depends on a [[""user-provided value""|""relative:///app.js:48:22:48:40""]].","/app.js","52","17","52","24"
"Uncontrolled command line","Using externally controlled strings in a command line may allow a malicious user to change the meaning of the command.","error","This command line depends on a [[""user-provided value""|""relative:///app.js:33:26:33:33""]].","/app.js","37","10","37","16"
"Code injection","Interpreting unsanitized user input as code allows a malicious user arbitrary code execution.","error","This code execution depends on a [[""user-provided value""|""relative:///app.js:133:22:133:29""]].","/app.js","137","29","137","39"
"Clear-text logging of sensitive information","Logging sensitive information without encryption or hashing can expose it to an attacker.","error","This logs sensitive data returned by [[""an access to password""|""relative:///app.js:24:89:24:96""]] as clear text.","/app.js","27","37","27","41"
"Missing rate limiting","An HTTP request handler that performs expensive operations without restricting the rate at which operations can be carried out is vulnerable to denial-of-service attacks.","warning","This route handler performs [[""a system command""|""relative:///app.js:37:5:43:6""]], but is not rate-limited.","/app.js","32","21","44","1"
"Missing rate limiting","An HTTP request handler that performs expensive operations without restricting the rate at which operations can be carried out is vulnerable to denial-of-service attacks.","warning","This route handler performs [[""a file system access""|""relative:///app.js:52:5:58:6""]], but is not rate-limited.","/app.js","47","28","59","1"
"Prototype-polluting function","Functions recursively assigning properties on objects may be the cause of accidental modification of a built-in prototype object.","warning","Properties are copied from [[""source""|""relative:///app.js:116:25:116:30""]] to [[""target""|""relative:///app.js:121:17:121:22""]] without guarding against prototype pollution.","/app.js","121","17","121","22"
"Log injection","Building log entries from user-controlled sources is vulnerable to insertion of forged log entries by a malicious user.","error","Log entry depends on a [[""user-provided value""|""relative:///app.js:21:36:21:43""]].","/app.js","27","37","27","41"
