/**
 * @name Vulnerability Detection by File
 * @description Shows which files contain our custom vulnerability pattern
 * @kind table
 * @id js/vulnerability-by-file
 */

import javascript

from StringLiteral str, File f
where str.getValue().toLowerCase().matches("%hacker%paradise%") and
      f = str.getFile() and
      f.getAbsolutePath().matches("%codeql-exercise%")
select f.getBaseName() as filename, 
       str.getLocation().getStartLine() as line_number,
       str.getValue() as sensitive_value
