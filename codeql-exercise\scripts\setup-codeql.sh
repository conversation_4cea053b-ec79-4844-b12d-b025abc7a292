#!/bin/bash

# CodeQL Setup and Scanning Script
# This script sets up CodeQL database and runs various scans

echo "=== CodeQL Vulnerability Detection Exercise ==="
echo "Setting up CodeQL database and running scans..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Step 1: Create CodeQL database
echo -e "${BLUE}Step 1: Creating CodeQL database...${NC}"
if [ -d "codeql-db" ]; then
    echo "Removing existing database..."
    rm -rf codeql-db
fi

codeql database create \
    --language=javascript \
    --source-root=. \
    --overwrite \
    codeql-db

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ CodeQL database created successfully${NC}"
else
    echo -e "${RED}✗ Failed to create CodeQL database${NC}"
    exit 1
fi

# Step 2: Run default JavaScript security queries
echo -e "${BLUE}Step 2: Running default security queries...${NC}"
codeql database analyze \
    codeql-db \
    javascript-security-and-quality.qls \
    --format=sarif-latest \
    --output=default-results.sarif

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Default security scan completed${NC}"
else
    echo -e "${YELLOW}⚠ Default security scan completed with warnings${NC}"
fi

# Step 3: Run custom vulnerability query
echo -e "${BLUE}Step 3: Running custom vulnerability detection query...${NC}"
codeql query run \
    queries/custom-vulnerability.ql \
    --database=codeql-db \
    --output=custom-results.bqrs

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Custom vulnerability query executed${NC}"
else
    echo -e "${RED}✗ Custom vulnerability query failed${NC}"
fi

# Step 4: Run SQL injection query
echo -e "${BLUE}Step 4: Running SQL injection detection query...${NC}"
codeql query run \
    queries/sql-injection.ql \
    --database=codeql-db \
    --output=sql-injection-results.bqrs

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ SQL injection query executed${NC}"
else
    echo -e "${RED}✗ SQL injection query failed${NC}"
fi

# Step 5: Run command injection query
echo -e "${BLUE}Step 5: Running command injection detection query...${NC}"
codeql query run \
    queries/command-injection.ql \
    --database=codeql-db \
    --output=command-injection-results.bqrs

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Command injection query executed${NC}"
else
    echo -e "${RED}✗ Command injection query failed${NC}"
fi

echo -e "${GREEN}=== CodeQL scanning completed! ===${NC}"
echo "Results files generated:"
echo "  - default-results.sarif (SARIF format)"
echo "  - custom-results.bqrs (Binary results)"
echo "  - sql-injection-results.bqrs"
echo "  - command-injection-results.bqrs"
echo ""
echo "Use the view-results.sh script to display results in human-readable format."
