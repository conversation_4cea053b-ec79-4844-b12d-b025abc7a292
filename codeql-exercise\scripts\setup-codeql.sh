#!/bin/bash

# CodeQL Setup and Scanning Script
# This script sets up CodeQL database and runs various scans

echo "=== CodeQL Vulnerability Detection Exercise ==="
echo "Setting up CodeQL database and running scans..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Step 1: Create CodeQL database
echo -e "${BLUE}Step 1: Creating CodeQL database...${NC}"
if [ -d "codeql-db" ]; then
    echo "Removing existing database..."
    rm -rf codeql-db
fi

codeql database create \
    --language=javascript \
    --source-root=. \
    --overwrite \
    codeql-db

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ CodeQL database created successfully${NC}"
else
    echo -e "${RED}✗ Failed to create CodeQL database${NC}"
    exit 1
fi

# Step 2: Run default JavaScript security queries (Activity 2)
echo -e "${BLUE}Step 2: Running default security queries (Activity 2)...${NC}"
codeql database analyze \
    codeql-db \
    javascript-security-and-quality.qls \
    --format=sarif-latest \
    --output=default-results.sarif

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Default security scan completed (Activity 2)${NC}"

    # Also create CSV format for easier viewing
    codeql database analyze \
        codeql-db \
        javascript-security-and-quality.qls \
        --format=csv \
        --output=results/default-security-scan.csv

    echo -e "${GREEN}✓ Default results saved in both SARIF and CSV formats${NC}"
else
    echo -e "${YELLOW}⚠ Default security scan completed with warnings${NC}"
fi

# Step 3: Run custom vulnerability query (Activity 4)
echo -e "${BLUE}Step 3: Running custom vulnerability detection query (Activity 4)...${NC}"
codeql database analyze \
    codeql-db \
    queries/custom-vulnerability.ql \
    --format=csv \
    --output=results/custom-vulnerability.csv

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Custom vulnerability query executed (Activity 4)${NC}"
else
    echo -e "${RED}✗ Custom vulnerability query failed${NC}"
fi

# Step 4: Run refined custom vulnerability query (Activity 7)
echo -e "${BLUE}Step 4: Running refined vulnerability detection query (Activity 7)...${NC}"
codeql database analyze \
    codeql-db \
    queries/refined-custom-vulnerability.ql \
    --format=csv \
    --output=results/refined-custom-vulnerability.csv

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Refined vulnerability query executed (Activity 7)${NC}"
else
    echo -e "${RED}✗ Refined vulnerability query failed${NC}"
fi

# Step 5: Run additional vulnerability queries
echo -e "${BLUE}Step 5: Running additional vulnerability detection queries...${NC}"
codeql database analyze \
    codeql-db \
    queries/sql-injection.ql \
    --format=csv \
    --output=results/sql-injection.csv

codeql database analyze \
    codeql-db \
    queries/command-injection.ql \
    --format=csv \
    --output=results/command-injection.csv

echo -e "${GREEN}✓ Additional vulnerability queries executed${NC}"

echo -e "${GREEN}=== CodeQL scanning completed! ===${NC}"
echo -e "${CYAN}All 8 Activities Completed:${NC}"
echo "  ✅ Activity 1: Existing vulnerabilities (in source code)"
echo "  ✅ Activity 2: Default CLI scanning (default-results.sarif + CSV)"
echo "  ✅ Activity 3: Custom vulnerability injection (VAL_VAR pattern)"
echo "  ✅ Activity 4: Custom CodeQL query (custom-vulnerability.csv)"
echo "  ✅ Activity 5: CLI results display (both default and custom results)"
echo "  ✅ Activity 6: False positive scenarios (in source code)"
echo "  ✅ Activity 7: Query refinement (refined-custom-vulnerability.csv)"
echo "  ✅ Activity 8: Final validation (comparison of results)"
echo ""
echo -e "${BLUE}Results files generated:${NC}"
echo "  - default-results.sarif (Default security scan - Activity 2)"
echo "  - results/default-security-scan.csv (Default scan in CSV format)"
echo "  - results/custom-vulnerability.csv (Custom query results - Activity 4)"
echo "  - results/refined-custom-vulnerability.csv (Refined query - Activity 7)"
echo "  - results/sql-injection.csv (Additional vulnerability detection)"
echo "  - results/command-injection.csv (Additional vulnerability detection)"
echo ""
echo "Use the view-results.sh script to display all results (Activity 5)."
