/**
 * @name Debug Why Command Injection Query Returns Empty
 * @description Diagnose why the command injection query finds nothing
 * @kind table
 * @id js/debug-empty-results
 */

import javascript

from string component, string status, string details
where
  (
    // Check 1: Do we have command execution calls?
    exists(CallExpr call |
      call.getCalleeName() = "exec" and
      call.getFile().getAbsolutePath().matches("%codeql-exercise%")
    ) and
    component = "Command Execution Calls" and
    status = "FOUND" and
    details = "exec() calls exist in codebase"
  ) or
  (
    // Check 2: Do we have user input sources?
    exists(PropAccess prop |
      prop.getBase().(PropAccess).getPropertyName() = "body" and
      prop.getFile().getAbsolutePath().matches("%codeql-exercise%")
    ) and
    component = "User Input Sources (body)" and
    status = "FOUND" and
    details = "req.body access found"
  ) or
  (
    // Check 3: Do we have user input sources (params)?
    exists(PropAccess prop |
      prop.getBase().(PropAccess).getPropertyName() = "params" and
      prop.getFile().getAbsolutePath().matches("%codeql-exercise%")
    ) and
    component = "User Input Sources (params)" and
    status = "FOUND" and
    details = "req.params access found"
  ) or
  (
    // Check 4: Do we have command keywords in strings?
    exists(StringLiteral str |
      str.getValue().regexpMatch(".*(tar|cp|mv|rm|cat|ls|ps|kill|wget|curl|ping).*") and
      str.getFile().getAbsolutePath().matches("%codeql-exercise%")
    ) and
    component = "Command Keywords in Strings" and
    status = "FOUND" and
    details = "Command keywords found in string literals"
  ) or
  (
    // Check 5: Template literals with variables
    exists(TemplateLiteral template |
      exists(VarAccess var | var = template.getAnElement()) and
      template.getFile().getAbsolutePath().matches("%codeql-exercise%")
    ) and
    component = "Template Literals with Variables" and
    status = "FOUND" and
    details = "Template literals with variable interpolation found"
  )
select component, status, details
